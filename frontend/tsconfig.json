{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "node"], "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "skipLibCheck": true}}