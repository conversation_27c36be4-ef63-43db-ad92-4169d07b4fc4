/**
 * 设备相关类型定义
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 14:30:51 +08:00; Reason: 设备管理模块开发, 创建前端设备类型定义; Principle_Applied: 类型安全;}}
 */

// 设备基本信息接口
export interface Device {
  id: number;
  enterpriseId: number;
  deviceModelId?: number;
  code?: string;
  sn?: string;
  mac?: string;
  ip?: string;
  name: string;
  remark?: string;
  controlModel?: string;
  vendor?: string;
  headSpace?: number;
  headNum?: number;
  headNeedleNum?: number;
  formularHeadSpace?: number;
  formularHeadNum?: number;
  formularLength?: number;
  displaySoftware?: string;
  controlSoftware?: string;
  productionLineId?: number;
  groupId?: number;
  registerWay?: string;
  wifiBitRate?: number;
  wifiFreq?: number;
  wifiIp?: string;
  wifiKeyMgmt?: string;
  wifiMac?: string;
  wifiSsid?: string;
  wifiState?: string;
  wifiLinkQuality?: string;
  wifiSignalLevel?: string;
  gatewayMac?: string;
  createdAt: string;
  updatedAt: string;
  // 关联信息
  deviceModelInfo?: {
    id: number;
    code: string;
    name: string;
    parameter?: string;
  };
  productionLine?: {
    id: number;
    name: string;
  };
  group?: {
    id: number;
    name: string;
  };
}

// 设备列表查询参数
export interface DeviceListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  deviceModelId?: number;
  productionLineId?: number;
  groupId?: number;
  vendor?: string;
  wifiState?: string;
}

// 设备列表响应
export interface DeviceListResponse {
  devices: Device[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 树形节点接口
export interface TreeNode {
  id: number;
  name: string;
  value: number;
  title: string;
  key: string;
  children?: TreeNode[];
}

// 设备搜索选项
export interface DeviceSearchOptions {
  deviceModels: Array<{ id: number; name: string }>;
  productionLines: TreeNode[];
  groups: Array<{ id: number; name: string }>;
}

// 创建设备请求
export interface CreateDeviceRequest {
  name: string;
  deviceModelId?: number;
  code?: string;
  sn?: string;
  mac?: string;
  ip?: string;
  remark?: string;
  controlModel?: string;
  vendor?: string;
  headSpace?: number;
  headNum?: number;
  headNeedleNum?: number;
  formularHeadSpace?: number;
  formularHeadNum?: number;
  formularLength?: number;
  displaySoftware?: string;
  controlSoftware?: string;
  productionLineId?: number;
  groupId?: number;
  registerWay?: string;
}

// 更新设备请求
export interface UpdateDeviceRequest {
  name?: string;
  deviceModelId?: number;
  code?: string;
  sn?: string;
  mac?: string;
  ip?: string;
  remark?: string;
  controlModel?: string;
  vendor?: string;
  headSpace?: number;
  headNum?: number;
  headNeedleNum?: number;
  formularHeadSpace?: number;
  formularHeadNum?: number;
  formularLength?: number;
  displaySoftware?: string;
  controlSoftware?: string;
  productionLineId?: number;
  groupId?: number;
  registerWay?: string;
}

// WiFi配置更新请求
export interface UpdateWifiConfigRequest {
  wifiBitRate?: number;
  wifiFreq?: number;
  wifiIp?: string;
  wifiKeyMgmt?: string;
  wifiMac?: string;
  wifiSsid?: string;
  wifiState?: string;
  wifiLinkQuality?: string;
  wifiSignalLevel?: string;
  gatewayMac?: string;
}

// 设备统计信息
export interface DeviceStats {
  total: number;
  online: number;
  offline: number;
  byModel: Array<{ model: string; count: number }>;
  byVendor: Array<{ vendor: string; count: number }>;
}

// WiFi状态枚举
export enum WiFiState {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  ERROR = 'error',
  UNKNOWN = 'unknown'
}

// 设备状态枚举
export enum DeviceStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  MAINTENANCE = 'maintenance',
  ERROR = 'error'
}

// 注册方式枚举
export enum RegisterWay {
  MANUAL = 'manual',
  AUTO = 'auto',
  SCAN = 'scan',
  IMPORT = 'import'
}

// WiFi加密方式枚举
export enum WiFiKeyMgmt {
  NONE = 'none',
  WEP = 'wep',
  WPA = 'wpa',
  WPA2 = 'wpa2',
  WPA3 = 'wpa3'
}

// 设备表单字段配置
export interface DeviceFormField {
  key: keyof Device;
  label: string;
  type: 'input' | 'number' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: string | number }>;
  rules?: any[];
}

// 设备表格列配置
export interface DeviceTableColumn {
  key: keyof Device | 'actions';
  title: string;
  dataIndex?: keyof Device;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  filters?: Array<{ text: string; value: string }>;
  render?: (value: any, record: Device) => any;
}

// 设备筛选选项
export interface DeviceFilterOptions {
  deviceModels: Array<{ label: string; value: string }>;
  vendors: Array<{ label: string; value: string }>;
  wifiStates: Array<{ label: string; value: string }>;
  registerWays: Array<{ label: string; value: string }>;
}

// WiFi信号强度等级
export interface WiFiSignalLevel {
  level: number;
  label: string;
  color: string;
  icon: string;
}

// 设备操作权限
export interface DevicePermissions {
  view: boolean;
  create: boolean;
  update: boolean;
  delete: boolean;
  updateWifi: boolean;
  viewStats: boolean;
}

// 设备导入/导出相关
export interface DeviceImportData {
  name: string;
  code?: string;
  sn?: string;
  mac?: string;
  ip?: string;
  deviceModel?: string;
  vendor?: string;
  remark?: string;
}

export interface DeviceExportData extends Device {
  enterpriseName?: string;
  statusText?: string;
  wifiStateText?: string;
}

// 设备批量操作
export interface DeviceBatchOperation {
  type: 'delete' | 'updateWifi' | 'export';
  deviceIds: number[];
  data?: any;
}

// 设备搜索建议
export interface DeviceSearchSuggestion {
  type: 'name' | 'code' | 'sn' | 'model' | 'vendor';
  value: string;
  label: string;
  count?: number;
}

// 设备监控数据
export interface DeviceMonitorData {
  deviceId: number;
  timestamp: string;
  status: DeviceStatus;
  wifiState: WiFiState;
  wifiSignalLevel?: string;
  wifiLinkQuality?: string;
  cpuUsage?: number;
  memoryUsage?: number;
  temperature?: number;
}

// 设备告警信息
export interface DeviceAlert {
  id: number;
  deviceId: number;
  deviceName: string;
  type: 'offline' | 'wifi_disconnected' | 'high_temperature' | 'error';
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: string;
  resolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
}

// 设备配置模板
export interface DeviceConfigTemplate {
  id: number;
  name: string;
  description?: string;
  deviceModel?: string;
  vendor?: string;
  config: {
    controlModel?: string;
    headSpace?: number;
    headNum?: number;
    headNeedleNum?: number;
    displaySoftware?: string;
    controlSoftware?: string;
    wifiConfig?: {
      keyMgmt?: string;
      freq?: number;
    };
  };
  createdAt: string;
  updatedAt: string;
}
