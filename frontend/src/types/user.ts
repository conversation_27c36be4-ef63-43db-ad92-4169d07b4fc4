/**
 * 用户管理相关类型定义
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 20:53:00 +08:00; Reason: Task-006 用户管理模块, 创建类型定义; Principle_Applied: TypeScript类型安全;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 11:30:19 +08:00; Reason: Task-002 微信绑定功能, 添加微信相关字段类型; Principle_Applied: 前后端类型一致性;}}
 */

// 用户信息接口
export interface User {
  id: number
  username: string
  email?: string
  phone?: string
  realName: string
  avatarUrl?: string
  status: number // 1:正常 0:禁用
  lastLoginAt?: string
  // 微信绑定相关字段
  wechatOpenid?: string // 微信OpenID
  wechatUnionid?: string // 微信UnionID
  wechatAvatar?: string // 微信头像URL
  wechatNickname?: string // 微信昵称
  wechatBoundAt?: string // 微信绑定时间
  createdAt: string
  updatedAt: string
  enterprise?: {
    id: number
    name: string
    code: string
  }
  department?: {
    id: number
    name: string
  }
  roles?: {
    id: number
    name: string
    code: string
  }[]
}

// 用户列表查询参数
export interface UserListQuery {
  page?: number
  pageSize?: number
  keyword?: string
  departmentId?: number
  status?: number
  enterpriseId?: number
}

// 用户列表响应
export interface UserListResponse {
  users: User[]
  total: number
  page: number
  pageSize: number
}

// 创建用户请求
export interface CreateUserRequest {
  username: string
  email?: string
  phone?: string
  password: string
  realName: string
  departmentId?: number
  roleIds?: number[]
  status?: number
}

// 更新用户请求
export interface UpdateUserRequest {
  email?: string
  phone?: string
  realName?: string
  departmentId?: number
  roleIds?: number[]
  status?: number
}

// 重置密码请求
export interface ResetPasswordRequest {
  newPassword: string
}

// 创建部门请求
export interface CreateDepartmentRequest {
  name: string
  parentId?: number
  description?: string
  sortOrder?: number
}

// 更新部门请求
export interface UpdateDepartmentRequest {
  name?: string
  parentId?: number
  description?: string
  sortOrder?: number
}

// 部门树节点
export interface DepartmentTreeNode {
  id: number
  name: string
  enterpriseId: number
  parentId?: number
  level: number
  path: string
  sortOrder: number
  description?: string
  userCount: number
  children?: DepartmentTreeNode[]
}

// 企业信息
export interface EnterpriseInfo {
  id: number
  name: string
  code: string
  logoUrl?: string
  description?: string
  status: number
  createdAt: string
  updatedAt: string
  statistics: {
    userCount: number
    departmentCount: number
  }
}

// 角色信息
export interface Role {
  id: number
  name: string
  code: string
  description?: string
  isSystem: boolean
  enterpriseId?: number
}

// 用户状态枚举
export enum UserStatus {
  DISABLED = 0,
  ACTIVE = 1
}

// 用户状态选项
export const USER_STATUS_OPTIONS = [
  { label: '正常', value: UserStatus.ACTIVE, color: 'green' },
  { label: '禁用', value: UserStatus.DISABLED, color: 'red' }
]

// 表格列配置
export interface UserTableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number
  fixed?: 'left' | 'right'
  sorter?: boolean
  filters?: Array<{ text: string; value: any }>
  ellipsis?: boolean
}

// 用户表单数据
export interface UserFormData {
  username?: string
  email?: string
  phone?: string
  password?: string
  realName?: string
  departmentId?: number
  roleIds?: number[]
  status?: number
}
