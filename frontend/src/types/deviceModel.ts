/**
 * 设备类型相关类型定义
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建前端类型定义; Principle_Applied: 类型安全;}}
 */

// 设备类型基础信息
export interface DeviceModel {
  id: number;
  enterpriseId: number;
  code: string;
  name: string;
  parameter?: string;
  createdAt: string;
  updatedAt: string;
  // 关联信息
  devices?: Array<{
    id: number;
    name: string;
    code?: string;
  }>;
}

// 设备类型列表查询参数
export interface DeviceModelListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
}

// 设备类型列表响应
export interface DeviceModelListResponse {
  deviceModels: DeviceModel[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 创建设备类型请求
export interface CreateDeviceModelRequest {
  code: string;
  name: string;
  parameter?: string;
}

// 更新设备类型请求
export interface UpdateDeviceModelRequest {
  code?: string;
  name?: string;
  parameter?: string;
}

// 设备类型选项
export interface DeviceModelOption {
  id: number;
  code: string;
  name: string;
}

// 设备类型表单数据
export interface DeviceModelFormData {
  code: string;
  name: string;
  parameter?: string;
}

// 设备类型搜索表单
export interface DeviceModelSearchForm {
  search?: string;
}
