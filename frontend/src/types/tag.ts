/**
 * 标签相关类型定义
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 标签管理模块开发, 创建前端标签类型定义; Principle_Applied: 类型安全;}}
 */

// 标签类型枚举
export enum TagType {
  WORKSHOP_LINE = 1,    // 车间产线 - 支持多级层级
  MACHINE_GROUP = 2,    // 机器分组 - 同级
  PATTERN_GROUP = 3,    // 花样分组 - 同级
  PATTERN_UNIT = 4      // 花样计量单位 - 同级
}

// 标签状态枚举
export enum TagStatus {
  DISABLED = 0,         // 禁用
  ENABLED = 1           // 正常
}

// 标签基本信息接口
export interface Tag {
  id: number;
  enterpriseId: number;
  pid: number | null;
  level: number;
  name: string;
  path: string;
  status: TagStatus;
  type: TagType;
  createdAt: string;
  updatedAt: string;
  // 关联数据
  parent?: {
    id: number;
    name: string;
  };
  children?: Tag[];
  // 扩展字段（用于表格展示）
  parentName?: string;
}

// 标签树节点接口
export interface TagTreeNode extends Tag {
  children?: TagTreeNode[];
  key?: string | number;
  title?: string;
  disabled?: boolean;
  selectable?: boolean;
}

// 标签列表查询参数
export interface TagListQuery {
  page?: number;
  pageSize?: number;
  type?: TagType;
  status?: TagStatus;
  search?: string;
  pid?: number;
}

// 标签列表响应
export interface TagListResponse {
  tags: Tag[];
  total: number;
  page: number;
  pageSize: number;
}

// 创建标签请求
export interface CreateTagRequest {
  name: string;
  type: TagType;
  pid?: number;
  status?: TagStatus;
}

// 更新标签请求
export interface UpdateTagRequest {
  name?: string;
  status?: TagStatus;
  pid?: number;
}

// 标签类型选项
export interface TagTypeOption {
  value: TagType;
  label: string;
  supportHierarchy: boolean;
}

// 标签状态选项
export interface TagStatusOption {
  value: TagStatus;
  label: string;
  color: string;
}

// 标签表格列配置
export interface TagTableColumn {
  key: keyof Tag | 'actions';
  title: string;
  dataIndex?: keyof Tag;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  filters?: Array<{ text: string; value: string | number }>;
  render?: (value: any, record: Tag) => any;
}

// 标签表单字段配置
export interface TagFormField {
  key: keyof CreateTagRequest | keyof UpdateTagRequest;
  label: string;
  type: 'input' | 'select' | 'tree-select';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: string | number }>;
  rules?: any[];
  dependencies?: string[];
}

// 标签筛选选项
export interface TagFilterOptions {
  types: TagTypeOption[];
  statuses: TagStatusOption[];
  parents: Array<{ label: string; value: number }>;
}

// 标签操作类型
export type TagAction = 'create' | 'update' | 'delete' | 'enable' | 'disable' | 'move';

// 标签批量操作请求
export interface BatchTagRequest {
  action: TagAction;
  ids: number[];
  data?: any;
}

// 标签移动请求
export interface MoveTagRequest {
  targetId: number;
  position: 'before' | 'after' | 'inside';
}

// 标签搜索结果
export interface TagSearchResult {
  tag: Tag;
  ancestors: Tag[];
  matches: string[];
}

// 标签统计信息
export interface TagStats {
  total: number;
  byType: Array<{
    type: TagType;
    typeName: string;
    count: number;
  }>;
  byStatus: Array<{
    status: TagStatus;
    statusName: string;
    count: number;
  }>;
  hierarchyDepth: {
    maxLevel: number;
    avgLevel: number;
  };
}

// 标签验证规则
export interface TagValidationRules {
  name: {
    required: boolean;
    maxLength: number;
    pattern?: RegExp;
  };
  type: {
    required: boolean;
    allowedValues: TagType[];
  };
  pid: {
    required: boolean;
    dependsOnType: TagType[];
  };
}

// 标签导入/导出格式
export interface TagImportData {
  name: string;
  type: TagType;
  parentName?: string;
  status?: TagStatus;
  remark?: string;
}

export interface TagExportData extends Tag {
  typeName: string;
  statusName: string;
  parentName?: string;
  fullPath: string;
}

// 标签权限配置
export interface TagPermissions {
  canView: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
  canManageHierarchy: boolean;
  allowedTypes: TagType[];
}

// 标签配置选项
export interface TagConfig {
  maxHierarchyLevel: number;
  allowDuplicateNames: boolean;
  enableSoftDelete: boolean;
  defaultStatus: TagStatus;
  typeConfigs: Record<TagType, {
    supportHierarchy: boolean;
    maxLevel?: number;
    namePattern?: RegExp;
    requiredFields?: string[];
  }>;
}
