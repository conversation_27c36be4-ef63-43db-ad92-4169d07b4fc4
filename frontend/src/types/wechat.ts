/**
 * 微信绑定相关类型定义
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 11:30:19 +08:00; Reason: Task-002 微信绑定功能, 创建微信专用类型定义; Principle_Applied: 类型安全和模块化;}}
 */

// 微信绑定状态枚举
export enum WechatBindStatus {
  UNBOUND = 'unbound', // 未绑定
  BINDING = 'binding', // 绑定中
  BOUND = 'bound', // 已绑定
  UNBINDING = 'unbinding', // 解绑中
  ERROR = 'error' // 绑定错误
}

// 微信用户信息
export interface WechatUserInfo {
  openid: string // 微信OpenID
  unionid?: string // 微信UnionID
  nickname: string // 微信昵称
  headimgurl: string // 微信头像URL
  sex: number // 性别：1男性，2女性，0未知
  province?: string // 省份
  city?: string // 城市
  country?: string // 国家
}

// 微信授权URL响应
export interface WechatAuthUrlResponse {
  authUrl: string // 微信授权URL
  state: string // 状态参数，用于防CSRF攻击
}

// 微信绑定请求
export interface WechatBindRequest {
  code: string // 微信授权码
  state: string // 状态参数
}

// 微信绑定响应
export interface WechatBindResponse {
  success: boolean
  message: string
  userInfo?: {
    wechatOpenid: string
    wechatUnionid?: string
    wechatNickname: string
    wechatAvatar: string
    wechatBoundAt: string
  }
}

// 微信解绑请求
export interface WechatUnbindRequest {
  // 解绑通常不需要额外参数，使用当前用户身份
}

// 微信解绑响应
export interface WechatUnbindResponse {
  success: boolean
  message: string
}

// 微信授权回调参数
export interface WechatCallbackParams {
  code?: string // 授权码
  state?: string // 状态参数
  error?: string // 错误信息
  error_description?: string // 错误描述
}

// 微信绑定组件Props
export interface WechatBindCardProps {
  loading?: boolean // 加载状态
  disabled?: boolean // 禁用状态
}

// 微信绑定组件事件
export interface WechatBindCardEmits {
  bind: () => void // 绑定事件
  unbind: () => void // 解绑事件
  refresh: () => void // 刷新事件
}

// 微信绑定状态信息
export interface WechatBindInfo {
  status: WechatBindStatus
  openid?: string
  unionid?: string
  nickname?: string
  avatar?: string
  boundAt?: string
  error?: string
}

// 微信API错误响应
export interface WechatApiError {
  errcode: number
  errmsg: string
}

// 微信绑定配置
export interface WechatConfig {
  appId: string // 微信应用ID
  redirectUri: string // 重定向URI
  scope: string // 授权作用域
  responseType: string // 响应类型
}

// 微信绑定状态选项
export const WECHAT_BIND_STATUS_OPTIONS = [
  { label: '未绑定', value: WechatBindStatus.UNBOUND, color: 'default' },
  { label: '绑定中', value: WechatBindStatus.BINDING, color: 'processing' },
  { label: '已绑定', value: WechatBindStatus.BOUND, color: 'success' },
  { label: '解绑中', value: WechatBindStatus.UNBINDING, color: 'warning' },
  { label: '绑定错误', value: WechatBindStatus.ERROR, color: 'error' }
]

// 微信绑定操作类型
export enum WechatBindAction {
  GET_AUTH_URL = 'getAuthUrl', // 获取授权URL
  BIND = 'bind', // 绑定微信
  UNBIND = 'unbind', // 解绑微信
  REFRESH_STATUS = 'refreshStatus' // 刷新状态
}

// 微信绑定操作结果
export interface WechatBindActionResult {
  action: WechatBindAction
  success: boolean
  message: string
  data?: any
}
