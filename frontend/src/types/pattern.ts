// 重新导出花样相关类型
export type {
  Pattern,
  PatternListQuery,
  PatternListResponse,
  PatternSearchOptions,
  PatternStats,
  CreatePatternRequest,
  UpdatePatternRequest
} from '@/api/pattern'

// 花样表格列配置
export interface PatternTableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sorter?: boolean
  fixed?: 'left' | 'right'
  ellipsis?: boolean
}

// 花样表单字段
export interface PatternFormFields {
  code?: string
  name: string
  remark?: string
  groupId?: number
  fileType?: string
  stitch?: number
  jumps?: number
  trim?: number
  colors?: number
  baseLine?: number
  surfaceLine?: number
  goldPieceNum?: number
  minX?: number
  maxX?: number
  minY?: number
  maxY?: number
  width?: number
  height?: number
  goldPieceLine?: string
  needle?: string
  mergeGoldPieceLine?: string
  image?: string
  createUser?: string
}

// 花样筛选表单
export interface PatternFilterForm {
  search?: string
  groupId?: number
  fileType?: string
  createUser?: string
}

// 花样操作类型
export type PatternAction = 'create' | 'edit' | 'view' | 'delete'

// 花样模态框状态
export interface PatternModalState {
  visible: boolean
  action: PatternAction
  pattern?: Pattern | null
  loading: boolean
}

// 花样文件类型选项
export const PATTERN_FILE_TYPES = [
  { label: 'DST', value: 'dst' },
  { label: 'PES', value: 'pes' },
  { label: 'JEF', value: 'jef' },
  { label: 'EXP', value: 'exp' },
  { label: 'VP3', value: 'vp3' },
  { label: 'HUS', value: 'hus' },
  { label: 'VIP', value: 'vip' },
  { label: 'XXX', value: 'xxx' },
  { label: 'PEC', value: 'pec' },
  { label: 'SEW', value: 'sew' }
] as const

// 花样统计卡片配置
export interface PatternStatsCard {
  title: string
  value: number | string
  icon: string
  color: string
  trend?: {
    value: number
    type: 'up' | 'down'
  }
}
