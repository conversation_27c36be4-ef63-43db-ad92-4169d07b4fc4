<!--
  企业管理页面
  {{CHENGQI: Action: Modified; Timestamp: 2025-06-29 14:30:00 +08:00; Reason: Task-006 企业管理模块, 实现企业管理功能; Principle_Applied: 企业信息管理;}}
-->

<template>
  <div class="enterprise-management">
    <!-- 搜索区域 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline" :model="searchForm" @submit="handleSearch">
        <a-form-item label="关键词">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="企业名称/编码"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option :value="1">正常</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 企业列表 -->
    <a-card title="企业列表" :bordered="false">
      <template #extra>
        <a-button type="primary" :disabled="!canCreate" @click="showCreateModal">
          <PlusOutlined />
          新增企业
        </a-button>
      </template>

      <a-table
        :columns="columns"
        :data-source="enterpriseList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'logo'">
            <div class="logo-cell">
              <img
                v-if="record.logoUrl"
                :src="record.logoUrl"
                :alt="record.name"
                class="enterprise-logo"
              />
              <div v-else class="logo-placeholder">
                <BankOutlined />
              </div>
            </div>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </template>

          <template v-if="column.key === 'statistics'">
            <div class="statistics-cell">
              <div>用户: {{ record.statistics?.userCount || 0 }}</div>
              <div>部门: {{ record.statistics?.departmentCount || 0 }}</div>
            </div>
          </template>

          <template v-if="column.key === 'admin'">
            <div class="admin-cell">
              <div v-if="record.admin">
                <div class="admin-username">{{ record.admin.username }}</div>
                <div class="admin-name">{{ record.admin.realName }}</div>
              </div>
              <div v-else class="no-admin">未设置管理员</div>
            </div>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                size="small"
                :disabled="!canUpdate"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                :disabled="!canUpdate || !record.admin"
                @click="showResetPasswordModal(record)"
              >
                重置密码
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                :disabled="!canDelete"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑企业模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑企业' : '新增企业'"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="企业名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入企业名称" />
        </a-form-item>

        <a-form-item label="企业编码" name="code">
          <a-input
            v-model:value="formData.code"
            placeholder="请输入企业编码（大写字母、数字、下划线）"
          />
        </a-form-item>

        <a-form-item label="企业Logo" name="logoUrl">
          <a-input v-model:value="formData.logoUrl" placeholder="请输入Logo URL" />
        </a-form-item>

        <a-form-item label="企业描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入企业描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">正常</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 管理员账号信息 - 仅创建时显示 -->
        <template v-if="!isEdit">
          <a-divider>管理员账号信息</a-divider>

          <a-form-item label="管理员用户名" name="adminUsername">
            <a-input
              v-model:value="formData.adminUsername"
              placeholder="请输入管理员用户名（字母、数字、下划线）"
            />
          </a-form-item>

          <a-form-item label="管理员密码" name="adminPassword">
            <a-input-password
              v-model:value="formData.adminPassword"
              placeholder="请输入管理员密码（至少6位）"
            />
          </a-form-item>

          <a-form-item label="管理员姓名" name="adminRealName">
            <a-input
              v-model:value="formData.adminRealName"
              placeholder="请输入管理员真实姓名"
            />
          </a-form-item>

          <a-form-item label="管理员邮箱" name="adminEmail">
            <a-input
              v-model:value="formData.adminEmail"
              placeholder="请输入管理员邮箱（可选）"
            />
          </a-form-item>

          <a-form-item label="管理员手机" name="adminPhone">
            <a-input
              v-model:value="formData.adminPhone"
              placeholder="请输入管理员手机号（可选）"
            />
          </a-form-item>
        </template>
      </a-form>
    </a-modal>

    <!-- 重置密码模态框 -->
    <a-modal
      v-model:open="resetPasswordModalVisible"
      title="重置企业管理员密码"
      :confirm-loading="resetPasswordLoading"
      @ok="handleResetPassword"
      @cancel="handleResetPasswordCancel"
    >
      <a-form
        ref="resetPasswordFormRef"
        :model="resetPasswordForm"
        :rules="resetPasswordRules"
        layout="vertical"
      >
        <a-form-item label="企业名称">
          <a-input :value="currentEnterprise?.name" disabled />
        </a-form-item>

        <a-form-item label="管理员账号">
          <a-input :value="currentEnterprise?.admin?.username" disabled />
        </a-form-item>

        <a-form-item label="管理员姓名">
          <a-input :value="currentEnterprise?.admin?.realName" disabled />
        </a-form-item>

        <a-form-item label="新密码" name="newPassword">
          <a-input-password
            v-model:value="resetPasswordForm.newPassword"
            placeholder="请输入新密码（6-50个字符）"
          />
        </a-form-item>

        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password
            v-model:value="resetPasswordForm.confirmPassword"
            placeholder="请再次输入新密码"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  BankOutlined
} from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { enterpriseApi } from '@/api/enterprise'
import type {
  Enterprise,
  EnterpriseListQuery,
  CreateEnterpriseRequest,
  UpdateEnterpriseRequest
} from '@/api/enterprise'

// 用户权限
const userStore = useUserStore()
const canCreate = computed(() => userStore.hasPermission('enterprise:create'))
const canUpdate = computed(() => userStore.hasPermission('enterprise:update'))
const canDelete = computed(() => userStore.hasPermission('enterprise:delete'))

// 搜索表单
const searchForm = reactive<EnterpriseListQuery>({
  keyword: '',
  status: undefined
})

// 企业列表
const enterpriseList = ref<Enterprise[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列定义
const columns = [
  {
    title: 'Logo',
    key: 'logo',
    width: 80,
    align: 'center'
  },
  {
    title: '企业名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '企业编码',
    dataIndex: 'code',
    key: 'code'
  },
  {
    title: '管理员账号',
    key: 'admin',
    width: 150,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    align: 'center'
  },
  {
    title: '统计信息',
    key: 'statistics',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    align: 'center'
  }
]

// 模态框相关
const modalVisible = ref(false)
const modalLoading = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 重置密码模态框相关
const resetPasswordModalVisible = ref(false)
const resetPasswordLoading = ref(false)
const resetPasswordFormRef = ref()
const currentEnterprise = ref<Enterprise | null>(null)

// 表单数据
const formData = reactive<CreateEnterpriseRequest & { id?: number }>({
  name: '',
  code: '',
  logoUrl: '',
  description: '',
  status: 1,
  // 管理员账号信息
  adminUsername: '',
  adminPassword: '',
  adminRealName: '',
  adminEmail: '',
  adminPhone: ''
})

// 重置密码表单数据
const resetPasswordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 重置密码验证规则
const resetPasswordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度必须在6-50个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string) => {
        if (value && value !== resetPasswordForm.newPassword) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
    { min: 2, max: 100, message: '企业名称长度必须在2-100个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入企业编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '企业编码只能包含大写字母、数字和下划线', trigger: 'blur' },
    { min: 2, max: 50, message: '企业编码长度必须在2-50个字符之间', trigger: 'blur' }
  ],
  logoUrl: [
    { type: 'url', message: 'Logo URL格式不正确', trigger: 'blur' }
  ],
  // 管理员账号信息验证（仅创建时需要）
  adminUsername: [
    { required: true, message: '请输入管理员用户名', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '管理员用户名只能包含字母、数字和下划线', trigger: 'blur' },
    { min: 3, max: 50, message: '管理员用户名长度必须在3-50个字符之间', trigger: 'blur' }
  ],
  adminPassword: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' },
    { min: 6, max: 50, message: '管理员密码长度必须在6-50个字符之间', trigger: 'blur' }
  ],
  adminRealName: [
    { required: true, message: '请输入管理员真实姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '管理员真实姓名长度必须在2-50个字符之间', trigger: 'blur' }
  ],
  adminEmail: [
    { type: 'email', message: '管理员邮箱格式不正确', trigger: 'blur' }
  ],
  adminPhone: [
    { pattern: /^1[3-9]\d{9}$/, message: '管理员手机号格式不正确', trigger: 'blur' }
  ]
}

// 加载企业列表
const loadEnterpriseList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status
    }

    const response = await enterpriseApi.getEnterpriseList(params)
    enterpriseList.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('加载企业列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadEnterpriseList()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = undefined
  pagination.current = 1
  loadEnterpriseList()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadEnterpriseList()
}

// 显示创建模态框
const showCreateModal = () => {
  isEdit.value = false
  resetFormData()
  modalVisible.value = true
}

// 显示编辑模态框
const showEditModal = (enterprise: Enterprise) => {
  isEdit.value = true
  formData.id = enterprise.id
  formData.name = enterprise.name
  formData.code = enterprise.code
  formData.logoUrl = enterprise.logoUrl || ''
  formData.description = enterprise.description || ''
  formData.status = enterprise.status
  modalVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  formData.id = undefined
  formData.name = ''
  formData.code = ''
  formData.logoUrl = ''
  formData.description = ''
  formData.status = 1
  // 重置管理员账号信息
  formData.adminUsername = ''
  formData.adminPassword = ''
  formData.adminRealName = ''
  formData.adminEmail = ''
  formData.adminPhone = ''
}

// 模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true

    if (isEdit.value) {
      // 更新企业
      const updateData: UpdateEnterpriseRequest = {
        name: formData.name,
        logoUrl: formData.logoUrl || undefined,
        description: formData.description || undefined,
        status: formData.status
      }
      // 只有超级管理员才能修改企业编码
      if (userStore.hasRole('SUPER_ADMIN')) {
        updateData.code = formData.code
      }

      await enterpriseApi.updateEnterprise(formData.id!, updateData)
      message.success('更新企业成功')
    } else {
      // 创建企业
      const createData: CreateEnterpriseRequest = {
        name: formData.name,
        code: formData.code,
        logoUrl: formData.logoUrl || undefined,
        description: formData.description || undefined,
        status: formData.status,
        // 管理员账号信息
        adminUsername: formData.adminUsername,
        adminPassword: formData.adminPassword,
        adminRealName: formData.adminRealName,
        adminEmail: formData.adminEmail || undefined,
        adminPhone: formData.adminPhone || undefined
      }

      await enterpriseApi.createEnterprise(createData)
      message.success('创建企业成功')
    }

    modalVisible.value = false
    loadEnterpriseList()
  } catch (error) {
    message.error(isEdit.value ? '更新企业失败' : '创建企业失败')
  } finally {
    modalLoading.value = false
  }
}

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

// 删除企业
const handleDelete = (enterprise: Enterprise) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除企业"${enterprise.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await enterpriseApi.deleteEnterprise(enterprise.id)
        message.success('删除企业成功')
        loadEnterpriseList()
      } catch (error) {
        message.error('删除企业失败')
      }
    }
  })
}

// 显示重置密码模态框
const showResetPasswordModal = (enterprise: any) => {
  if (!enterprise.admin) {
    message.warning('该企业暂无管理员账号')
    return
  }
  currentEnterprise.value = enterprise
  resetPasswordForm.newPassword = ''
  resetPasswordForm.confirmPassword = ''
  resetPasswordModalVisible.value = true
}

// 重置密码
const handleResetPassword = async () => {
  try {
    await resetPasswordFormRef.value?.validate()
    resetPasswordLoading.value = true

    await enterpriseApi.resetAdminPassword(
      currentEnterprise.value!.id,
      resetPasswordForm.newPassword
    )

    message.success('重置企业管理员密码成功')
    resetPasswordModalVisible.value = false
  } catch (error: any) {
    if (error.errorFields) {
      // 表单验证错误，不显示错误消息
      return
    }
    message.error('重置密码失败')
  } finally {
    resetPasswordLoading.value = false
  }
}

// 重置密码模态框取消
const handleResetPasswordCancel = () => {
  resetPasswordModalVisible.value = false
  resetPasswordFormRef.value?.resetFields()
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 页面加载时获取数据
onMounted(() => {
  loadEnterpriseList()
})
</script>

<style scoped>
.enterprise-management {
  padding: 24px;
}

.search-card {
  margin-bottom: 16px;
}

.logo-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.enterprise-logo {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.logo-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 16px;
}

.statistics-cell {
  font-size: 12px;
  line-height: 1.4;
}

.statistics-cell > div {
  margin-bottom: 2px;
}

.statistics-cell > div:last-child {
  margin-bottom: 0;
}

.admin-cell {
  text-align: center;
}

.admin-username {
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 2px;
}

.admin-name {
  font-size: 12px;
  color: #666;
}

.no-admin {
  color: #999;
  font-style: italic;
}
</style>
