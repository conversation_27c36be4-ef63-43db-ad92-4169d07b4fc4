<!--
  用户管理页面 - 重构版本
  {{CHENGQI: Action: Modified; Timestamp: 2025-01-04 15:50:00 +08:00; Reason: 重构UserManagementView.vue，拆分为多个小组件; Principle_Applied: 单一职责原则、组件化设计;}}
-->

<template>
  <div class="user-management">
    <!-- Header: 企业信息 -->
    <div class="header">
      <EnterpriseInfoCard
        :enterprise-info="enterpriseInfo"
        :can-update="canUpdateEnterprise"
        @edit="showEnterpriseEditModal"
      />
    </div>

    <!-- Content: Tab切换布局 -->
    <div class="content">
      <!-- Tab切换 -->
      <a-tabs v-model:activeKey="activeTab" type="card" class="management-tabs">
        <a-tab-pane key="organization" tab="组织管理">
          <!-- 组织管理内容：左右布局 -->
          <div class="organization-content">
            <!-- Left: 部门树 -->
            <div class="left">
              <DepartmentTree
                :department-tree="departmentTree"
                :loading="departmentLoading"
                :selected-keys="selectedDepartmentKeys"
                :expanded-keys="expandedDepartmentKeys"
                :can-create="canCreateDepartment"
                :can-update="canUpdateDepartment"
                :can-delete="canDeleteDepartment"
                @select="onDepartmentSelect"
                @expand="onDepartmentExpand"
                @create="showCreateDepartmentModal"
                @edit="showEditDepartmentModal"
                @delete="showDeleteDepartmentConfirm"
                @refresh="refreshDepartmentTree"
              />
            </div>

            <!-- Right: 用户列表 -->
            <div class="right">
              <!-- 用户列表（包含搜索功能） -->
              <UserList
                :user-list="userList"
                :loading="userLoading"
                :pagination="pagination"
                :selected-department-name="selectedDepartmentName"
                :can-create="canCreate"
                :can-update="canUpdate"
                :can-delete="canDelete"
                :initial-search-form="searchForm"
                @create="showCreateModal"
                @edit="showEditModal"
                @delete="handleDelete"
                @reset-password="showResetPasswordModal"
                @table-change="handleTableChange"
                @refresh="refreshUserList"
                @search="handleSearch"
                @reset="handleReset"
              />
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="permission" tab="权限管理" v-if="canManagePermissions">
          <!-- 权限管理内容 -->
          <PermissionManagement
            :can-create-role="canCreateRole"
            :can-update-role="canUpdateRole"
            :can-delete-role="canDeleteRole"
            @create-role="showCreateRoleModal"
            @edit-role="showEditRoleModal"
            ref="permissionManagementRef"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 模态框组件 -->
    <!-- 企业信息编辑模态框 -->
    <EnterpriseEditModal
      v-model:visible="enterpriseEditModalVisible"
      :loading="enterpriseEditLoading"
      :enterprise-data="enterpriseFormData"
      :can-edit-code="userStore.hasRole('SUPER_ADMIN')"
      @ok="handleEnterpriseEditOk"
      @cancel="handleEnterpriseEditCancel"
    />

    <!-- 部门新增/编辑模态框 -->
    <DepartmentModal
      v-model:visible="departmentModalVisible"
      :loading="departmentModalLoading"
      :mode="departmentModalMode"
      :department-tree="departmentTree"
      :current-editing-department-id="currentEditingDepartmentId"
      :initial-data="departmentModalMode === 'edit' ? departmentFormData : undefined"
      @ok="handleDepartmentModalOk"
      @cancel="handleDepartmentModalCancel"
    />

    <!-- 用户模态框 -->
    <UserModal
      v-model:visible="userModalVisible"
      :loading="userFormLoading"
      :mode="userModalMode"
      :department-tree="departmentTree"
      :role-list="roleList"
      :role-loading="roleLoading"
      :initial-data="userModalMode === 'edit' ? userFormData : undefined"
      :selected-department-id="selectedDepartmentKeys[0]"
      @ok="handleUserModalOk"
      @cancel="handleUserModalCancel"
    />

    <!-- 重置密码模态框 -->
    <ResetPasswordModal
      v-model:visible="resetPasswordModalVisible"
      :loading="resetPasswordFormLoading"
      :user-name="currentResetPasswordUserName"
      @ok="handleResetPasswordOk"
      @cancel="handleResetPasswordCancel"
    />

    <!-- 角色创建/编辑模态框 -->
    <RoleModal
      v-model:visible="roleModalVisible"
      :loading="roleModalLoading"
      :mode="roleModalMode"
      :grouped-permissions="groupedPermissions"
      :initial-data="roleModalMode === 'edit' ? roleFormData : undefined"
      @ok="handleRoleModalOk"
      @cancel="handleRoleModalCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import { userApi, departmentApi } from '@/api/user'
import { enterpriseApi } from '@/api/enterprise'
import { roleApi, type Role, type Permission, type CreateRoleData, type UpdateRoleData } from '@/api/role'
import type {
  User,
  UserListQuery,
  DepartmentTreeNode,
  EnterpriseInfo,
  CreateUserRequest,
  UpdateUserRequest
} from '@/types/user'
import type { UpdateEnterpriseRequest } from '@/api/enterprise'

// 导入拆分的组件
import EnterpriseInfoCard from '@/components/user/EnterpriseInfoCard.vue'
import DepartmentTree from '@/components/user/DepartmentTree.vue'
import UserList from '@/components/user/UserList.vue'
import PermissionManagement from '@/components/user/PermissionManagement.vue'
import EnterpriseEditModal from '@/components/user/modals/EnterpriseEditModal.vue'
import DepartmentModal from '@/components/user/modals/DepartmentModal.vue'
import UserModal from '@/components/user/modals/UserModal.vue'
import ResetPasswordModal from '@/components/user/modals/ResetPasswordModal.vue'
import RoleModal from '@/components/user/modals/RoleModal.vue'

// 用户权限
const userStore = useUserStore()
const canCreate = computed(() => userStore.hasPermission('user:create'))
const canUpdate = computed(() => userStore.hasPermission('user:update'))
const canDelete = computed(() => userStore.hasPermission('user:delete'))

// 企业编辑权限
const canUpdateEnterprise = computed(() => {
  // 企业管理员、老板可以编辑自己的企业，超级管理员可以编辑任何企业
  return userStore.hasPermission('enterprise:update') &&
         (userStore.hasRole('ENTERPRISE_ADMIN') || userStore.hasRole('BOSS') || userStore.hasRole('SUPER_ADMIN'))
})

// 部门管理权限
const canCreateDepartment = computed(() => userStore.hasPermission('department:create'))
const canUpdateDepartment = computed(() => userStore.hasPermission('department:update'))
const canDeleteDepartment = computed(() => userStore.hasPermission('department:delete'))

// 权限管理权限
const canManagePermissions = computed(() => userStore.hasPermission('user:view'))
const canCreateRole = computed(() => userStore.hasPermission('user:create'))
const canUpdateRole = computed(() => userStore.hasPermission('user:update'))
const canDeleteRole = computed(() => userStore.hasPermission('user:delete'))

// Tab切换
const activeTab = ref('organization')

// 权限管理组件引用
const permissionManagementRef = ref()

// 角色模态框相关
const roleModalVisible = ref(false)
const roleModalMode = ref<'create' | 'edit'>('create')
const roleModalLoading = ref(false)
const roleFormData = reactive<CreateRoleData>({
  name: '',
  code: '',
  description: '',
  permissionIds: []
})

// 权限分组（从权限管理组件获取）
const groupedPermissions = computed(() => {
  return permissionManagementRef.value?.groupedPermissions || {}
})

// 企业信息
const enterpriseInfo = ref<EnterpriseInfo | null>(null)

// 企业编辑相关
const enterpriseEditModalVisible = ref(false)
const enterpriseEditLoading = ref(false)
const enterpriseFormData = reactive({
  name: '',
  code: '',
  logoUrl: '',
  description: '',
  status: 1
})

// 部门树相关
const departmentTree = ref<DepartmentTreeNode[]>([])
const departmentLoading = ref(false)
const selectedDepartmentKeys = ref<number[]>([])
const expandedDepartmentKeys = ref<number[]>([])
const selectedDepartmentName = ref('')
const hoveredDepartmentId = ref<number | null>(null)

// 用户模态框相关
const userModalVisible = ref(false)
const userModalMode = ref<'create' | 'edit'>('create')
const currentEditingUserId = ref<number | null>(null)
const userFormData = ref({
  username: '',
  email: '',
  realName: '',
  phone: '',
  departmentId: undefined as number | undefined,
  roleIds: [] as number[],
  status: 1,
  password: '', // 仅在创建时使用
})
const userFormLoading = ref(false)

// 重置密码模态框相关
const resetPasswordModalVisible = ref(false)
const resetPasswordFormLoading = ref(false)
const currentResetPasswordUserId = ref<number | null>(null)
const currentResetPasswordUserName = ref('')

// 部门管理模态框相关
const departmentModalVisible = ref(false)
const departmentModalLoading = ref(false)
const departmentModalMode = ref<'create' | 'edit'>('create')
const currentEditingDepartmentId = ref<number | null>(null)
const departmentFormData = reactive({
  name: '',
  parentId: undefined as number | undefined,
  description: '',
  sortOrder: undefined as number | undefined
})

// 用户列表相关
const userList = ref<User[]>([])
const userLoading = ref(false)
const searchForm = reactive<UserListQuery>({
  keyword: '',
  status: undefined,
  departmentId: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 角色列表（用于用户模态框）
const roleList = ref<Role[]>([])
const roleLoading = ref(false)

// 初始化数据
onMounted(() => {
  loadEnterpriseInfo()
  loadDepartmentTree()
  loadUserList()
  loadRoleList()
})

// 加载企业信息
const loadEnterpriseInfo = async () => {
  try {
    enterpriseInfo.value = await departmentApi.getCurrentEnterpriseInfo()
  } catch (error) {
    console.error('加载企业信息失败:', error)
  }
}

// 显示企业编辑模态框
const showEnterpriseEditModal = () => {
  if (!enterpriseInfo.value) {
    message.error('企业信息未加载')
    return
  }

  // 填充表单数据
  enterpriseFormData.name = enterpriseInfo.value.name
  enterpriseFormData.code = enterpriseInfo.value.code
  enterpriseFormData.logoUrl = enterpriseInfo.value.logoUrl || ''
  enterpriseFormData.description = enterpriseInfo.value.description || ''
  enterpriseFormData.status = enterpriseInfo.value.status

  enterpriseEditModalVisible.value = true
}

// 企业编辑模态框确认
const handleEnterpriseEditOk = async (updateData: UpdateEnterpriseRequest) => {
  try {
    enterpriseEditLoading.value = true

    if (!enterpriseInfo.value) {
      message.error('企业信息未加载')
      return
    }

    await enterpriseApi.updateEnterprise(enterpriseInfo.value.id, updateData)
    message.success('更新企业信息成功')

    // 重新加载企业信息
    await loadEnterpriseInfo()

    enterpriseEditModalVisible.value = false
  } catch (error) {
    message.error('更新企业信息失败')
    console.error('更新企业信息失败:', error)
  } finally {
    enterpriseEditLoading.value = false
  }
}

// 企业编辑模态框取消
const handleEnterpriseEditCancel = () => {
  enterpriseEditModalVisible.value = false
}

// 查找总部部门ID
const findHeadquartersDepartmentId = (tree: any[]): number | null => {
  for (const node of tree) {
    if (node.name === '总部') {
      return node.id
    }
    if (node.children && node.children.length > 0) {
      const found = findHeadquartersDepartmentId(node.children)
      if (found) return found
    }
  }
  return null
}

// 加载部门树
const loadDepartmentTree = async () => {
  departmentLoading.value = true
  try {
    departmentTree.value = await departmentApi.getDepartmentTree()

    // 自动展开总部
    const headquartersId = findHeadquartersDepartmentId(departmentTree.value)
    if (headquartersId) {
      expandedDepartmentKeys.value = [headquartersId]
    }
  } catch (error) {
    message.error('加载部门树失败')
    console.error('加载部门树失败:', error)
  } finally {
    departmentLoading.value = false
  }
}

// 加载角色列表
const loadRoleList = async () => {
  roleLoading.value = true
  try {
    roleList.value = await roleApi.getRoleList()
  } catch (error) {
    message.error('加载角色列表失败')
    console.error('加载角色列表失败:', error)
  } finally {
    roleLoading.value = false
  }
}

// 角色模态框相关方法

// 显示创建角色模态框
const showCreateRoleModal = () => {
  roleModalMode.value = 'create'
  roleFormData.name = ''
  roleFormData.code = ''
  roleFormData.description = ''
  roleFormData.permissionIds = []
  roleModalVisible.value = true
}

// 显示编辑角色模态框
const showEditRoleModal = (role: Role) => {
  roleModalMode.value = 'edit'
  roleFormData.name = role.name
  roleFormData.code = role.code
  roleFormData.description = role.description || ''
  roleFormData.permissionIds = role.permissions?.map(p => p.id) || []
  roleModalVisible.value = true
}

// 角色模态框确认
const handleRoleModalOk = async (data: CreateRoleData | UpdateRoleData) => {
  try {
    roleModalLoading.value = true

    if (roleModalMode.value === 'create') {
      await roleApi.createRole(data as CreateRoleData)
      message.success('角色创建成功')
    } else {
      // 需要获取当前编辑的角色ID，这里从权限管理组件获取
      const selectedRole = permissionManagementRef.value?.selectedRole
      if (!selectedRole) return

      await roleApi.updateRole(selectedRole.id, data as UpdateRoleData)
      message.success('角色更新成功')
    }

    roleModalVisible.value = false
    await loadRoleList()
    // 刷新权限管理组件的角色列表
    permissionManagementRef.value?.loadRoleList()
  } catch (error) {
    message.error(roleModalMode.value === 'create' ? '角色创建失败' : '角色更新失败')
    console.error('角色操作失败:', error)
  } finally {
    roleModalLoading.value = false
  }
}

// 角色模态框取消
const handleRoleModalCancel = () => {
  roleModalVisible.value = false
}

// 刷新部门树
const refreshDepartmentTree = () => {
  loadDepartmentTree()
}

// 部门选择
const onDepartmentSelect = (selectedKeys: number[], info: any) => {
  selectedDepartmentKeys.value = selectedKeys
  if (selectedKeys.length > 0) {
    searchForm.departmentId = selectedKeys[0]
    selectedDepartmentName.value = info.node.name
  } else {
    searchForm.departmentId = undefined
    selectedDepartmentName.value = ''
  }
  loadUserList()
}

// 部门展开/收起
const onDepartmentExpand = (expandedKeys: number[]) => {
  expandedDepartmentKeys.value = expandedKeys
}

// 加载用户列表
const loadUserList = async () => {
  userLoading.value = true
  try {
    const params: UserListQuery = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    // 在用户管理页面中，总是查询当前企业的用户
    // 这样可以确保企业管理员只看到自己企业的用户，超级管理员不会出现在企业用户列表中
    if (enterpriseInfo.value?.id) {
      params.enterpriseId = enterpriseInfo.value.id
    }

    const response = await userApi.getUserList(params)
    userList.value = response.users
    pagination.total = response.total
  } catch (error) {
    message.error('加载用户列表失败')
    console.error('加载用户列表失败:', error)
  } finally {
    userLoading.value = false
  }
}

// 刷新用户列表
const refreshUserList = () => {
  loadUserList()
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadUserList()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = undefined
  searchForm.departmentId = undefined
  selectedDepartmentKeys.value = []
  selectedDepartmentName.value = ''
  pagination.current = 1
  loadUserList()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadUserList()
}

// 显示创建用户模态框
const showCreateModal = () => {
  userModalMode.value = 'create'
  currentEditingUserId.value = null

  // 创建模式下清空表单数据，让UserModal组件自己处理默认值
  userFormData.value = {
    username: '',
    email: '',
    realName: '',
    phone: '',
    departmentId: undefined,
    roleIds: [],
    status: 1,
    password: '',
  }

  userModalVisible.value = true
}

// 显示编辑用户模态框
const showEditModal = (user: User) => {
  userModalMode.value = 'edit'
  currentEditingUserId.value = user.id

  // 填充表单数据
  userFormData.value = {
    username: user.username,
    email: user.email || '',
    realName: user.realName,
    phone: user.phone || '',
    departmentId: user.department?.id,
    roleIds: user.roles ? user.roles.map((role: any) => role.id) : [],
    status: user.status,
    password: '', // 编辑时不显示密码
  }

  userModalVisible.value = true
}

// 显示重置密码模态框
const showResetPasswordModal = (user: User) => {
  currentResetPasswordUserId.value = user.id
  currentResetPasswordUserName.value = user.realName
  resetPasswordModalVisible.value = true
}

// 用户模态框确认
const handleUserModalOk = async (data: CreateUserRequest | UpdateUserRequest) => {
  userFormLoading.value = true
  try {
    if (userModalMode.value === 'create') {
      // 调用创建用户API
      await userApi.createUser(data as CreateUserRequest)
      message.success('用户创建成功')
    } else {
      // 调用更新用户API
      await userApi.updateUser(currentEditingUserId.value!, data as UpdateUserRequest)
      message.success('用户更新成功')
    }

    userModalVisible.value = false

    // 刷新所有相关数据：用户列表、企业信息、部门树
    await Promise.all([
      loadUserList(),
      loadEnterpriseInfo(),
      loadDepartmentTree()
    ])
  } catch (error) {
    console.error('用户操作失败:', error)
    message.error(userModalMode.value === 'create' ? '创建用户失败' : '更新用户失败')
  } finally {
    userFormLoading.value = false
  }
}

// 用户模态框取消
const handleUserModalCancel = () => {
  userModalVisible.value = false
}

// 重置密码模态框确认
const handleResetPasswordOk = async (data: { newPassword: string }) => {
  try {
    resetPasswordFormLoading.value = true

    await userApi.resetPassword(currentResetPasswordUserId.value!, {
      newPassword: data.newPassword
    })

    message.success('重置密码成功')
    resetPasswordModalVisible.value = false
  } catch (error) {
    console.error('重置密码失败:', error)
    message.error('重置密码失败')
  } finally {
    resetPasswordFormLoading.value = false
  }
}

// 重置密码模态框取消
const handleResetPasswordCancel = () => {
  resetPasswordModalVisible.value = false
}

// 部门管理相关方法
// 显示部门操作按钮
const showDepartmentActions = (departmentId: number) => {
  hoveredDepartmentId.value = departmentId
}

// 隐藏部门操作按钮
const hideDepartmentActions = () => {
  hoveredDepartmentId.value = null
}

// 显示创建部门模态框
const showCreateDepartmentModal = (parentId?: number) => {
  departmentModalMode.value = 'create'
  currentEditingDepartmentId.value = null

  // 重置表单数据
  departmentFormData.name = ''
  departmentFormData.parentId = parentId
  departmentFormData.description = ''
  departmentFormData.sortOrder = undefined

  departmentModalVisible.value = true
}

// 显示编辑部门模态框
const showEditDepartmentModal = async (departmentId: number) => {
  try {
    departmentModalMode.value = 'edit'
    currentEditingDepartmentId.value = departmentId

    // 获取部门详情
    const department = await departmentApi.getDepartmentById(departmentId)

    // 填充表单数据
    departmentFormData.name = department.name
    departmentFormData.parentId = department.parentId
    departmentFormData.description = department.description || ''
    departmentFormData.sortOrder = department.sortOrder

    departmentModalVisible.value = true
  } catch (error) {
    message.error('获取部门信息失败')
    console.error('获取部门信息失败:', error)
  }
}

// 显示删除部门确认对话框
const showDeleteDepartmentConfirm = (departmentId: number) => {
  const department = findDepartmentById(departmentTree.value, departmentId)
  if (!department) {
    message.error('部门不存在')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除部门"${department.name}"吗？删除后不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: () => deleteDepartment(departmentId)
  })
}

// 查找部门
const findDepartmentById = (nodes: DepartmentTreeNode[], id: number): DepartmentTreeNode | null => {
  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children) {
      const found = findDepartmentById(node.children, id)
      if (found) return found
    }
  }
  return null
}

// 部门模态框确认
const handleDepartmentModalOk = async (data: any) => {
  try {
    departmentModalLoading.value = true

    // 处理 parentId：如果用户没有选择上级部门，则默认设置为总部
    if (data.parentId === undefined && departmentModalMode.value === 'create') {
      // 创建新部门时，如果没有选择上级部门，默认设置为总部
      const headquartersId = findHeadquartersDepartmentId(departmentTree.value)
      if (headquartersId) {
        data.parentId = headquartersId
      }
    }

    if (departmentModalMode.value === 'create') {
      await departmentApi.createDepartment(data)
      message.success('创建部门成功')
    } else {
      await departmentApi.updateDepartment(currentEditingDepartmentId.value!, data)
      message.success('更新部门成功')
    }

    // 重新加载部门树
    await loadDepartmentTree()

    departmentModalVisible.value = false
  } catch (error) {
    const action = departmentModalMode.value === 'create' ? '创建' : '更新'
    message.error(`${action}部门失败`)
    console.error(`${action}部门失败:`, error)
  } finally {
    departmentModalLoading.value = false
  }
}

// 部门模态框取消
const handleDepartmentModalCancel = () => {
  departmentModalVisible.value = false
}

// 删除部门
const deleteDepartment = async (departmentId: number) => {
  try {
    await departmentApi.deleteDepartment(departmentId)
    message.success('删除部门成功')

    // 重新加载部门树
    await loadDepartmentTree()

    // 如果删除的是当前选中的部门，清除选择
    if (selectedDepartmentKeys.value.includes(departmentId)) {
      selectedDepartmentKeys.value = []
      selectedDepartmentName.value = ''
      searchForm.departmentId = undefined
      loadUserList()
    }
  } catch (error) {
    message.error('删除部门失败')
    console.error('删除部门失败:', error)
  }
}

// 删除用户
const handleDelete = async (userId: number) => {
  try {
    await userApi.deleteUser(userId)
    message.success('删除用户成功')

    // 刷新所有相关数据：用户列表、企业信息、部门树
    await Promise.all([
      loadUserList(),
      loadEnterpriseInfo(),
      loadDepartmentTree()
    ])
  } catch (error) {
    message.error('删除用户失败')
    console.error('删除用户失败:', error)
  }
}

// 这些方法已经移到了UserList组件中，不再需要
</script>

<style scoped>
.user-management {
  padding: 16px;
  background: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Header: 企业信息 */
.header {
  flex-shrink: 0;
}

/* Tab切换样式 */
.management-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.management-tabs :deep(.ant-tabs-nav) {
  flex-shrink: 0;
}

.management-tabs :deep(.ant-tabs-content-holder) {
  flex: 1;
  min-height: 0;
}

.management-tabs :deep(.ant-tabs-content) {
  height: 100%;
}

.management-tabs :deep(.ant-tabs-tabpane) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 组织管理内容布局 */
.organization-content {
  flex: 1;
  display: flex;
  gap: 16px;
  min-height: 0;
}

/* Content: Tab容器 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* Left: 部门树 */
.left {
  width: 300px;
  flex-shrink: 0;
}

/* Right: 用户列表 */
.right {
  flex: 1;
  min-width: 0;
}

/* 这些样式已经移到了各个组件中，不再需要 */

/* 响应式设计 */
@media (max-width: 992px) {
  .organization-content {
    flex-direction: column;
  }

  .left,
  .right {
    width: 100% !important;
    flex: none !important;
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .user-management {
    padding: 8px;
  }
}
</style>
