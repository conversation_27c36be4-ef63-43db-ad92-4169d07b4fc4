<!--
  个人信息页面
  {{CHENGQI: Action: Added; Timestamp: 2025-06-29 15:30:00 +08:00; Reason: 用户管理模块完善, 创建个人信息页面; Principle_Applied: 用户体验优化;}}
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 12:42:55 +08:00; Reason: Task-009 个人信息页面集成, 添加微信绑定功能; Principle_Applied: 功能集成;}}
-->

<template>
  <div class="profile-container">
    <a-card title="个人信息" :bordered="false">
      <template #extra>
        <a-button
          v-if="!isEditing"
          type="primary"
          @click="startEdit"
        >
          <EditOutlined />
          编辑信息
        </a-button>
        <a-space v-else>
          <a-button @click="cancelEdit">
            取消
          </a-button>
          <a-button
            type="primary"
            :loading="saving"
            @click="saveProfile"
          >
            保存
          </a-button>
        </a-space>
      </template>

      <!-- 用户基本信息区域 -->
      <div class="profile-header">
        <div class="avatar-section">
          <a-avatar
            :size="80"
            :src="userStore.user?.avatarUrl"
            class="profile-avatar"
          >
            {{ userStore.user?.realName?.charAt(0) }}
          </a-avatar>
          <div v-if="isEditing" class="avatar-upload">
            <a-button type="link" size="small">
              更换头像
            </a-button>
          </div>
        </div>

        <div class="user-basic-info">
          <h2 class="user-name">{{ userStore.user?.realName }}</h2>
          <div class="user-meta">
            <a-tag color="blue" class="role-tag">
              {{ formatRoleName(userStore.roles[0]) }}
            </a-tag>
            <a-tag :color="userStore.user?.status === 1 ? 'green' : 'red'" class="status-tag">
              {{ userStore.user?.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </div>
          <div class="enterprise-info">
            <span class="enterprise-name">{{ userStore.user?.enterprise?.name }}</span>
            <span v-if="userStore.user?.department?.name" class="department-name">
              · {{ userStore.user?.department?.name }}
            </span>
          </div>
        </div>
      </div>

      <!-- 详细信息表单 -->
      <div class="profile-form">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          layout="vertical"
          :disabled="!isEditing"
        >
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="用户名" name="username">
                  <a-input
                    v-model:value="formData.username"
                    disabled
                    placeholder="用户名不可修改"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="真实姓名" name="realName">
                  <a-input
                    v-model:value="formData.realName"
                    placeholder="请输入真实姓名"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="邮箱地址" name="email">
                  <a-input
                    v-model:value="formData.email"
                    placeholder="请输入邮箱地址"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="手机号码" name="phone">
                  <a-input
                    v-model:value="formData.phone"
                    placeholder="请输入手机号"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属企业">
                  <a-input
                    :value="userStore.user?.enterprise?.name"
                    disabled
                    placeholder="企业信息不可修改"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属部门">
                  <a-input
                    :value="userStore.user?.department?.name || '未分配'"
                    disabled
                    placeholder="部门信息不可修改"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <div class="form-section">
            <h3 class="section-title">权限信息</h3>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户角色">
                  <div class="roles-display">
                    <a-tag
                      v-for="role in userStore.roles"
                      :key="role"
                      color="blue"
                      class="role-tag-item"
                    >
                      {{ formatRoleName(role) }}
                    </a-tag>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="账号状态">
                  <a-tag
                    :color="userStore.user?.status === 1 ? 'green' : 'red'"
                    class="status-display"
                  >
                    {{ userStore.user?.status === 1 ? '正常' : '禁用' }}
                  </a-tag>
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <div class="form-section">
            <h3 class="section-title">系统信息</h3>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="最后登录时间">
                  <a-input
                    :value="formatDateTime(userStore.user?.lastLoginAt)"
                    disabled
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="账号创建时间">
                  <a-input
                    :value="formatDateTime(userStore.user?.createdAt)"
                    disabled
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </div>
    </a-card>

    <!-- 微信绑定卡片 -->
    <div style="margin-top: 16px">
      <WechatBindCard
        :loading="wechatLoading"
        :disabled="isEditing"
        @bind="handleWechatBind"
        @unbind="handleWechatUnbind"
        @refresh="handleWechatRefresh"
      />
    </div>

    <!-- 修改密码卡片 -->
    <a-card title="修改密码" :bordered="false" style="margin-top: 16px">
      <a-alert
        message="密码安全提示"
        description="为了您的账号安全，建议定期更换密码。新密码长度应在6-50个字符之间。"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />

      <a-form
        ref="passwordFormRef"
        :model="passwordFormData"
        :rules="passwordFormRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="当前密码" name="currentPassword">
              <a-input-password
                v-model:value="passwordFormData.currentPassword"
                placeholder="请输入当前密码"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="新密码" name="newPassword">
              <a-input-password
                v-model:value="passwordFormData.newPassword"
                placeholder="请输入新密码"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="确认新密码" name="confirmPassword">
              <a-input-password
                v-model:value="passwordFormData.confirmPassword"
                placeholder="请再次输入新密码"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item style="margin-bottom: 0;">
          <a-space>
            <a-button
              type="primary"
              :loading="changingPassword"
              @click="changePassword"
            >
              修改密码
            </a-button>
            <a-button @click="resetPasswordForm">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { EditOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { userApi } from '@/api/user'
import WechatBindCard from '@/components/user/WechatBindCard.vue'
import dayjs from 'dayjs'

const userStore = useUserStore()

// 表单引用
const formRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 编辑状态
const isEditing = ref(false)
const saving = ref(false)
const changingPassword = ref(false)
const wechatLoading = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  realName: '',
  email: '',
  phone: ''
})

// 密码表单数据
const passwordFormData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '真实姓名长度在2-20个字符', trigger: 'blur' }
  ],
  email: [
    {
      validator: (_rule, value) => {
        if (!value) {
          return Promise.resolve() // 邮箱为空时不验证
        }
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailPattern.test(value)) {
          return Promise.reject('请输入正确的邮箱格式')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  phone: [
    {
      validator: (_rule, value) => {
        if (!value) {
          return Promise.resolve() // 手机号为空时不验证
        }
        const phonePattern = /^1[3-9]\d{9}$/
        if (!phonePattern.test(value)) {
          return Promise.reject('请输入正确的手机号格式')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 密码表单验证规则
const passwordFormRules: Record<string, Rule[]> = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在6-50个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_rule, value) => {
        if (value !== passwordFormData.newPassword) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (userStore.user) {
    formData.username = userStore.user.username
    formData.realName = userStore.user.realName
    formData.email = userStore.user.email || ''
    formData.phone = userStore.user.phone || ''
  }
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
  initFormData()
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  initFormData()
}

// 保存个人信息
const saveProfile = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true

    // 调用更新个人信息API
    await userApi.updateProfile({
      realName: formData.realName,
      email: formData.email,
      phone: formData.phone
    })

    // 更新本地用户信息
    userStore.updateUserInfo({
      realName: formData.realName,
      email: formData.email,
      phone: formData.phone
    })

    message.success('个人信息更新成功')
    isEditing.value = false
  } catch (error) {
    console.error('更新个人信息失败:', error)
    message.error('更新个人信息失败')
  } finally {
    saving.value = false
  }
}

// 修改密码
const changePassword = async () => {
  try {
    await passwordFormRef.value?.validate()
    changingPassword.value = true

    // 调用修改密码API
    await userApi.changePassword({
      currentPassword: passwordFormData.currentPassword,
      newPassword: passwordFormData.newPassword
    })

    message.success('密码修改成功，请重新登录')

    // 清空表单
    resetPasswordForm()

    // 退出登录
    setTimeout(() => {
      userStore.logout()
    }, 1500)
  } catch (error) {
    console.error('修改密码失败:', error)
    message.error('修改密码失败')
  } finally {
    changingPassword.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordFormData.currentPassword = ''
  passwordFormData.newPassword = ''
  passwordFormData.confirmPassword = ''
  passwordFormRef.value?.clearValidate()
}

// 格式化角色名称
const formatRoleName = (roleCode: string): string => {
  const roleMap: Record<string, string> = {
    'SUPER_ADMIN': '超级管理员',
    'ENTERPRISE_ADMIN': '企业管理员',
    'SALES': '销售',
    'FOLLOWER': '跟单',
    'PLATE_MAKER': '制板师',
    'WORKSHOP_DIRECTOR': '车间主任',
    'FOREMAN': '领班',
    'OPERATOR': '挡车工'
  }
  return roleMap[roleCode] || roleCode
}

// 格式化日期时间
const formatDateTime = (dateTime?: string): string => {
  if (!dateTime) return '-'
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// ==================== 微信绑定事件处理 ====================

/**
 * 处理微信绑定事件
 */
const handleWechatBind = () => {
  console.log('微信绑定开始')
  // 可以在这里添加额外的逻辑，比如显示提示信息
}

/**
 * 处理微信解绑事件
 */
const handleWechatUnbind = () => {
  console.log('微信解绑完成')
  // 可以在这里添加额外的逻辑，比如显示提示信息
}

/**
 * 处理微信状态刷新事件
 */
const handleWechatRefresh = async () => {
  try {
    wechatLoading.value = true
    // 刷新用户信息，确保微信绑定状态是最新的
    await userStore.refreshUserProfile()
    console.log('微信状态刷新完成')
  } catch (error) {
    console.error('刷新微信状态失败:', error)
    message.error('刷新状态失败，请稍后重试')
  } finally {
    wechatLoading.value = false
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  initFormData()
})
</script>

<style scoped>
/* 整体布局 - 紧凑版本 */
.profile-container {
  padding: 8px 12px; /* 减少左右和顶部的padding */
  max-width: 100%;
  margin: 0;
}

/* 用户基本信息头部 */
.profile-header {
  display: flex;
  align-items: center;
  padding: 16px 0; /* 减少内部padding */
  margin-bottom: 16px; /* 减少底部间距 */
}

.avatar-section {
  margin-right: 20px; /* 减少右边距 */
  text-align: center;
}

.profile-avatar {
  background-color: #1890ff;
  margin-bottom: 8px; /* 减少底部间距 */
}

.avatar-upload {
  margin-top: 4px; /* 减少顶部间距 */
}

.user-basic-info {
  flex: 1;
}

.user-name {
  font-size: 22px; /* 稍微减小字体 */
  font-weight: 600;
  margin: 0 0 8px 0; /* 减少底部间距 */
  color: #262626;
}

.user-meta {
  margin-bottom: 8px; /* 减少底部间距 */
}

.role-tag,
.status-tag {
  margin-right: 8px;
  font-weight: 500;
}

.enterprise-info {
  font-size: 14px; /* 减小字体 */
  color: #666;
}

.enterprise-name {
  font-weight: 500;
}

.department-name {
  color: #999;
}

/* 表单区域 */
.profile-form {
  background: white;
  border-radius: 8px;
  padding: 0;
}

.form-section {
  padding: 16px 0; /* 减少内部padding */
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 16px; /* 减小字体 */
  font-weight: 600;
  color: #262626;
  margin: 0 0 12px 0; /* 减少底部间距 */
  padding-bottom: 6px; /* 减少底部padding */
  border-bottom: 1px solid #f0f0f0;
}

.roles-display {
  display: flex;
  flex-wrap: wrap;
  gap: 6px; /* 减少间距 */
}

.role-tag-item {
  margin: 0;
}

.status-display {
  font-weight: 500;
}

/* 表单样式优化 */
:deep(.ant-form-item) {
  margin-bottom: 16px; /* 减少表单项间距 */
}

:deep(.ant-form-item-label) {
  font-weight: 500;
  color: #262626;
  padding-bottom: 4px; /* 减少标签底部间距 */
}

:deep(.ant-form-item-label > label) {
  font-size: 13px; /* 稍微减小标签字体 */
}

:deep(.ant-input[disabled]) {
  background-color: #fafafa;
  border-color: #e8e8e8;
  color: #8c8c8c;
  cursor: not-allowed;
}

:deep(.ant-input) {
  border-radius: 4px; /* 减小圆角 */
  transition: all 0.3s;
}

:deep(.ant-input:hover:not([disabled])) {
  border-color: #40a9ff;
}

:deep(.ant-input:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.ant-input-password) {
  border-radius: 4px;
}

:deep(.ant-input-password .ant-input) {
  border-radius: 4px;
}

:deep(.ant-tag) {
  border-radius: 4px;
  font-weight: 500;
}

:deep(.ant-alert) {
  border-radius: 6px;
}

/* 卡片样式 */
:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06); /* 减小阴影 */
  border: 1px solid #f0f0f0;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px; /* 减少左右padding */
  min-height: 48px; /* 减小头部高度 */
}

:deep(.ant-card-head-title) {
  font-size: 16px; /* 减小标题字体 */
  font-weight: 600;
  color: #262626;
}

:deep(.ant-card-body) {
  padding: 16px; /* 减少卡片内部padding */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 4px 8px; /* 移动端进一步减少padding */
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    padding: 12px 0;
  }

  .avatar-section {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .form-section {
    padding: 12px 0;
  }

  .user-name {
    font-size: 20px;
  }

  :deep(.ant-card-body) {
    padding: 12px;
  }
}
</style>
