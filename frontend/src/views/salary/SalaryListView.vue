<!--
  工资列表页面
  {{CHENGQI: Action: Added; Timestamp: 2025-07-10 18:00:00 +08:00; Reason: 工资管理权限细化, 创建工资列表子页面; Principle_Applied: 模块化页面设计;}}
-->

<template>
  <div class="salary-list">
    <a-card title="工资列表" :bordered="false">
      <!-- 操作栏 -->
      <template #extra>
        <a-space>
          <a-button 
            type="primary" 
            :icon="h(PlusOutlined)"
            v-permission="'salary_list:create'"
            @click="showCreateModal"
          >
            新增工资记录
          </a-button>
          <a-button 
            :icon="h(ExportOutlined)"
            v-permission="'salary_list:export'"
            @click="handleExport"
          >
            导出数据
          </a-button>
          <a-button 
            :icon="h(ReloadOutlined)"
            @click="refreshData"
          >
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <a-row :gutter="16">
          <a-col :span="5">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="搜索员工姓名"
              :prefix="h(SearchOutlined)"
              @press-enter="handleSearch"
            />
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="searchForm.status"
              placeholder="审核状态"
              allow-clear
            >
              <a-select-option value="pending">待审核</a-select-option>
              <a-select-option value="approved">已审核</a-select-option>
              <a-select-option value="rejected">已拒绝</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="5">
            <a-range-picker
              v-model:value="searchForm.dateRange"
              placeholder="['开始日期', '结束日期']"
              style="width: 100%"
            />
          </a-col>
          <a-col :span="6">
            <a-space>
              <a-button type="primary" @click="handleSearch">搜索</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'totalAmount'">
            <span class="amount-value">¥{{ record.totalAmount }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button 
                type="link" 
                size="small"
                @click="showDetailModal(record)"
              >
                查看
              </a-button>
              <a-button 
                type="link" 
                size="small"
                v-permission="'salary_list:update'"
                v-if="record.status === 'pending'"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>
              <a-button 
                type="link" 
                size="small"
                v-permission="'salary_list:approve'"
                v-if="record.status === 'pending'"
                @click="handleApprove(record)"
              >
                审核
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                danger
                v-permission="'salary_list:delete'"
                v-if="record.status === 'pending'"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalMode === 'create' ? '新增工资记录' : '编辑工资记录'"
      :confirm-loading="modalLoading"
      width="800px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="员工姓名" name="employeeName">
              <a-input v-model:value="formData.employeeName" placeholder="请输入员工姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工资月份" name="salaryMonth">
              <a-month-picker 
                v-model:value="formData.salaryMonth" 
                placeholder="请选择工资月份"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="基本工资" name="baseSalary">
              <a-input-number
                v-model:value="formData.baseSalary"
                :min="0"
                :precision="2"
                placeholder="基本工资"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="绩效工资" name="performanceSalary">
              <a-input-number
                v-model:value="formData.performanceSalary"
                :min="0"
                :precision="2"
                placeholder="绩效工资"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="加班费" name="overtimePay">
              <a-input-number
                v-model:value="formData.overtimePay"
                :min="0"
                :precision="2"
                placeholder="加班费"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="津贴补助" name="allowance">
              <a-input-number
                v-model:value="formData.allowance"
                :min="0"
                :precision="2"
                placeholder="津贴补助"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="扣除项" name="deduction">
              <a-input-number
                v-model:value="formData.deduction"
                :min="0"
                :precision="2"
                placeholder="扣除项"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="实发工资" name="totalAmount">
              <a-input-number
                v-model:value="calculatedTotal"
                :precision="2"
                placeholder="自动计算"
                disabled
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 审核模态框 -->
    <a-modal
      v-model:open="approveModalVisible"
      title="审核工资记录"
      :confirm-loading="approveLoading"
      @ok="handleApproveOk"
      @cancel="approveModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="审核结果">
          <a-radio-group v-model:value="approveForm.result">
            <a-radio value="approved">通过</a-radio>
            <a-radio value="rejected">拒绝</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审核意见">
          <a-textarea 
            v-model:value="approveForm.comment" 
            placeholder="请输入审核意见"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  PlusOutlined, 
  ReloadOutlined, 
  SearchOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const dataList = ref([])
const modalVisible = ref(false)
const modalLoading = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const formRef = ref()
const approveModalVisible = ref(false)
const approveLoading = ref(false)
const currentRecord = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined,
  dateRange: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '员工姓名',
    dataIndex: 'employeeName',
    key: 'employeeName'
  },
  {
    title: '工资月份',
    dataIndex: 'salaryMonth',
    key: 'salaryMonth'
  },
  {
    title: '实发工资',
    dataIndex: 'totalAmount',
    key: 'totalAmount'
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt'
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

// 表单数据
const formData = reactive({
  employeeName: '',
  salaryMonth: undefined,
  baseSalary: 0,
  performanceSalary: 0,
  overtimePay: 0,
  allowance: 0,
  deduction: 0,
  remark: ''
})

// 审核表单
const approveForm = reactive({
  result: 'approved',
  comment: ''
})

// 表单验证规则
const formRules = {
  employeeName: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' }
  ],
  salaryMonth: [
    { required: true, message: '请选择工资月份', trigger: 'change' }
  ]
}

// 计算实发工资
const calculatedTotal = computed(() => {
  const total = (formData.baseSalary || 0) + 
                (formData.performanceSalary || 0) + 
                (formData.overtimePay || 0) + 
                (formData.allowance || 0) - 
                (formData.deduction || 0)
  return Math.max(0, total)
})

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    pending: 'orange',
    approved: 'green',
    rejected: 'red'
  }
  return colors[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts = {
    pending: '待审核',
    approved: '已审核',
    rejected: '已拒绝'
  }
  return texts[status] || status
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取工资列表数据
    // const response = await salaryListApi.getList({
    //   page: pagination.current,
    //   pageSize: pagination.pageSize,
    //   ...searchForm
    // })
    // dataList.value = response.list
    // pagination.total = response.total
    
    // 模拟数据
    dataList.value = []
    pagination.total = 0
    
    message.info('工资列表功能开发中，敬请期待')
  } catch (error) {
    message.error('加载数据失败')
    console.error('加载工资列表数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 其他方法实现...
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = undefined
  searchForm.dateRange = undefined
  pagination.current = 1
  loadData()
}

const refreshData = () => {
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const showCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  modalVisible.value = true
}

const showEditModal = (record: any) => {
  modalMode.value = 'edit'
  Object.assign(formData, record)
  modalVisible.value = true
}

const showDetailModal = (record: any) => {
  // TODO: 实现详情查看
  message.info('查看详情功能开发中')
}

const resetForm = () => {
  Object.assign(formData, {
    employeeName: '',
    salaryMonth: undefined,
    baseSalary: 0,
    performanceSalary: 0,
    overtimePay: 0,
    allowance: 0,
    deduction: 0,
    remark: ''
  })
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    // TODO: 调用API保存数据
    message.success(`${modalMode.value === 'create' ? '创建' : '更新'}成功`)
    modalVisible.value = false
    loadData()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    modalLoading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

const handleApprove = (record: any) => {
  currentRecord.value = record
  approveForm.result = 'approved'
  approveForm.comment = ''
  approveModalVisible.value = true
}

const handleApproveOk = async () => {
  try {
    approveLoading.value = true
    // TODO: 调用API审核
    message.success('审核完成')
    approveModalVisible.value = false
    loadData()
  } catch (error) {
    message.error('审核失败')
    console.error('审核失败:', error)
  } finally {
    approveLoading.value = false
  }
}

const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除员工"${record.employeeName}"的工资记录吗？`,
    onOk: async () => {
      try {
        // TODO: 调用API删除数据
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
        console.error('删除工资记录失败:', error)
      }
    }
  })
}

const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中，敬请期待')
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.salary-list {
  padding: 24px;
}

.search-bar {
  margin-bottom: 16px;
}

.amount-value {
  font-weight: 500;
  color: #1890ff;
}
</style>
