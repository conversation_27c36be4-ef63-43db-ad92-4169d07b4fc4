<!--
  工价设置页面
  {{CHENGQI: Action: Added; Timestamp: 2025-07-10 18:00:00 +08:00; Reason: 工资管理权限细化, 创建工价设置子页面; Principle_Applied: 模块化页面设计;}}
-->

<template>
  <div class="salary-rates">
    <a-card title="工价设置" :bordered="false">
      <!-- 操作栏 -->
      <template #extra>
        <a-space>
          <a-button 
            type="primary" 
            :icon="h(PlusOutlined)"
            v-permission="'salary_rate:create'"
            @click="showCreateModal"
          >
            新增工价
          </a-button>
          <a-button 
            :icon="h(ReloadOutlined)"
            @click="refreshData"
          >
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="搜索工价名称"
              :prefix="h(SearchOutlined)"
              @press-enter="handleSearch"
            />
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="searchForm.status"
              placeholder="状态"
              allow-clear
            >
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-space>
              <a-button type="primary" @click="handleSearch">搜索</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'rate'">
            <span class="rate-value">¥{{ record.rate }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button 
                type="link" 
                size="small"
                v-permission="'salary_rate:update'"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                danger
                v-permission="'salary_rate:delete'"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalMode === 'create' ? '新增工价' : '编辑工价'"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="工价名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入工价名称" />
        </a-form-item>
        <a-form-item label="工价金额" name="rate">
          <a-input-number
            v-model:value="formData.rate"
            :min="0"
            :precision="2"
            placeholder="请输入工价金额"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="计价单位" name="unit">
          <a-input v-model:value="formData.unit" placeholder="如：件、米、小时等" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  PlusOutlined, 
  ReloadOutlined, 
  SearchOutlined 
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const dataList = ref([])
const modalVisible = ref(false)
const modalLoading = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '工价名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '工价金额',
    dataIndex: 'rate',
    key: 'rate'
  },
  {
    title: '计价单位',
    dataIndex: 'unit',
    key: 'unit'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt'
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

// 表单数据
const formData = reactive({
  name: '',
  rate: undefined,
  unit: '',
  remark: '',
  status: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入工价名称', trigger: 'blur' }
  ],
  rate: [
    { required: true, message: '请输入工价金额', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入计价单位', trigger: 'blur' }
  ]
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取工价设置数据
    // const response = await salaryRateApi.getList({
    //   page: pagination.current,
    //   pageSize: pagination.pageSize,
    //   ...searchForm
    // })
    // dataList.value = response.list
    // pagination.total = response.total
    
    // 模拟数据
    dataList.value = []
    pagination.total = 0
    
    message.info('工价设置功能开发中，敬请期待')
  } catch (error) {
    message.error('加载数据失败')
    console.error('加载工价设置数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = undefined
  pagination.current = 1
  loadData()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

// 显示创建模态框
const showCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  modalVisible.value = true
}

// 显示编辑模态框
const showEditModal = (record: any) => {
  modalMode.value = 'edit'
  Object.assign(formData, record)
  modalVisible.value = true
}

// 重置表单
const resetForm = () => {
  formData.name = ''
  formData.rate = undefined
  formData.unit = ''
  formData.remark = ''
  formData.status = 1
}

// 模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    
    // TODO: 调用API保存数据
    // if (modalMode.value === 'create') {
    //   await salaryRateApi.create(formData)
    // } else {
    //   await salaryRateApi.update(formData.id, formData)
    // }
    
    message.success(`${modalMode.value === 'create' ? '创建' : '更新'}成功`)
    modalVisible.value = false
    loadData()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除工价"${record.name}"吗？`,
    onOk: async () => {
      try {
        // TODO: 调用API删除数据
        // await salaryRateApi.delete(record.id)
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
        console.error('删除工价设置失败:', error)
      }
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.salary-rates {
  padding: 24px;
}

.search-bar {
  margin-bottom: 16px;
}

.rate-value {
  font-weight: 500;
  color: #1890ff;
}
</style>
