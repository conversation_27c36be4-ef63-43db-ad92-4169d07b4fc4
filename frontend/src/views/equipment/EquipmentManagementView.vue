<!--
  设备管理页面
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 15:30:28 +08:00; Reason: 基于设备管理模块API开发完整的设备列表页面; Principle_Applied: 功能完整性、用户体验优化;}}
-->

<template>
  <div class="equipment-management">
    <!-- 搜索区域 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline" :model="searchForm" class="search-form">
        <a-form-item label="设备名称">
          <a-input
            v-model:value="searchForm.search"
            placeholder="请输入设备名称或SN号"
            allow-clear
            style="width: 200px"
            @press-enter="handleSearch"
          />
        </a-form-item>

        <a-form-item label="机型">
          <a-select
            v-model:value="searchForm.deviceModelId"
            placeholder="请选择机型"
            allow-clear
            style="width: 150px"
            :options="deviceModelOptions"
            :loading="optionsLoading"
          />
        </a-form-item>

        <a-form-item label="车间产线">
          <a-tree-select
            v-model:value="searchForm.productionLineId"
            placeholder="请选择车间产线"
            allow-clear
            style="width: 150px"
            :tree-data="productionLineOptions"
            :loading="optionsLoading"
            tree-default-expand-all
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          />
        </a-form-item>

        <a-form-item label="分组">
          <a-select
            v-model:value="searchForm.groupId"
            placeholder="请选择分组"
            allow-clear
            style="width: 120px"
            :options="groupOptions"
            :loading="optionsLoading"
          />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 设备列表区域 -->
    <a-card :bordered="false" class="table-card">
      <template #title>
        <span>设备列表</span>
        <a-tag color="blue" style="margin-left: 8px">
          共 {{ pagination.total }} 台设备
        </a-tag>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <PlusOutlined />
            新增设备
          </a-button>
          <a-button @click="handleDeviceModel">
            <UnorderedListOutlined />
            机型管理
          </a-button>
          <a-button @click="handleWorkshopLine">
            <ApartmentOutlined />
            车间产线
          </a-button>
          <a-button @click="handleGrouping">
            <GroupOutlined />
            分组
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="deviceList"
        :loading="loading"
        :pagination="tablePagination"
        :scroll="{ x: 1200 }"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 设备名称列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="device-name">
              <strong>{{ record.name }}</strong>
              <div class="device-code" v-if="record.code">
                编号: {{ record.code }}
              </div>
            </div>
          </template>

          <!-- 设备类型列 -->
          <template v-else-if="column.key === 'deviceModel'">
            <span v-if="record.deviceModelInfo">
              {{ record.deviceModelInfo.name }}
              <div class="device-model-code">
                {{ record.deviceModelInfo.code }}
              </div>
            </span>
            <span v-else class="text-gray">未设置</span>
          </template>

          <!-- WiFi状态列 -->
          <template v-else-if="column.key === 'wifiState'">
            <a-tag :color="getWifiStateColor(record.wifiState)">
              {{ getWifiStateText(record.wifiState) }}
            </a-tag>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'createdAt'">
            {{ formatDateTime(record.createdAt) }}
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="showDetailModal(record)">
                <EyeOutlined />
                详情
              </a-button>
              <a-button type="link" size="small" @click="showEditModal(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                @click="showDeleteConfirm(record)"
              >
                <DeleteOutlined />
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 设备详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="设备详情"
      width="800px"
      :footer="null"
    >
      <DeviceDetailModal
        v-if="detailModalVisible && selectedDevice"
        :device="selectedDevice"
        @close="detailModalVisible = false"
      />
    </a-modal>

    <!-- 新增/编辑设备弹窗 -->
    <a-modal
      v-model:open="formModalVisible"
      :title="isEdit ? '编辑设备' : '新增设备'"
      width="800px"
      :footer="null"
    >
      <DeviceFormModal
        v-if="formModalVisible"
        :device="selectedDevice"
        :is-edit="isEdit"
        @success="handleFormSuccess"
        @cancel="formModalVisible = false"
      />
    </a-modal>

    <!-- 车间产线管理弹框 -->
    <TagManagementModal
      v-model:visible="workshopLineModalVisible"
      :tag-type="1"
      title="车间产线管理"
      @cancel="workshopLineModalVisible = false"
    />

    <!-- 分组管理弹框 -->
    <TagManagementModal
      v-model:visible="groupingModalVisible"
      :tag-type="2"
      title="机器分组管理"
      @cancel="groupingModalVisible = false"
    />

    <!-- 设备机型管理弹框 -->
    <DeviceModelManagement
      v-model:visible="deviceModelModalVisible"
      @cancel="deviceModelModalVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ApartmentOutlined,
  GroupOutlined,
  UnorderedListOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import { deviceApi, deviceUtils } from '../../api/device'
import type {
  Device,
  DeviceListQuery,
  DeviceListResponse,
  DeviceSearchOptions
} from '../../types/device'
import DeviceDetailModal from '../../components/equipment/DeviceDetailModal.vue'
import DeviceFormModal from '../../components/equipment/DeviceFormModal.vue'
import TagManagementModal from '../../components/tag/TagManagementModal.vue'
import DeviceModelManagement from '../../components/equipment/DeviceModelManagement.vue'
import dayjs from 'dayjs'

// ==================== 响应式数据 ====================

// 设备列表数据
const deviceList = ref<Device[]>([])
const loading = ref(false)

// 搜索表单
const searchForm = reactive<DeviceListQuery>({
  search: '',
  deviceModelId: undefined,
  productionLineId: undefined,
  groupId: undefined,
  vendor: undefined,
  wifiState: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 弹窗状态
const detailModalVisible = ref(false)
const formModalVisible = ref(false)
const isEdit = ref(false)

// 标签管理弹窗状态
const workshopLineModalVisible = ref(false)
const groupingModalVisible = ref(false)
const deviceModelModalVisible = ref(false)
const selectedDevice = ref<Device | null>(null)

// 搜索选项
const deviceModelOptions = ref<Array<{ label: string; value: number }>>([])
const productionLineOptions = ref<any[]>([])
const groupOptions = ref<Array<{ label: string; value: number }>>([])
const vendorOptions = ref<Array<{ label: string; value: string }>>([])
const wifiStateOptions = ref([
  { label: '已连接', value: 'connected' },
  { label: '未连接', value: 'disconnected' },
  { label: '连接中', value: 'connecting' },
  { label: '连接错误', value: 'error' },
  { label: '未知', value: 'unknown' }
])

// 选项加载状态
const optionsLoading = ref(false)

// ==================== 计算属性 ====================

// 表格分页配置
const tablePagination = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: pagination.showSizeChanger,
  showQuickJumper: pagination.showQuickJumper,
  showTotal: pagination.showTotal,
  pageSizeOptions: ['10', '20', '50', '100']
}))

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '设备名称',
    key: 'name',
    dataIndex: 'name',
    width: 200,
    fixed: 'left'
  },
  {
    title: 'SN号',
    dataIndex: 'sn',
    width: 150,
    ellipsis: true
  },
  {
    title: 'MAC地址',
    dataIndex: 'mac',
    width: 140,
    ellipsis: true
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    width: 120
  },
  {
    title: '设备类型',
    key: 'deviceModel',
    width: 120,
    ellipsis: true
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
    width: 120,
    ellipsis: true
  },
  {
    title: 'WiFi状态',
    key: 'wifiState',
    dataIndex: 'wifiState',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'createdAt',
    dataIndex: 'createdAt',
    width: 160,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 180,
    fixed: 'right',
    align: 'center'
  }
]

// ==================== 方法 ====================

/**
 * 获取设备列表
 */
const fetchDeviceList = async () => {
  try {
    loading.value = true

    const params: DeviceListQuery = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      search: searchForm.search || undefined,
      deviceModelId: searchForm.deviceModelId || undefined,
      productionLineId: searchForm.productionLineId || undefined,
      groupId: searchForm.groupId || undefined,
      vendor: searchForm.vendor || undefined,
      wifiState: searchForm.wifiState || undefined
    }

    const response: DeviceListResponse = await deviceApi.getDeviceList(params)

    deviceList.value = response.devices
    pagination.total = response.total

    // 更新厂商选项（从设备数据中提取）
    updateVendorOptions(response.devices)

  } catch (error) {
    console.error('获取设备列表失败:', error)
    message.error('获取设备列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 获取搜索选项数据
 */
const fetchSearchOptions = async () => {
  try {
    optionsLoading.value = true
    const options: DeviceSearchOptions = await deviceApi.getSearchOptions()

    // 更新设备类型选项
    deviceModelOptions.value = options.deviceModels.map(item => ({
      label: item.name,
      value: item.id
    }))

    // 更新车间产线选项（树形结构）
    productionLineOptions.value = options.productionLines

    // 更新分组选项
    groupOptions.value = options.groups.map(item => ({
      label: item.name,
      value: item.id
    }))

  } catch (error) {
    console.error('获取搜索选项失败:', error)
    message.error('获取搜索选项失败，请稍后重试')
  } finally {
    optionsLoading.value = false
  }
}

/**
 * 更新厂商选项（从设备数据中提取）
 */
const updateVendorOptions = (devices: Device[]) => {
  const vendors = [...new Set(devices.map(d => d.vendor).filter(Boolean))]
  vendorOptions.value = vendors.map(vendor => ({
    label: vendor!,
    value: vendor!
  }))
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  pagination.current = 1
  fetchDeviceList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    deviceModelId: undefined,
    productionLineId: undefined,
    groupId: undefined,
    vendor: undefined,
    wifiState: undefined
  })
  pagination.current = 1
  fetchDeviceList()
}

/**
 * 表格变化处理
 */
const handleTableChange: TableProps['onChange'] = (pag) => {
  if (pag) {
    pagination.current = pag.current || 1
    pagination.pageSize = pag.pageSize || 10
  }
  fetchDeviceList()
}

/**
 * 显示新增弹窗
 */
const showCreateModal = () => {
  selectedDevice.value = null
  isEdit.value = false
  formModalVisible.value = true
}

/**
 * 显示编辑弹窗
 */
const showEditModal = (device: any) => {
  selectedDevice.value = device as Device
  isEdit.value = true
  formModalVisible.value = true
}

/**
 * 显示详情弹窗
 */
const showDetailModal = (device: any) => {
  selectedDevice.value = device as Device
  detailModalVisible.value = true
}

/**
 * 显示删除确认
 */
const showDeleteConfirm = (device: any) => {
  const deviceData = device as Device
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除设备"${deviceData.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: () => handleDelete(deviceData)
  })
}

/**
 * 删除设备
 */
const handleDelete = async (device: Device) => {
  try {
    await deviceApi.deleteDevice(device.id)
    message.success('删除设备成功')
    fetchDeviceList()
  } catch (error) {
    console.error('删除设备失败:', error)
    message.error('删除设备失败，请稍后重试')
  }
}

/**
 * 车间产线管理
 */
const handleWorkshopLine = () => {
  workshopLineModalVisible.value = true
}

/**
 * 分组管理
 */
const handleGrouping = () => {
  groupingModalVisible.value = true
}

/**
 * 设备机型管理
 */
const handleDeviceModel = () => {
  deviceModelModalVisible.value = true
}

/**
 * 表单操作成功回调
 */
const handleFormSuccess = () => {
  formModalVisible.value = false
  fetchDeviceList()
}

/**
 * 获取WiFi状态显示文本
 */
const getWifiStateText = (state?: string): string => {
  return deviceUtils.getWifiStateText(state)
}

/**
 * 获取WiFi状态颜色
 */
const getWifiStateColor = (state?: string): string => {
  return deviceUtils.getWifiStateColor(state)
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string): string => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// ==================== 生命周期 ====================

onMounted(() => {
  fetchSearchOptions()
  fetchDeviceList()
})
</script>

<style scoped>
.equipment-management {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.search-card {
  margin-bottom: 16px;
}

.search-form .ant-form-item {
  margin-bottom: 16px;
}

.table-card :deep(.ant-card-head-title) {
  display: flex;
  align-items: center;
}

.device-name .device-code {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.device-model-code {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.text-gray {
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .equipment-management {
    padding: 8px;
  }

  .search-form .ant-form-item {
    margin-bottom: 8px;
  }

  :deep(.ant-table) {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .search-form .ant-form-item {
    width: 100%;
  }

  .search-form .ant-form-item .ant-input,
  .search-form .ant-form-item .ant-select {
    width: 100% !important;
  }
}
</style>
