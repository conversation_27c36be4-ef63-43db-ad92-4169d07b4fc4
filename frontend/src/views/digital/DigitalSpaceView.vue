<!--
  数字空间 - 花样管理页面
-->

<template>
  <div class="digital-space">
    <!-- 搜索区域 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline" :model="searchForm" class="search-form">
        <a-form-item label="花样名称">
          <a-input
            v-model:value="searchForm.search"
            placeholder="请输入花样名称、编号或备注"
            allow-clear
            style="width: 250px"
            @press-enter="handleSearch"
          />
        </a-form-item>

        <a-form-item label="花样分组">
          <a-select
            v-model:value="searchForm.groupId"
            placeholder="请选择花样分组"
            allow-clear
            style="width: 200px"
            :loading="groupsLoading"
            @change="handleGroupChange"
          >
            <a-select-option
              v-for="group in searchOptions.groups"
              :key="group.id"
              :value="group.id"
            >
              {{ group.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="文件类型">
          <a-select
            v-model:value="searchForm.fileType"
            placeholder="请选择文件类型"
            allow-clear
            style="width: 150px"
            @change="handleFileTypeChange"
          >
            <a-select-option
              v-for="fileType in searchOptions.fileTypes"
              :key="fileType.value"
              :value="fileType.value"
            >
              {{ fileType.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 花样列表区域 -->
    <a-card :bordered="false" class="table-card">
      <template #title>
        <span>花样列表</span>
        <a-tag color="blue" style="margin-left: 8px">
          共 {{ pagination.total }} 个花样
        </a-tag>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleCreate">
            <PlusOutlined />
            新增花样
          </a-button>
          <a-button @click="handleGroupManagement">
            <GroupOutlined />
            分组管理
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="patterns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1500 }"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 花样编号 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'code'">
            <span class="pattern-code">{{ record.code || '-' }}</span>
          </template>

          <!-- 花样名称 -->
          <template v-else-if="column.key === 'name'">
            <div class="pattern-name">
              <a-button type="link" @click="handleView(record)" class="name-link">
                {{ record.name }}
              </a-button>
              <div class="pattern-meta">
                <a-tag v-if="record.fileType" size="small">
                  {{ record.fileType.toUpperCase() }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 分组 -->
          <template v-else-if="column.key === 'group'">
            <a-tag v-if="record.group" color="blue">
              {{ record.group.name }}
            </a-tag>
            <span v-else class="text-gray">未分组</span>
          </template>

          <!-- 花样属性 -->
          <template v-else-if="column.key === 'properties'">
            <div class="pattern-properties">
              <div v-if="record.stitch" class="property-item">
                <span class="property-label">针数:</span>
                <span class="property-value">{{ record.stitch.toLocaleString() }}</span>
              </div>
              <div v-if="record.colors" class="property-item">
                <span class="property-label">颜色:</span>
                <span class="property-value">{{ record.colors }}</span>
              </div>
              <div v-if="record.width && record.height" class="property-item">
                <span class="property-label">尺寸:</span>
                <span class="property-value">{{ record.width }}×{{ record.height }}</span>
              </div>
            </div>
          </template>

          <!-- 创建信息 -->
          <template v-else-if="column.key === 'createInfo'">
            <div class="create-info">
              <div v-if="record.createUser" class="creator">
                <UserOutlined />
                {{ record.createUser }}
              </div>
              <div class="create-time">
                {{ formatDateTime(record.createdAt) }}
              </div>
            </div>
          </template>

          <!-- 操作 -->
          <template v-else-if="column.key === 'actions'">
            <div class="action-buttons">
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个花样吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 花样详情模态框 -->
    <PatternDetailModal
      v-model:visible="detailModal.visible"
      :pattern="detailModal.pattern"
      @edit="handleEditFromDetail"
    />

    <!-- 花样表单模态框 -->
    <PatternFormModal
      v-model:visible="formModal.visible"
      :pattern="formModal.pattern"
      :mode="formModal.mode"
      :groups="searchOptions.groups"
      @success="handleFormSuccess"
    />

    <!-- 分组管理模态框 -->
    <PatternGroupManagement
      v-model:visible="groupManagementVisible"
      @success="handleGroupManagementSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  UserOutlined,
  ReloadOutlined,
  GroupOutlined
} from '@ant-design/icons-vue'
import { patternApi } from '@/api/pattern'
import type {
  Pattern,
  PatternListQuery,
  PatternSearchOptions,
  PatternFilterForm
} from '@/types/pattern'
import { formatDateTime } from '@/utils/date'
import PatternDetailModal from '@/components/digital/PatternDetailModal.vue'
import PatternFormModal from '@/components/digital/PatternFormModal.vue'
import PatternGroupManagement from '@/components/digital/PatternGroupManagement.vue'
import { generateMockPatterns, generateMockSearchOptions } from '@/utils/mockData'

// 响应式数据
const loading = ref(false)
const groupsLoading = ref(false)
const patterns = ref<Pattern[]>([])
const searchOptions = ref<PatternSearchOptions>({
  groups: [],
  fileTypes: [],
  createUsers: []
})

// 模态框状态
const detailModal = reactive({
  visible: false,
  pattern: null as Pattern | null
})

const formModal = reactive({
  visible: false,
  pattern: null as Pattern | null,
  mode: 'create' as 'create' | 'edit'
})

// 分组管理弹窗状态
const groupManagementVisible = ref(false)

// 搜索表单
const searchForm = reactive<PatternFilterForm>({
  search: '',
  groupId: undefined,
  fileType: undefined,
  createUser: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '花样编号',
    dataIndex: 'code',
    key: 'code',
    width: 120,
    ellipsis: true
  },
  {
    title: '花样名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '分组',
    dataIndex: 'group',
    key: 'group',
    width: 120,
    align: 'center' as const
  },
  {
    title: '花样属性',
    key: 'properties',
    width: 200
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 150,
    ellipsis: true
  },
  {
    title: '创建信息',
    key: 'createInfo',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right' as const,
    align: 'center' as const
  }
]

// 方法
/**
 * 获取花样列表
 */
const fetchPatterns = async () => {
  try {
    loading.value = true

    // 统一使用真实API，确保数据一致性
    const params: PatternListQuery = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    const response = await patternApi.getPatternList(params)
    patterns.value = response.patterns
    pagination.total = response.total
  } catch (error) {
    console.error('获取花样列表失败:', error)
    message.error('获取花样列表失败')

    // 如果API调用失败，使用模拟数据作为后备
    try {
      const mockPatterns = generateMockPatterns(50)
      let filteredPatterns = mockPatterns

      // 搜索过滤
      if (searchForm.search) {
        filteredPatterns = filteredPatterns.filter((p: Pattern) =>
          p.name.includes(searchForm.search!) ||
          p.code?.includes(searchForm.search!) ||
          p.remark?.includes(searchForm.search!)
        )
      }

      // 分组过滤
      if (searchForm.groupId) {
        filteredPatterns = filteredPatterns.filter((p: Pattern) => p.groupId === searchForm.groupId)
      }

      // 分页
      const start = (pagination.current - 1) * pagination.pageSize
      const end = start + pagination.pageSize
      patterns.value = filteredPatterns.slice(start, end)
      pagination.total = filteredPatterns.length
    } catch (fallbackError) {
      console.error('模拟数据生成失败:', fallbackError)
    }
  } finally {
    loading.value = false
  }
}

/**
 * 获取搜索选项
 */
const fetchSearchOptions = async () => {
  try {
    groupsLoading.value = true

    // 🔥 使用统一的搜索选项接口，就像设备列表一样
    const options = await patternApi.getSearchOptions()

    // 调试信息：显示获取到的选项数量
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 获取到花样搜索选项:`, {
        groups: options.groups.length,
        fileTypes: options.fileTypes.length,
        createUsers: options.createUsers.length
      })
    }

    searchOptions.value = options

    // 检查当前选中的分组是否还存在（可能被禁用了）
    if (searchForm.groupId && !options.groups.find((group: { id: number; name: string }) => group.id === searchForm.groupId)) {
      // 如果当前选中的分组不在新的分组列表中，说明被禁用了，清空选择
      const previousGroupId = searchForm.groupId
      searchForm.groupId = undefined

      // 显示友好提示
      message.info('所选择的花样分组已被禁用，已自动清空筛选条件')
      if (process.env.NODE_ENV === 'development') {
        console.log(`分组 ID ${previousGroupId} 已被禁用，自动清空选择`)
      }

      // 自动触发搜索以更新列表
      handleSearch()
    }
  } catch (error) {
    console.error('获取搜索选项失败:', error)
    // 如果API调用失败，使用模拟数据作为后备
    searchOptions.value = generateMockSearchOptions()
  } finally {
    groupsLoading.value = false
  }
}



/**
 * 搜索处理
 */
const handleSearch = () => {
  pagination.current = 1
  fetchPatterns()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    groupId: undefined,
    fileType: undefined,
    createUser: undefined
  })
  pagination.current = 1
  fetchPatterns()
}

/**
 * 分组变化处理
 */
const handleGroupChange = (value: number | undefined) => {
  searchForm.groupId = value
  // 自动触发搜索
  handleSearch()
}

/**
 * 文件类型变化处理
 */
const handleFileTypeChange = (value: string | undefined) => {
  searchForm.fileType = value
  // 自动触发搜索
  handleSearch()
}

/**
 * 表格变化处理
 */
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchPatterns()
}

/**
 * 创建花样
 */
const handleCreate = () => {
  formModal.visible = true
  formModal.pattern = null
  formModal.mode = 'create'
}

/**
 * 查看花样详情
 */
const handleView = (pattern: Pattern) => {
  detailModal.visible = true
  detailModal.pattern = pattern
}

/**
 * 编辑花样
 */
const handleEdit = (pattern: Pattern) => {
  formModal.visible = true
  formModal.pattern = pattern
  formModal.mode = 'edit'
}

/**
 * 从详情页面编辑
 */
const handleEditFromDetail = (pattern: Pattern) => {
  detailModal.visible = false
  handleEdit(pattern)
}

/**
 * 表单提交成功
 */
const handleFormSuccess = () => {
  fetchPatterns()
}

/**
 * 分组管理
 */
const handleGroupManagement = () => {
  groupManagementVisible.value = true
}

/**
 * 分组管理成功回调
 */
const handleGroupManagementSuccess = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('分组管理操作成功，开始刷新搜索选项...')
  }
  // 刷新搜索选项，更新分组列表
  fetchSearchOptions()
  // 同时刷新花样列表，以防当前选中的分组被禁用
  fetchPatterns()
}

/**
 * 删除花样
 */
const handleDelete = async (pattern: Pattern) => {
  try {
    await patternApi.deletePattern(pattern.id)
    message.success('删除成功')
    fetchPatterns()
  } catch (error) {
    console.error('删除花样失败:', error)
    message.error('删除失败')
  }
}

// 生命周期
onMounted(() => {
  fetchPatterns()
  fetchSearchOptions()
})
</script>

<style scoped>
.digital-space {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}



/* 搜索卡片 */
.search-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-card :deep(.ant-card-body) {
  padding: 20px;
}

.search-form {
  width: 100%;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 16px;
  margin-right: 24px;
}

.search-form :deep(.ant-form-item-label) {
  font-weight: 500;
  color: #262626;
}

.search-form :deep(.ant-select),
.search-form :deep(.ant-input) {
  border-radius: 6px;
}

.search-form :deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
}

/* 表格卡片 */
.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-card :deep(.ant-card-body) {
  padding: 0;
}

.table-card :deep(.ant-table) {
  border-radius: 8px;
}

/* 表格内容样式 */
.pattern-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1890ff;
  background: #f0f8ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.pattern-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name-link {
  padding: 0;
  height: auto;
  font-weight: 500;
  text-align: left;
}

.pattern-meta {
  display: flex;
  gap: 4px;
}

.pattern-properties {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.property-item {
  display: flex;
  gap: 4px;
}

.property-label {
  color: #8c8c8c;
  min-width: 32px;
}

.property-value {
  color: #262626;
  font-weight: 500;
}

.create-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.creator {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #1890ff;
}

.create-time {
  color: #8c8c8c;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.text-gray {
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-section :deep(.ant-col) {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .digital-space {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-form {
    flex-direction: column;
  }

  .search-card :deep(.ant-form-item) {
    margin-bottom: 16px;
    margin-right: 0;
  }

  .search-card :deep(.ant-input),
  .search-card :deep(.ant-select) {
    width: 100% !important;
  }

  .search-card :deep(.ant-space) {
    width: 100%;
    justify-content: center;
  }
}
</style>
