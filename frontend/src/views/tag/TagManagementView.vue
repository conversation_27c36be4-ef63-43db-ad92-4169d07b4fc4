<!--
  标签管理页面
  {{CHENGQI: Action: Added; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 标签管理模块开发, 创建标签管理主页面; Principle_Applied: 页面组件化;}}
-->

<template>
  <div class="tag-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">标签管理</h2>
        <p class="page-description">管理不同类型的标签，支持车间产线的层级结构</p>
      </div>
    </div>

    <!-- 标签类型切换 -->
    <div class="type-tabs">
      <a-tabs 
        v-model:activeKey="activeType" 
        @change="handleTypeChange"
        type="card"
      >
        <a-tab-pane 
          v-for="type in tagTypes" 
          :key="type.value" 
          :tab="type.label"
        >
          <!-- 搜索和操作区域 -->
          <div class="search-section">
            <div class="search-left">
              <a-input-search
                v-model:value="searchForm.search"
                placeholder="搜索标签名称"
                style="width: 300px"
                @search="handleSearch"
                @change="handleSearchChange"
                allow-clear
              />
              
              <a-select
                v-model:value="searchForm.status"
                placeholder="选择状态"
                style="width: 120px; margin-left: 12px"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option :value="TagStatus.ENABLED">正常</a-select-option>
                <a-select-option :value="TagStatus.DISABLED">禁用</a-select-option>
              </a-select>
            </div>
            
            <div class="search-right">
              <a-space>
                <a-button @click="handleRefresh" :loading="loading">
                  <template #icon><ReloadOutlined /></template>
                  刷新
                </a-button>
                
                <a-button 
                  type="primary" 
                  @click="showCreateModal"
                  :disabled="!canCreate"
                >
                  <template #icon><PlusOutlined /></template>
                  新增{{ getCurrentTypeLabel() }}
                </a-button>
                
                <a-dropdown v-if="selectedRowKeys.length > 0">
                  <a-button>
                    批量操作
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="handleBatchAction">
                      <a-menu-item key="enable">
                        <CheckOutlined />
                        批量启用
                      </a-menu-item>
                      <a-menu-item key="disable">
                        <StopOutlined />
                        批量禁用
                      </a-menu-item>
                      <a-menu-item key="delete" danger>
                        <DeleteOutlined />
                        批量删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                
                <a-button @click="handleExport">
                  <template #icon><ExportOutlined /></template>
                  导出
                </a-button>
              </a-space>
            </div>
          </div>

          <!-- 内容区域 -->
          <div class="content-section">
            <!-- 车间产线 - 树形结构 -->
            <div v-if="type.supportHierarchy" class="tree-container">
              <a-spin :spinning="loading">
                <a-tree
                  v-if="treeData.length > 0"
                  :tree-data="treeData"
                  :expanded-keys="expandedKeys"
                  :selected-keys="selectedKeys"
                  :checked-keys="checkedKeys"
                  checkable
                  show-line
                  @expand="handleTreeExpand"
                  @select="handleTreeSelect"
                  @check="handleTreeCheck"
                >
                  <template #title="{ title, key, status, level }">
                    <div class="tree-node-title">
                      <span class="node-name">{{ title }}</span>
                      <a-tag 
                        :color="tagUtils.getTagStatusColor(status)" 
                        size="small"
                        class="node-status"
                      >
                        {{ tagUtils.getTagStatusName(status) }}
                      </a-tag>
                      <span class="node-level">L{{ level }}</span>
                      
                      <div class="node-actions">
                        <a-button 
                          type="text" 
                          size="small" 
                          @click.stop="showCreateChildModal(key)"
                          title="添加子级"
                        >
                          <PlusOutlined />
                        </a-button>
                        <a-button 
                          type="text" 
                          size="small" 
                          @click.stop="showEditModal(key)"
                          title="编辑"
                        >
                          <EditOutlined />
                        </a-button>
                        <a-button 
                          type="text" 
                          size="small" 
                          danger
                          @click.stop="showDeleteConfirm(key)"
                          title="删除"
                        >
                          <DeleteOutlined />
                        </a-button>
                      </div>
                    </div>
                  </template>
                </a-tree>
                
                <a-empty v-else description="暂无数据" />
              </a-spin>
            </div>

            <!-- 其他类型 - 表格列表 -->
            <div v-else class="table-container">
              <a-table
                :columns="tableColumns"
                :data-source="tagList"
                :loading="loading"
                :pagination="pagination"
                :row-selection="rowSelection"
                row-key="id"
                @change="handleTableChange"
              >
                <!-- 状态列 -->
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-tag :color="tagUtils.getTagStatusColor(record.status)">
                      {{ tagUtils.getTagStatusName(record.status) }}
                    </a-tag>
                  </template>
                  
                  <!-- 创建时间列 -->
                  <template v-else-if="column.key === 'createdAt'">
                    {{ formatDateTime(record.createdAt) }}
                  </template>
                  
                  <!-- 操作列 -->
                  <template v-else-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="showEditModal(record.id)">
                        <EditOutlined />
                        编辑
                      </a-button>
                      
                      <a-button 
                        type="link" 
                        size="small" 
                        @click="handleToggleStatus(record)"
                      >
                        <CheckOutlined v-if="record.status === TagStatus.DISABLED" />
                        <StopOutlined v-else />
                        {{ record.status === TagStatus.ENABLED ? '禁用' : '启用' }}
                      </a-button>
                      
                      <a-button 
                        type="link" 
                        size="small" 
                        danger 
                        @click="showDeleteConfirm(record.id)"
                      >
                        <DeleteOutlined />
                        删除
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 标签表单弹窗 -->
    <TagFormModal
      v-model:visible="formModalVisible"
      :tag="selectedTag"
      :is-edit="isEdit"
      :tag-type="activeType"
      :parent-options="parentOptions"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  ReloadOutlined,
  PlusOutlined,
  DownOutlined,
  CheckOutlined,
  StopOutlined,
  DeleteOutlined,
  ExportOutlined,
  EditOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import { tagApi, tagUtils } from '../../api/tag'
import type {
  Tag,
  TagListQuery,
  TagTreeNode,
  TagType,
  TagStatus,
  TagTypeOption
} from '../../types/tag'
import TagFormModal from '../../components/tag/TagFormModal.vue'
import dayjs from 'dayjs'

// 导入枚举（作为值使用）
import { TagType, TagStatus } from '../../types/tag'

// ==================== 响应式数据 ====================

// 当前激活的标签类型
const activeType = ref<TagType>(TagType.WORKSHOP_LINE)

// 标签类型列表
const tagTypes = ref<TagTypeOption[]>([])

// 加载状态
const loading = ref(false)

// 搜索表单
const searchForm = reactive<TagListQuery>({
  search: '',
  status: undefined
})

// 标签列表数据
const tagList = ref<Tag[]>([])

// 树形数据
const treeData = ref<TagTreeNode[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格选择
const selectedRowKeys = ref<number[]>([])

// 树形组件状态
const expandedKeys = ref<number[]>([])
const selectedKeys = ref<number[]>([])
const checkedKeys = ref<number[]>([])

// 表单弹窗
const formModalVisible = ref(false)
const selectedTag = ref<Tag | null>(null)
const isEdit = ref(false)

// 权限控制
const canCreate = ref(true)
const canEdit = ref(true)
const canDelete = ref(true)

// ==================== 计算属性 ====================

// 当前类型配置
const currentTypeConfig = computed(() => {
  return tagTypes.value.find(t => t.value === activeType.value)
})

// 父级选项（用于表单）
const parentOptions = computed(() => {
  if (!currentTypeConfig.value?.supportHierarchy) {
    return []
  }
  
  return treeData.value.map(tag => ({
    label: tag.name,
    value: tag.id,
    children: buildParentOptions(tag.children || [])
  }))
})

// 表格列配置
const tableColumns: TableColumnsType = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    sorter: true
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'createdAt',
    dataIndex: 'createdAt',
    width: 160,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    align: 'center'
  }
]

// 表格行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: Tag) => ({
    disabled: !canDelete.value
  })
}))

// ==================== 方法 ====================

/**
 * 获取当前类型标签
 */
const getCurrentTypeLabel = (): string => {
  return currentTypeConfig.value?.label || '标签'
}

/**
 * 构建父级选项
 */
const buildParentOptions = (children: TagTreeNode[]): any[] => {
  return children.map(child => ({
    label: child.name,
    value: child.id,
    children: buildParentOptions(child.children || [])
  }))
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string): string => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取标签类型列表
 */
const fetchTagTypes = async () => {
  try {
    tagTypes.value = await tagApi.getTagTypes()
  } catch (error) {
    console.error('获取标签类型失败:', error)
    message.error('获取标签类型失败')
  }
}

/**
 * 获取标签数据
 */
const fetchTagData = async () => {
  try {
    loading.value = true

    if (currentTypeConfig.value?.supportHierarchy) {
      // 获取树形数据
      treeData.value = await tagApi.getTagTree(activeType.value)
    } else {
      // 获取列表数据
      const params: TagListQuery = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        type: activeType.value,
        search: searchForm.search || undefined,
        status: searchForm.status
      }

      const response = await tagApi.getTagList(params)
      tagList.value = response.tags
      pagination.total = response.total
    }
  } catch (error) {
    console.error('获取标签数据失败:', error)
    message.error('获取标签数据失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理类型切换
 */
const handleTypeChange = (type: string) => {
  activeType.value = parseInt(type) as TagType
  resetSearchForm()
  resetSelection()
  fetchTagData()
}

/**
 * 重置搜索表单
 */
const resetSearchForm = () => {
  searchForm.search = ''
  searchForm.status = undefined
  pagination.current = 1
}

/**
 * 重置选择状态
 */
const resetSelection = () => {
  selectedRowKeys.value = []
  selectedKeys.value = []
  checkedKeys.value = []
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.current = 1
  fetchTagData()
}

/**
 * 处理搜索输入变化
 */
const handleSearchChange = () => {
  // 防抖处理
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    handleSearch()
  }, 500)
}

let searchTimeout: NodeJS.Timeout

/**
 * 处理刷新
 */
const handleRefresh = () => {
  resetSearchForm()
  resetSelection()
  fetchTagData()
}

/**
 * 处理表格变化
 */
const handleTableChange: TableProps['onChange'] = (pag) => {
  if (pag) {
    pagination.current = pag.current || 1
    pagination.pageSize = pag.pageSize || 20
  }
  fetchTagData()
}

/**
 * 处理树形展开
 */
const handleTreeExpand = (keys: number[]) => {
  expandedKeys.value = keys
}

/**
 * 处理树形选择
 */
const handleTreeSelect = (keys: number[]) => {
  selectedKeys.value = keys
}

/**
 * 处理树形勾选
 */
const handleTreeCheck = (keys: number[]) => {
  checkedKeys.value = keys
}

/**
 * 显示创建弹窗
 */
const showCreateModal = () => {
  selectedTag.value = null
  isEdit.value = false
  formModalVisible.value = true
}

/**
 * 显示创建子级弹窗
 */
const showCreateChildModal = (parentId: number) => {
  selectedTag.value = { pid: parentId } as Tag
  isEdit.value = false
  formModalVisible.value = true
}

/**
 * 显示编辑弹窗
 */
const showEditModal = async (id: number) => {
  try {
    selectedTag.value = await tagApi.getTagById(id)
    isEdit.value = true
    formModalVisible.value = true
  } catch (error) {
    console.error('获取标签详情失败:', error)
    message.error('获取标签详情失败')
  }
}

/**
 * 显示删除确认
 */
const showDeleteConfirm = (id: number) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个标签吗？此操作不可恢复。',
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: () => handleDelete(id)
  })
}

/**
 * 处理删除
 */
const handleDelete = async (id: number) => {
  try {
    await tagApi.deleteTag(id)
    message.success('删除标签成功')
    fetchTagData()
  } catch (error) {
    console.error('删除标签失败:', error)
    message.error('删除标签失败，请稍后重试')
  }
}

/**
 * 处理状态切换
 */
const handleToggleStatus = async (tag: Tag) => {
  try {
    const newStatus = tag.status === TagStatus.ENABLED ? TagStatus.DISABLED : TagStatus.ENABLED
    await tagApi.updateTagStatus(tag.id, newStatus)
    message.success(`${newStatus === TagStatus.ENABLED ? '启用' : '禁用'}标签成功`)
    fetchTagData()
  } catch (error) {
    console.error('更新标签状态失败:', error)
    message.error('更新标签状态失败，请稍后重试')
  }
}

/**
 * 处理批量操作
 */
const handleBatchAction = ({ key }: { key: string }) => {
  const ids = currentTypeConfig.value?.supportHierarchy ? checkedKeys.value : selectedRowKeys.value

  if (ids.length === 0) {
    message.warning('请先选择要操作的标签')
    return
  }

  switch (key) {
    case 'enable':
      handleBatchStatus(ids, TagStatus.ENABLED)
      break
    case 'disable':
      handleBatchStatus(ids, TagStatus.DISABLED)
      break
    case 'delete':
      handleBatchDelete(ids)
      break
  }
}

/**
 * 批量更新状态
 */
const handleBatchStatus = async (ids: number[], status: TagStatus) => {
  try {
    await Promise.all(ids.map(id => tagApi.updateTagStatus(id, status)))
    message.success(`批量${status === TagStatus.ENABLED ? '启用' : '禁用'}成功`)
    resetSelection()
    fetchTagData()
  } catch (error) {
    console.error('批量更新状态失败:', error)
    message.error('批量更新状态失败，请稍后重试')
  }
}

/**
 * 批量删除
 */
const handleBatchDelete = (ids: number[]) => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${ids.length} 个标签吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await Promise.all(ids.map(id => tagApi.deleteTag(id)))
        message.success('批量删除成功')
        resetSelection()
        fetchTagData()
      } catch (error) {
        console.error('批量删除失败:', error)
        message.error('批量删除失败，请稍后重试')
      }
    }
  })
}

/**
 * 处理导出
 */
const handleExport = async () => {
  try {
    let exportData: Tag[] = []

    if (currentTypeConfig.value?.supportHierarchy) {
      exportData = tagUtils.flattenTagTree(treeData.value)
    } else {
      // 获取所有数据用于导出
      const response = await tagApi.getTagList({
        type: activeType.value,
        search: searchForm.search || undefined,
        status: searchForm.status,
        page: 1,
        pageSize: 10000
      })
      exportData = response.tags
    }

    const filename = `${getCurrentTypeLabel()}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    tagUtils.exportToCSV(exportData, filename)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请稍后重试')
  }
}

/**
 * 处理表单成功
 */
const handleFormSuccess = () => {
  formModalVisible.value = false
  fetchTagData()
}

/**
 * 处理表单取消
 */
const handleFormCancel = () => {
  formModalVisible.value = false
  selectedTag.value = null
}

// ==================== 生命周期 ====================

onMounted(async () => {
  await fetchTagTypes()
  await fetchTagData()
})

// 监听类型变化
watch(activeType, () => {
  fetchTagData()
})
</script>

<style scoped>
.tag-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-description {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.type-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.search-left {
  display: flex;
  align-items: center;
}

.search-right {
  display: flex;
  align-items: center;
}

.content-section {
  padding: 24px;
  min-height: 400px;
}

.tree-container {
  background: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  padding: 16px;
}

.tree-node-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-name {
  flex: 1;
  font-weight: 500;
}

.node-status {
  margin: 0;
}

.node-level {
  font-size: 12px;
  color: #8c8c8c;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node-title:hover .node-actions {
  opacity: 1;
}

.table-container {
  background: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-management {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
    margin-bottom: 16px;
  }

  .search-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-left,
  .search-right {
    justify-content: center;
  }

  .content-section {
    padding: 16px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .tag-management {
    background: #141414;
  }

  .page-header,
  .type-tabs,
  .tree-container,
  .table-container {
    background: #1f1f1f;
    border-color: #303030;
  }

  .page-title {
    color: #ffffff;
  }

  .page-description {
    color: #a6a6a6;
  }

  .search-section {
    background: #262626;
    border-color: #303030;
  }

  .node-level {
    background: #262626;
    color: #a6a6a6;
  }
}
</style>
