/**
 * 用户状态管理
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建用户状态管理; Principle_Applied: 状态管理模式;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 12:32:03 +08:00; Reason: Task-007 用户状态管理更新, 添加微信绑定状态管理; Principle_Applied: 状态管理模式;}}
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authApi } from '@/api/auth'
import { userApi } from '@/api/user'
import { wechatApi } from '@/api/wechat'
import type { LoginRequest, LoginResponse, UserProfile } from '@/types/auth'
import type { WechatBindInfo, WechatBindStatus } from '@/types/wechat'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<UserProfile | null>(null)
  const accessToken = ref<string>('')
  const refreshToken = ref<string>('')
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!accessToken.value && !!user.value)
  const isAdmin = computed(() => roles.value.includes('ENTERPRISE_ADMIN'))
  const isSuperAdmin = computed(() => roles.value.includes('SUPER_ADMIN'))

  // 微信绑定相关计算属性
  const wechatBindInfo = computed((): WechatBindInfo => {
    return wechatApi.checkBindStatus(user.value)
  })

  const isWechatBound = computed((): boolean => {
    return wechatBindInfo.value.status === 'bound'
  })

  const wechatNickname = computed((): string => {
    return user.value?.wechatNickname || ''
  })

  const wechatAvatar = computed((): string => {
    return user.value?.wechatAvatar || ''
  })

  const wechatBoundTime = computed((): string => {
    return wechatApi.formatBoundTime(user.value?.wechatBoundAt)
  })

  // 方法
  const login = async (loginData: LoginRequest): Promise<void> => {
    loading.value = true
    try {
      const response = await authApi.login(loginData)
      const { user: userData, tokens, permissions: userPermissions, roles: userRoles } = response

      // 保存用户信息
      user.value = userData
      accessToken.value = tokens.accessToken
      refreshToken.value = tokens.refreshToken
      permissions.value = userPermissions
      roles.value = userRoles

      // 保存到本地存储
      localStorage.setItem('accessToken', tokens.accessToken)
      localStorage.setItem('refreshToken', tokens.refreshToken)
      localStorage.setItem('user', JSON.stringify(userData))
      localStorage.setItem('permissions', JSON.stringify(userPermissions))
      localStorage.setItem('roles', JSON.stringify(userRoles))
    } finally {
      loading.value = false
    }
  }

  const logout = async (): Promise<void> => {
    try {
      if (accessToken.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.warn('登出API调用失败:', error)
    } finally {
      // 清除状态
      user.value = null
      accessToken.value = ''
      refreshToken.value = ''
      permissions.value = []
      roles.value = []

      // 清除本地存储
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      localStorage.removeItem('permissions')
      localStorage.removeItem('roles')

      // 跳转到登录页
      const router = (await import('@/router')).default
      router.push('/login')
    }
  }

  const refreshAccessToken = async (): Promise<boolean> => {
    if (!refreshToken.value) {
      return false
    }

    try {
      const response = await authApi.refreshToken({ refreshToken: refreshToken.value })
      accessToken.value = response.accessToken
      localStorage.setItem('accessToken', response.accessToken)
      return true
    } catch (error) {
      console.error('刷新令牌失败:', error)
      await logout()
      return false
    }
  }

  const getUserProfile = async (): Promise<void> => {
    try {
      const profile = await authApi.getProfile()
      user.value = profile
      permissions.value = profile.permissions
      roles.value = profile.roles

      // 更新本地存储
      localStorage.setItem('user', JSON.stringify(profile))
      localStorage.setItem('permissions', JSON.stringify(profile.permissions))
      localStorage.setItem('roles', JSON.stringify(profile.roles))
    } catch (error) {
      console.error('获取用户信息失败:', error)
      await logout()
    }
  }

  const initializeAuth = async (): Promise<void> => {
    // 从本地存储恢复状态
    const storedToken = localStorage.getItem('accessToken')
    const storedRefreshToken = localStorage.getItem('refreshToken')
    const storedUser = localStorage.getItem('user')
    const storedPermissions = localStorage.getItem('permissions')
    const storedRoles = localStorage.getItem('roles')

    if (storedToken && storedUser) {
      accessToken.value = storedToken
      refreshToken.value = storedRefreshToken || ''
      user.value = JSON.parse(storedUser)
      permissions.value = storedPermissions ? JSON.parse(storedPermissions) : []
      roles.value = storedRoles ? JSON.parse(storedRoles) : []

      // 验证令牌有效性
      try {
        await authApi.verifyToken()
        // 如果令牌有效，获取最新的用户信息
        await getUserProfile()
      } catch (error) {
        // 令牌无效，尝试刷新
        const refreshed = await refreshAccessToken()
        if (refreshed) {
          await getUserProfile()
        }
      }
    }
  }

  const hasPermission = (permission: string): boolean => {
    // 超级管理员只拥有企业管理权限，不再默认拥有所有权限
    return permissions.value.includes(permission)
  }

  const hasPermissions = (requiredPermissions: string[]): boolean => {
    // 超级管理员只拥有企业管理权限，不再默认拥有所有权限
    return requiredPermissions.every(permission => permissions.value.includes(permission))
  }

  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }

  const hasRoles = (requiredRoles: string[]): boolean => {
    return requiredRoles.some(role => roles.value.includes(role))
  }

  const hasAnyPermission = (requiredPermissions: string[]): boolean => {
    // 超级管理员只拥有企业管理权限，不再默认拥有所有权限
    return requiredPermissions.some(permission => permissions.value.includes(permission))
  }

  const updateUserInfo = (userInfo: Partial<UserProfile>): void => {
    if (user.value) {
      user.value = { ...user.value, ...userInfo }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  // ==================== 微信绑定状态管理 ====================

  /**
   * 更新微信信息
   */
  const updateWechatInfo = (wechatInfo: {
    wechatOpenid?: string
    wechatUnionid?: string
    wechatAvatar?: string
    wechatNickname?: string
    wechatBoundAt?: string
  }): void => {
    if (user.value) {
      user.value = {
        ...user.value,
        wechatOpenid: wechatInfo.wechatOpenid,
        wechatUnionid: wechatInfo.wechatUnionid,
        wechatAvatar: wechatInfo.wechatAvatar,
        wechatNickname: wechatInfo.wechatNickname,
        wechatBoundAt: wechatInfo.wechatBoundAt
      }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  /**
   * 清除微信信息
   */
  const clearWechatInfo = (): void => {
    if (user.value) {
      user.value = {
        ...user.value,
        wechatOpenid: undefined,
        wechatUnionid: undefined,
        wechatAvatar: undefined,
        wechatNickname: undefined,
        wechatBoundAt: undefined
      }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  /**
   * 获取微信授权URL
   */
  const getWechatAuthUrl = async (): Promise<string> => {
    try {
      const response = await userApi.getWechatAuthUrl()
      return response.authUrl
    } catch (error) {
      console.error('获取微信授权URL失败:', error)
      throw error
    }
  }

  /**
   * 绑定微信账号
   */
  const bindWechat = async (code: string, state: string): Promise<void> => {
    try {
      const response = await userApi.bindWechat({ code, state })
      if (response.success && response.userInfo) {
        updateWechatInfo(response.userInfo)
      }
    } catch (error) {
      console.error('绑定微信账号失败:', error)
      throw error
    }
  }

  /**
   * 解绑微信账号
   */
  const unbindWechat = async (): Promise<void> => {
    try {
      const response = await userApi.unbindWechat()
      if (response.success) {
        clearWechatInfo()
      }
    } catch (error) {
      console.error('解绑微信账号失败:', error)
      throw error
    }
  }

  /**
   * 刷新用户信息（包含微信字段）
   */
  const refreshUserProfile = async (): Promise<void> => {
    await getUserProfile()
  }

  return {
    // 状态
    user: readonly(user),
    accessToken: readonly(accessToken),
    refreshToken: readonly(refreshToken),
    permissions: readonly(permissions),
    roles: readonly(roles),
    loading: readonly(loading),

    // 计算属性
    isAuthenticated,
    isAdmin,
    isSuperAdmin,
    wechatBindInfo,
    isWechatBound,
    wechatNickname,
    wechatAvatar,
    wechatBoundTime,

    // 方法
    login,
    logout,
    refreshAccessToken,
    getUserProfile,
    initializeAuth,
    hasPermission,
    hasPermissions,
    hasRole,
    hasRoles,
    hasAnyPermission,
    updateUserInfo,

    // 微信相关方法
    updateWechatInfo,
    clearWechatInfo,
    getWechatAuthUrl,
    bindWechat,
    unbindWechat,
    refreshUserProfile
  }
})
