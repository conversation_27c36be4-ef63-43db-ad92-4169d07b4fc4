/**
 * Vue Router配置
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建路由配置; Principle_Applied: 前端路由管理;}}
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getFirstAccessibleMenuPath } from '@/utils/permission'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 路由配置
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: () => {
      // 动态重定向到用户有权限访问的第一个菜单页面
      return getFirstAccessibleMenuPath()
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    component: () => import('@/components/Layout/MainLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/DashboardView.vue'),
        meta: {
          title: '仪表盘',
          requiresAuth: true,
          permissions: []
        }
      },
      {
        path: 'enterprises',
        name: 'EnterpriseManagement',
        component: () => import('@/views/enterprise/EnterpriseManagementView.vue'),
        meta: {
          title: '企业管理',
          requiresAuth: true,
          permissions: ['enterprise:view']
        }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/views/user/UserManagementView.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          // 允许企业管理员(user:view)和超级管理员(enterprise:view)访问
          customPermissionCheck: true
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/user/ProfileView.vue'),
        meta: {
          title: '个人信息',
          requiresAuth: true,
          permissions: []
        }
      },
      {
        path: 'equipment',
        name: 'EquipmentManagement',
        component: () => import('@/views/equipment/EquipmentManagementView.vue'),
        meta: {
          title: '设备管理',
          requiresAuth: true,
          permissions: ['equipment:view']
        }
      },
      {
        path: 'tag-management',
        name: 'TagManagement',
        component: () => import('@/views/tag/TagManagementView.vue'),
        meta: {
          title: '标签管理',
          requiresAuth: true,
          permissions: ['tag:view']
        }
      },
      {
        path: 'enterprises',
        name: 'EnterpriseManagement',
        component: () => import('@/views/enterprise/EnterpriseManagementView.vue'),
        meta: {
          title: '企业管理',
          requiresAuth: true,
          permissions: ['enterprise:view'],
          roles: ['SUPER_ADMIN']
        }
      },
      {
        path: 'departments',
        name: 'DepartmentManagement',
        component: () => import('@/views/department/DepartmentManagementView.vue'),
        meta: {
          title: '部门管理',
          requiresAuth: true,
          permissions: ['department:view']
        }
      },
      {
        path: 'orders',
        name: 'OrderManagement',
        component: () => import('@/views/order/OrderManagementView.vue'),
        meta: {
          title: '订单管理',
          requiresAuth: true,
          permissions: ['order:view']
        }
      },
      {
        path: 'production',
        name: 'ProductionPlanning',
        component: () => import('@/views/production/ProductionPlanningView.vue'),
        meta: {
          title: '生产计划',
          requiresAuth: true,
          permissions: ['production:view']
        }
      },
      {
        path: 'inventory',
        name: 'InventoryManagement',
        component: () => import('@/views/inventory/InventoryManagementView.vue'),
        meta: {
          title: '库存管理',
          requiresAuth: true,
          permissions: ['inventory:view']
        }
      },
      {
        path: 'salary/rates',
        name: 'SalaryRates',
        component: () => import('@/views/salary/SalaryRatesView.vue'),
        meta: {
          title: '工价设置',
          requiresAuth: true,
          permissions: ['salary_rate:view']
        }
      },
      {
        path: 'salary/list',
        name: 'SalaryList',
        component: () => import('@/views/salary/SalaryListView.vue'),
        meta: {
          title: '工资列表',
          requiresAuth: true,
          permissions: ['salary_list:view']
        }
      },
      {
        path: 'digital',
        name: 'DigitalSpace',
        component: () => import('@/views/digital/DigitalSpaceView.vue'),
        meta: {
          title: '数字空间',
          requiresAuth: true,
          permissions: ['digital:view']
        }
      }
    ]
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/ForbiddenView.vue'),
    meta: {
      title: '权限不足',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/NotFoundView.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 刺绣管理系统`
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果用户未登录，重定向到登录页
    if (!userStore.isAuthenticated) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      const hasPermission = userStore.hasPermissions(to.meta.permissions as string[])
      if (!hasPermission) {
        next('/403')
        return
      }
    }

    // 检查自定义权限
    if (to.meta.customPermissionCheck) {
      if (to.name === 'UserManagement') {
        // 用户管理页面：企业管理员(user:view)和超级管理员(enterprise:view)都可以访问
        const hasUserManagementPermission = userStore.hasPermission('user:view') || userStore.hasPermission('enterprise:view')
        if (!hasUserManagementPermission) {
          next('/403')
          return
        }
      }
    }
    
    // 检查角色
    if (to.meta.roles && Array.isArray(to.meta.roles)) {
      const hasRole = userStore.hasRoles(to.meta.roles as string[])
      if (!hasRole) {
        next('/403')
        return
      }
    }
  }
  
  // 如果已登录用户访问登录页，重定向到用户有权限的第一个菜单页面
  if (to.path === '/login' && userStore.isAuthenticated) {
    next(getFirstAccessibleMenuPath())
    return
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
