import type { Pattern, PatternStats, PatternSearchOptions } from '@/types/pattern'

/**
 * 生成模拟花样数据
 */
export function generateMockPatterns(count: number = 20): Pattern[] {
  const fileTypes = ['dst', 'pes', 'jef', 'exp', 'vp3', 'hus']
  const groups = [
    { id: 1, name: '基础花样' },
    { id: 2, name: '装饰花样' },
    { id: 3, name: '字母花样' },
    { id: 4, name: '动物花样' },
    { id: 5, name: '植物花样' }
  ]
  const users = ['张三', '李四', '王五', '赵六', '钱七']

  const patterns: Pattern[] = []

  for (let i = 1; i <= count; i++) {
    const fileType = fileTypes[Math.floor(Math.random() * fileTypes.length)]
    const group = groups[Math.floor(Math.random() * groups.length)]
    const createUser = users[Math.floor(Math.random() * users.length)]
    
    patterns.push({
      id: i,
      enterpriseId: 1,
      code: `P${String(i).padStart(4, '0')}`,
      name: `花样${i}`,
      remark: Math.random() > 0.5 ? `这是花样${i}的备注信息` : undefined,
      groupId: group.id,
      fileType,
      stitch: Math.floor(Math.random() * 50000) + 1000,
      jumps: Math.floor(Math.random() * 100) + 10,
      trim: Math.floor(Math.random() * 50) + 5,
      colors: Math.floor(Math.random() * 10) + 1,
      baseLine: Math.round((Math.random() * 1000 + 100) * 100) / 100,
      surfaceLine: Math.round((Math.random() * 2000 + 200) * 100) / 100,
      goldPieceNum: Math.random() > 0.7 ? Math.floor(Math.random() * 20) : undefined,
      minX: Math.round((Math.random() * 100 - 50) * 100) / 100,
      maxX: Math.round((Math.random() * 100 + 50) * 100) / 100,
      minY: Math.round((Math.random() * 100 - 50) * 100) / 100,
      maxY: Math.round((Math.random() * 100 + 50) * 100) / 100,
      width: Math.round((Math.random() * 200 + 50) * 100) / 100,
      height: Math.round((Math.random() * 200 + 50) * 100) / 100,
      goldPieceLine: Math.random() > 0.7 ? JSON.stringify([
        { color: '针杆1', goldPieces: null, lines: '212.67' }
      ]) : undefined,
      needle: JSON.stringify([
        `1A:1:${Math.floor(Math.random() * 500 + 100)}`,
        `2A:2:${Math.floor(Math.random() * 500 + 100)}`
      ]),
      mergeGoldPieceLine: Math.random() > 0.8 ? JSON.stringify([
        {
          colors: '针杆1,针杆2',
          goldPieces: 'A:100,B:200',
          line: Math.floor(Math.random() * 500 + 100),
          uuid: Math.random().toString(36).substr(2, 9)
        }
      ]) : undefined,
      image: Math.random() > 0.6 ? `/images/pattern-${i}.png` : undefined,
      createUser,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      group
    })
  }

  return patterns
}

/**
 * 生成模拟统计数据
 */
export function generateMockStats(patterns: Pattern[]): PatternStats {
  // 按文件类型统计
  const fileTypeMap = new Map<string, number>()
  patterns.forEach(pattern => {
    if (pattern.fileType) {
      fileTypeMap.set(pattern.fileType, (fileTypeMap.get(pattern.fileType) || 0) + 1)
    }
  })
  const byFileType = Array.from(fileTypeMap.entries()).map(([fileType, count]) => ({
    fileType,
    count
  }))

  // 按分组统计
  const groupMap = new Map<string, number>()
  patterns.forEach(pattern => {
    const groupName = pattern.group?.name || '未分组'
    groupMap.set(groupName, (groupMap.get(groupName) || 0) + 1)
  })
  const byGroup = Array.from(groupMap.entries()).map(([groupName, count]) => ({
    groupName,
    count
  }))

  // 按创建人统计
  const createUserMap = new Map<string, number>()
  patterns.forEach(pattern => {
    if (pattern.createUser) {
      createUserMap.set(pattern.createUser, (createUserMap.get(pattern.createUser) || 0) + 1)
    }
  })
  const byCreateUser = Array.from(createUserMap.entries()).map(([createUser, count]) => ({
    createUser,
    count
  }))

  return {
    total: patterns.length,
    byFileType,
    byGroup,
    byCreateUser
  }
}

/**
 * 生成模拟搜索选项
 */
export function generateMockSearchOptions(): PatternSearchOptions {
  return {
    groups: [
      { id: 1, name: '基础花样' },
      { id: 2, name: '装饰花样' },
      { id: 3, name: '字母花样' },
      { id: 4, name: '动物花样' },
      { id: 5, name: '植物花样' }
    ],
    fileTypes: [
      { label: 'DST', value: 'dst' },
      { label: 'PES', value: 'pes' },
      { label: 'JEF', value: 'jef' },
      { label: 'EXP', value: 'exp' },
      { label: 'VP3', value: 'vp3' },
      { label: 'HUS', value: 'hus' }
    ],
    createUsers: [
      { label: '张三', value: '张三' },
      { label: '李四', value: '李四' },
      { label: '王五', value: '王五' },
      { label: '赵六', value: '赵六' },
      { label: '钱七', value: '钱七' }
    ]
  }
}
