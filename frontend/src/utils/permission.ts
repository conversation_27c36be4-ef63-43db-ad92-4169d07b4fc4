/**
 * 权限相关工具函数
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-02 14:30:00 +08:00; Reason: 实现用户登录后自动跳转到有权限的第一个菜单页面; Principle_Applied: 权限控制;}}
 */

import { useUserStore } from '../stores/user'

/**
 * 菜单项接口定义
 */
interface MenuItem {
  key: string
  title: string
  path?: string
  permissions?: string[]
  roles?: string[]
  disabled?: boolean
  children?: MenuItem[]
}

/**
 * 系统菜单配置 - 与SideMenu.vue保持一致
 */
const MENU_ITEMS: MenuItem[] = [
  {
    key: 'enterprises',
    title: '企业管理',
    path: '/enterprises',
    permissions: ['enterprise:view']
  },
  {
    key: 'equipment',
    title: '设备管理',
    path: '/equipment',
    permissions: ['equipment:view']
  },
  {
    key: 'orders',
    title: '订单管理',
    path: '/orders',
    permissions: ['order:view']
  },
  {
    key: 'production',
    title: '生产计划',
    path: '/production',
    permissions: ['production:view']
  },
  {
    key: 'salary-rates',
    title: '工价设置',
    path: '/salary/rates',
    permissions: [
      'salary_rate:view', 'salary_rate:create', 'salary_rate:update', 'salary_rate:delete'
    ]
  },
  {
    key: 'salary-list',
    title: '工资列表',
    path: '/salary/list',
    permissions: [
      'salary_list:view', 'salary_list:create', 'salary_list:update', 'salary_list:delete',
      'salary_list:export', 'salary_list:approve'
    ]
  },
  {
    key: 'digital',
    title: '数字空间',
    path: '/digital',
    permissions: ['digital:view']
  }
]

/**
 * 检查菜单权限
 */
function hasMenuPermission(item: MenuItem, userStore: any): boolean {
  // 如果没有权限要求，默认显示
  if (!item.permissions && !item.roles) {
    return true
  }

  // 检查角色权限
  if (item.roles && item.roles.length > 0) {
    const hasRole = userStore.hasRoles(item.roles)
    if (!hasRole) {
      return false
    }
  }

  // 检查功能权限
  if (item.permissions && item.permissions.length > 0) {
    return userStore.hasPermissions(item.permissions)
  }

  return true
}

/**
 * 获取用户有权限访问的第一个菜单页面路径
 */
export function getFirstAccessibleMenuPath(): string {
  const userStore = useUserStore()
  
  // 如果用户未登录，返回登录页
  if (!userStore.isAuthenticated) {
    return '/login'
  }

  // 遍历菜单项，找到第一个有权限访问的页面
  for (const item of MENU_ITEMS) {
    if (item.path && hasMenuPermission(item, userStore)) {
      return item.path
    }
    
    // 如果有子菜单，检查子菜单项
    if (item.children) {
      for (const child of item.children) {
        if (child.path && hasMenuPermission(child, userStore)) {
          return child.path
        }
      }
    }
  }

  // 检查用户管理权限（现在通过底部弹框访问，这里不再作为默认跳转）
  // 如果用户只有用户管理权限而没有其他业务权限，跳转到用户管理页面
  if (userStore.hasPermission('user:view') && !userStore.hasAnyPermission(['order:view', 'production:view', 'salary:view', 'digital:view', 'equipment:view'])) {
    return '/users'
  }

  // 如果没有任何菜单权限，返回仪表盘
  return '/dashboard'
}

/**
 * 检查用户是否有指定权限
 */
export function hasPermission(permission: string): boolean {
  const userStore = useUserStore()
  return userStore.hasPermission(permission)
}

/**
 * 检查用户是否有指定权限中的任意一个
 */
export function hasAnyPermission(permissions: string[]): boolean {
  const userStore = useUserStore()
  return userStore.hasAnyPermission(permissions)
}

/**
 * 检查用户是否有所有指定权限
 */
export function hasAllPermissions(permissions: string[]): boolean {
  const userStore = useUserStore()
  return userStore.hasPermissions(permissions)
}

/**
 * 检查用户是否有指定角色
 */
export function hasRole(role: string): boolean {
  const userStore = useUserStore()
  return userStore.hasRole(role)
}

/**
 * 检查用户是否有指定角色中的任意一个
 */
export function hasAnyRole(roles: string[]): boolean {
  const userStore = useUserStore()
  return userStore.hasRoles(roles)
}
