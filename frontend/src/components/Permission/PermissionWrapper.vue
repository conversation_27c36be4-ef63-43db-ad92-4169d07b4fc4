<!--
  权限包装组件
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-005 基础布局, 创建权限包装组件; Principle_Applied: 组件化权限控制;}}
-->

<template>
  <div v-if="hasPermission">
    <slot />
  </div>
  <div v-else-if="$slots.fallback">
    <slot name="fallback" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

interface Props {
  // 需要的权限（字符串或数组）
  permissions?: string | string[]
  // 需要的角色（字符串或数组）
  roles?: string | string[]
  // 权限检查模式：all-需要所有权限，any-需要任意权限
  mode?: 'all' | 'any'
  // 是否反转权限检查结果
  reverse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'all',
  reverse: false
})

const userStore = useUserStore()

// 计算是否有权限
const hasPermission = computed(() => {
  // 如果用户未登录，没有权限
  if (!userStore.isAuthenticated) {
    return props.reverse ? true : false
  }
  
  // 超级管理员只拥有企业管理权限，不再默认拥有所有权限
  // 移除超级管理员的默认全权限逻辑，让其按正常权限检查流程处理
  
  let permissionResult = true
  let roleResult = true
  
  // 检查权限
  if (props.permissions) {
    if (typeof props.permissions === 'string') {
      permissionResult = userStore.hasPermission(props.permissions)
    } else if (Array.isArray(props.permissions)) {
      if (props.mode === 'all') {
        permissionResult = userStore.hasPermissions(props.permissions)
      } else {
        permissionResult = userStore.hasAnyPermission(props.permissions)
      }
    }
  }
  
  // 检查角色
  if (props.roles) {
    if (typeof props.roles === 'string') {
      roleResult = userStore.hasRole(props.roles)
    } else if (Array.isArray(props.roles)) {
      if (props.mode === 'all') {
        // 需要拥有所有角色
        roleResult = props.roles.every(role => userStore.hasRole(role))
      } else {
        // 需要拥有任意角色
        roleResult = userStore.hasRoles(props.roles)
      }
    }
  }
  
  const result = permissionResult && roleResult
  return props.reverse ? !result : result
})
</script>

<script lang="ts">
export default {
  name: 'PermissionWrapper'
}
</script>
