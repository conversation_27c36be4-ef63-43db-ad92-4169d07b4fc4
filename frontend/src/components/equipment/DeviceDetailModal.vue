<!--
  设备详情弹窗组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-23 15:30:28 +08:00; Reason: 设备管理页面开发, 创建设备详情展示组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <div class="device-detail">
    <a-descriptions :column="2" bordered>
      <!-- 基本信息 -->
      <a-descriptions-item label="设备名称" :span="2">
        <strong>{{ device.name }}</strong>
      </a-descriptions-item>
      
      <a-descriptions-item label="设备编号">
        {{ device.code || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="设备SN">
        {{ device.sn || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="MAC地址">
        {{ formatMacAddress(device.mac) || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="IP地址">
        {{ device.ip || '-' }}
      </a-descriptions-item>
      
      <!-- 设备规格 -->
      <a-descriptions-item label="机型">
        {{ device.deviceModel || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="厂商">
        {{ device.vendor || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="电控型号">
        {{ device.controlModel || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="机头头距">
        {{ device.headSpace ? `${device.headSpace}mm` : '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="机头头数">
        {{ device.headNum || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="机头针数">
        {{ device.headNeedleNum || '-' }}
      </a-descriptions-item>
      
      <!-- 计算参数 -->
      <a-descriptions-item label="计算头距">
        {{ device.formularHeadSpace ? `${device.formularHeadSpace}mm` : '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="计算头数">
        {{ device.formularHeadNum || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="计算长度">
        {{ device.formularLength ? `${device.formularLength}mm` : '-' }}
      </a-descriptions-item>
      
      <!-- 软件信息 -->
      <a-descriptions-item label="显示软件">
        {{ device.displaySoftware || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="主控软件">
        {{ device.controlSoftware || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="产线ID">
        {{ device.productionLineId || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="添加方式">
        {{ getRegisterWayText(device.registerWay) }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- WiFi信息 -->
    <a-divider>WiFi信息</a-divider>
    
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="WiFi状态">
        <a-tag :color="getWifiStateColor(device.wifiState)">
          {{ getWifiStateText(device.wifiState) }}
        </a-tag>
      </a-descriptions-item>
      
      <a-descriptions-item label="WiFi名称">
        {{ device.wifiSsid || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="WiFi IP">
        {{ device.wifiIp || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="WiFi MAC">
        {{ formatMacAddress(device.wifiMac) || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="加密方式">
        {{ getWifiKeyMgmtText(device.wifiKeyMgmt) }}
      </a-descriptions-item>
      
      <a-descriptions-item label="信号强度">
        <div v-if="device.wifiSignalLevel" class="wifi-signal">
          <a-tag :color="signalInfo.color">
            {{ signalInfo.label }}
          </a-tag>
          <span class="signal-value">{{ device.wifiSignalLevel }}dBm</span>
        </div>
        <span v-else>-</span>
      </a-descriptions-item>
      
      <a-descriptions-item label="信号质量">
        {{ device.wifiLinkQuality || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="WiFi速率">
        {{ device.wifiBitRate ? `${device.wifiBitRate}Mbps` : '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="无线频率">
        {{ device.wifiFreq ? `${device.wifiFreq}MHz` : '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="网关MAC">
        {{ formatMacAddress(device.gatewayMac) || '-' }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 时间信息 -->
    <a-divider>时间信息</a-divider>
    
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="创建时间">
        {{ formatDateTime(device.createdAt) }}
      </a-descriptions-item>
      
      <a-descriptions-item label="更新时间">
        {{ formatDateTime(device.updatedAt) }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 备注信息 -->
    <a-divider v-if="device.remark">备注信息</a-divider>
    
    <div v-if="device.remark" class="remark-section">
      <a-typography-paragraph>
        {{ device.remark }}
      </a-typography-paragraph>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button @click="$emit('close')">
          关闭
        </a-button>
        <a-button type="primary" @click="handleEdit">
          <EditOutlined />
          编辑设备
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { EditOutlined } from '@ant-design/icons-vue'
import { deviceUtils } from '../../api/device'
import type { Device } from '../../types/device'
import dayjs from 'dayjs'

// Props
interface Props {
  device: Device
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  edit: [device: Device]
}>()

// 计算属性
const signalInfo = computed(() => {
  return deviceUtils.parseWifiSignalLevel(props.device.wifiSignalLevel)
})

// 方法
const formatMacAddress = (mac?: string): string => {
  return deviceUtils.formatMacAddress(mac)
}

const getWifiStateText = (state?: string): string => {
  return deviceUtils.getWifiStateText(state)
}

const getWifiStateColor = (state?: string): string => {
  return deviceUtils.getWifiStateColor(state)
}

const getRegisterWayText = (way?: string): string => {
  return deviceUtils.getRegisterWayText(way)
}

const getWifiKeyMgmtText = (keyMgmt?: string): string => {
  return deviceUtils.getWifiKeyMgmtText(keyMgmt)
}

const formatDateTime = (dateTime: string): string => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

const handleEdit = () => {
  emit('edit', props.device)
}
</script>

<style scoped>
.device-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.wifi-signal {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-value {
  font-size: 12px;
  color: #666;
}

.remark-section {
  background-color: #fafafa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.action-buttons {
  margin-top: 24px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-detail {
    max-height: 60vh;
  }
  
  :deep(.ant-descriptions) {
    font-size: 12px;
  }
  
  :deep(.ant-descriptions-item-label) {
    width: 80px !important;
  }
}
</style>
