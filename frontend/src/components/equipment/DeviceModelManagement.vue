<!--
设备类型管理组件
{{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建设备类型管理组件; Principle_Applied: 组件化开发;}}
-->

<template>
  <a-modal
    :open="props.visible"
    title="设备机型管理"
    width="1000px"
    :footer="null"
    :destroy-on-close="true"
    :body-style="{ height: '600px', padding: '16px', overflow: 'hidden' }"
    @cancel="handleCancel"
  >
    <div class="device-model-management">
      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="left-actions">
          <a-input-search
            v-model:value="searchForm.search"
            placeholder="搜索设备类型名称或编码"
            style="width: 300px"
            @search="handleSearch"
            @pressEnter="handleSearch"
          />
        </div>
        <div class="right-actions">
          <a-button type="primary" @click="showCreateModal">
            <template #icon>
              <PlusOutlined />
            </template>
            新增设备类型
          </a-button>
          <a-button @click="refreshList">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <!-- 设备类型表格 -->
      <a-table
        :columns="columns"
        :data-source="deviceModelList"
        :loading="loading"
        :pagination="tablePagination"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 设备类型名称列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="device-model-name">
              <strong>{{ record.name }}</strong>
              <div class="device-model-code">
                编码: {{ record.code }}
              </div>
            </div>
          </template>

          <!-- 关联设备数量列 -->
          <template v-else-if="column.key === 'deviceCount'">
            <a-tag :color="record.devices?.length > 0 ? 'blue' : 'default'">
              {{ record.devices?.length || 0 }} 台设备
            </a-tag>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'createdAt'">
            {{ formatDateTime(record.createdAt) }}
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="showDetailModal(record)">
                <EyeOutlined />
                查看
              </a-button>
              <a-button type="link" size="small" @click="showEditModal(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                danger 
                :disabled="!deviceModelUtils.canDelete(record)"
                @click="handleDelete(record)"
              >
                <DeleteOutlined />
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>

    <!-- 设备类型表单弹窗 -->
      <DeviceModelFormModal
        v-model:visible="formModalVisible"
        :device-model="selectedDeviceModel"
        :is-edit="isEdit"
        @success="handleFormSuccess"
      />

      <!-- 设备类型详情弹窗 -->
      <DeviceModelDetailModal
        v-model:visible="detailModalVisible"
        :device-model="selectedDeviceModel"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import { deviceModelApi, deviceModelUtils } from '../../api/deviceModel'
import type {
  DeviceModel,
  DeviceModelListQuery,
  DeviceModelListResponse,
  DeviceModelSearchForm
} from '../../types/deviceModel'
import DeviceModelFormModal from './DeviceModelFormModal.vue'
import DeviceModelDetailModal from './DeviceModelDetailModal.vue'
import dayjs from 'dayjs'

// ==================== Props & Emits ====================

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'cancel'): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref(false)

// 设备类型列表
const deviceModelList = ref<DeviceModel[]>([])

// 搜索表单
const searchForm = reactive<DeviceModelSearchForm>({
  search: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 弹窗状态
const formModalVisible = ref(false)
const detailModalVisible = ref(false)
const selectedDeviceModel = ref<DeviceModel | null>(null)
const isEdit = ref(false)

// ==================== 计算属性 ====================

// 表格分页配置
const tablePagination = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: pagination.showSizeChanger,
  showQuickJumper: pagination.showQuickJumper,
  showTotal: pagination.showTotal
}))

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '设备类型',
    key: 'name',
    dataIndex: 'name',
    align: 'center'
  },
  {
    title: '关联设备',
    key: 'deviceCount',
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'createdAt',
    dataIndex: 'createdAt',
    sorter: true,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center'
  }
]

// ==================== 方法 ====================

/**
 * 获取设备类型列表
 */
const fetchDeviceModelList = async () => {
  try {
    loading.value = true

    const params: DeviceModelListQuery = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      search: searchForm.search || undefined
    }

    const response: DeviceModelListResponse = await deviceModelApi.getDeviceModelList(params)

    deviceModelList.value = response.deviceModels
    pagination.total = response.total

  } catch (error) {
    console.error('获取设备类型列表失败:', error)
    message.error('获取设备类型列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  pagination.current = 1
  fetchDeviceModelList()
}

/**
 * 刷新列表
 */
const refreshList = () => {
  fetchDeviceModelList()
}

/**
 * 表格变化处理
 */
const handleTableChange: TableProps['onChange'] = (pag) => {
  if (pag) {
    pagination.current = pag.current || 1
    pagination.pageSize = pag.pageSize || 10
  }
  fetchDeviceModelList()
}

/**
 * 显示新增弹窗
 */
const showCreateModal = () => {
  selectedDeviceModel.value = null
  isEdit.value = false
  formModalVisible.value = true
}

/**
 * 显示编辑弹窗
 */
const showEditModal = (deviceModel: DeviceModel) => {
  selectedDeviceModel.value = deviceModel
  isEdit.value = true
  formModalVisible.value = true
}

/**
 * 显示详情弹窗
 */
const showDetailModal = (deviceModel: DeviceModel) => {
  selectedDeviceModel.value = deviceModel
  detailModalVisible.value = true
}

/**
 * 表单成功处理
 */
const handleFormSuccess = () => {
  formModalVisible.value = false
  refreshList()
  // 通知父组件刷新搜索选项
  console.log('🔧 DeviceModelManagement: 表单操作成功，触发success事件')
  emit('success')
}

/**
 * 删除设备类型
 */
const handleDelete = (deviceModel: DeviceModel) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除设备类型"${deviceModel.name}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deviceModelApi.deleteDeviceModel(deviceModel.id)
        message.success('删除设备类型成功')
        refreshList()
        // 通知父组件刷新搜索选项
        console.log('🔧 DeviceModelManagement: 删除设备类型成功，触发success事件')
        emit('success')
      } catch (error) {
        console.error('删除设备类型失败:', error)
        message.error('删除设备类型失败，请稍后重试')
      }
    }
  })
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 处理弹窗取消
 */
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// ==================== 监听器 ====================

// 监听弹窗显示状态，显示时获取数据
watch(
  () => props.visible,
  (newVisible: boolean) => {
    if (newVisible) {
      fetchDeviceModelList()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.device-model-management {
  background: #fff;
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.right-actions {
  display: flex;
  gap: 8px;
}

.device-model-name {
  line-height: 1.4;
}

.device-model-code {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.parameter-text {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

/* 简单的表格样式 */
:deep(.ant-table-wrapper) {
  flex: 1;
}
</style>
