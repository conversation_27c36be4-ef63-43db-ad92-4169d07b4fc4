<!--
设备类型详情弹窗组件
{{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建设备类型详情弹窗组件; Principle_Applied: 组件化开发;}}
-->

<template>
  <a-modal
    :visible="visible"
    title="设备类型详情"
    :width="800"
    :footer="null"
    @cancel="handleCancel"
  >
    <div v-if="deviceModel" class="device-model-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="设备类型名称">
            {{ deviceModel.name }}
          </a-descriptions-item>
          <a-descriptions-item label="型号编码">
            <a-tag color="blue">{{ deviceModel.code }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(deviceModel.createdAt) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDateTime(deviceModel.updatedAt) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 配置参数 -->
      <div class="detail-section">
        <h3 class="section-title">配置参数</h3>
        <div class="parameter-content">
          <template v-if="deviceModel.parameter">
            <pre class="parameter-json">{{ formatParameter(deviceModel.parameter) }}</pre>
          </template>
          <template v-else>
            <a-empty description="暂无配置参数" />
          </template>
        </div>
      </div>

      <!-- 关联设备 -->
      <div class="detail-section">
        <h3 class="section-title">
          关联设备
          <a-tag :color="deviceCount > 0 ? 'blue' : 'default'" style="margin-left: 8px">
            {{ deviceCount }} 台
          </a-tag>
        </h3>
        <div class="devices-content">
          <template v-if="deviceModel.devices && deviceModel.devices.length > 0">
            <a-list
              :data-source="deviceModel.devices"
              size="small"
              bordered
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <div class="device-item">
                    <div class="device-name">{{ item.name }}</div>
                    <div v-if="item.code" class="device-code">编号: {{ item.code }}</div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </template>
          <template v-else>
            <a-empty description="暂无关联设备" />
          </template>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
defineOptions({
  name: 'DeviceModelDetailModal'
})
import { computed } from 'vue'
import type { DeviceModel } from '../../types/deviceModel'
import { deviceModelUtils } from '../../api/deviceModel'
import dayjs from 'dayjs'

// ==================== Props & Emits ====================

interface Props {
  visible: boolean
  deviceModel?: DeviceModel | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  deviceModel: null
})

const emit = defineEmits<Emits>()

// ==================== 计算属性 ====================

// 关联设备数量
const deviceCount = computed(() => {
  return props.deviceModel?.devices?.length || 0
})

// ==================== 方法 ====================

/**
 * 取消操作
 */
const handleCancel = () => {
  emit('update:visible', false)
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化配置参数
 */
const formatParameter = (parameter: string) => {
  return deviceModelUtils.formatParameter(parameter)
}
</script>

<style scoped>
.device-model-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
}

.parameter-content {
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
}

.parameter-json {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #262626;
  background: transparent;
  white-space: pre-wrap;
  word-break: break-all;
}

.devices-content {
  max-height: 300px;
  overflow-y: auto;
}

.device-item {
  width: 100%;
}

.device-name {
  font-weight: 500;
  color: #262626;
}

.device-code {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}
</style>
