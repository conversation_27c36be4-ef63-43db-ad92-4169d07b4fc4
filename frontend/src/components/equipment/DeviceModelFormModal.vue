<!--
设备类型表单弹窗组件
{{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建设备类型表单弹窗组件; Principle_Applied: 组件化开发;}}
-->

<template>
  <a-modal
    :visible="visible"
    :title="isEdit ? '编辑设备类型' : '新增设备类型'"
    :confirm-loading="loading"
    :width="600"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="型号编码" name="code">
        <a-input
          v-model:value="formData.code"
          placeholder="请输入型号编码"
          :maxlength="50"
          show-count
        />
      </a-form-item>

      <a-form-item label="设备类型名称" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入设备类型名称"
          :maxlength="100"
          show-count
        />
      </a-form-item>

      <a-form-item label="配置参数" name="parameter">
        <a-textarea
          v-model:value="formData.parameter"
          placeholder="请输入配置参数（JSON格式，可选）"
          :rows="6"
          :maxlength="5000"
          show-count
        />
        <div class="parameter-hint">
          <InfoCircleOutlined />
          配置参数应为有效的JSON格式，例如：{"headCount": 12, "needleCount": 9}
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
defineOptions({
  name: 'DeviceModelFormModal'
})
import { ref, reactive, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { deviceModelApi, deviceModelUtils } from '../../api/deviceModel'
import type {
  DeviceModel,
  DeviceModelFormData,
  CreateDeviceModelRequest,
  UpdateDeviceModelRequest
} from '../../types/deviceModel'

// ==================== Props & Emits ====================

interface Props {
  visible: boolean
  deviceModel?: DeviceModel | null
  isEdit: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  deviceModel: null,
  isEdit: false
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const formData = reactive<DeviceModelFormData>({
  code: '',
  name: '',
  parameter: ''
})

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  code: [
    { required: true, message: '请输入型号编码', trigger: 'blur' },
    { min: 1, max: 50, message: '型号编码长度必须在1-50个字符之间', trigger: 'blur' },
    {
      validator: (_, value) => {
        if (value && !deviceModelUtils.validateCode(value)) {
          return Promise.reject('型号编码只能包含字母、数字、下划线和连字符')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  name: [
    { required: true, message: '请输入设备类型名称', trigger: 'blur' },
    { min: 1, max: 100, message: '设备类型名称长度必须在1-100个字符之间', trigger: 'blur' }
  ],
  parameter: [
    {
      validator: (_, value) => {
        if (value) {
          const validation = deviceModelUtils.validateParameter(value)
          if (!validation.valid) {
            return Promise.reject(validation.error)
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// ==================== 方法 ====================

/**
 * 重置表单
 */
const resetForm = () => {
  formData.code = ''
  formData.name = ''
  formData.parameter = ''
  formRef.value?.clearValidate()
}

/**
 * 填充表单数据
 */
const fillFormData = (deviceModel: DeviceModel) => {
  formData.code = deviceModel.code
  formData.name = deviceModel.name
  formData.parameter = deviceModel.parameter || ''
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()

    loading.value = true

    if (props.isEdit && props.deviceModel) {
      // 编辑模式
      const updateData: UpdateDeviceModelRequest = {
        code: formData.code,
        name: formData.name,
        parameter: formData.parameter || undefined
      }

      await deviceModelApi.updateDeviceModel(props.deviceModel.id, updateData)
      message.success('更新设备类型成功')
    } else {
      // 新增模式
      const createData: CreateDeviceModelRequest = {
        code: formData.code,
        name: formData.name,
        parameter: formData.parameter || undefined
      }

      await deviceModelApi.createDeviceModel(createData)
      message.success('创建设备类型成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交设备类型表单失败:', error)
    if (error instanceof Error) {
      message.error(error.message)
    } else {
      message.error('操作失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

/**
 * 取消操作
 */
const handleCancel = () => {
  emit('update:visible', false)
}

// ==================== 监听器 ====================

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      nextTick(() => {
        if (props.isEdit && props.deviceModel) {
          fillFormData(props.deviceModel)
        } else {
          resetForm()
        }
      })
    } else {
      resetForm()
    }
  }
)
</script>

<style scoped>
.parameter-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
