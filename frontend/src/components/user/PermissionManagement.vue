<!--
  权限管理组件
  {{CHENGQI: Action: Added; Timestamp: 2025-01-04 15:48:00 +08:00; Reason: 重构UserManagementView.vue，拆分权限管理; Principle_Applied: 单一职责原则;}}
-->

<template>
  <div class="permission-content">
    <!-- 左侧：角色列表 -->
    <div class="left">
      <a-card title="角色管理" :bordered="false">
        <template #extra>
          <a-space>
            <a-button
              type="primary"
              size="small"
              @click="handleCreateRole"
              :disabled="!canCreateRole"
            >
              <PlusOutlined />
              新增角色
            </a-button>
            <a-button
              type="text"
              size="small"
              @click="handleRefreshRoles"
              :loading="roleLoading"
            >
              <ReloadOutlined />
            </a-button>
          </a-space>
        </template>

        <div class="role-list-container">
          <a-list
            :data-source="roleList"
            :loading="roleLoading"
            item-layout="horizontal"
            class="role-list"
          >
            <template #renderItem="{ item }">
              <a-list-item
                :class="{ 'role-item-selected': selectedRoleId === item.id }"
                @click="handleSelectRole(item)"
                class="role-item"
              >
                <template #actions>
                  <a-space>
                    <a-button
                      v-if="canUpdateRole && !item.isSystem"
                      type="text"
                      size="small"
                      @click.stop="handleEditRole(item)"
                    >
                      <EditOutlined />
                    </a-button>
                    <a-button
                      v-if="canDeleteRole && !item.isSystem"
                      type="text"
                      size="small"
                      danger
                      @click.stop="handleDeleteRole(item)"
                    >
                      <DeleteOutlined />
                    </a-button>
                  </a-space>
                </template>

                <a-list-item-meta>
                  <template #title>
                    <div class="role-title">
                      <span class="role-name">{{ item.name }}</span>
                      <a-tag v-if="item.isSystem" color="blue" size="small">系统角色</a-tag>
                    </div>
                  </template>
                  <template #description>
                    <div class="role-description">
                      <div class="role-code">编码: {{ item.code }}</div>
                      <div v-if="item.description" class="role-desc">{{ item.description }}</div>
                      <div class="role-permissions">
                        权限数量: {{ item.permissions?.length || 0 }}
                      </div>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </a-card>
    </div>

    <!-- 右侧：权限分配 -->
    <div class="right">
      <a-card :bordered="false">
        <template #title>
          <div class="permission-header">
            <span v-if="selectedRole">{{ selectedRole.name }} - 权限配置</span>
            <span v-else>请选择角色</span>
          </div>
        </template>

        <template #extra v-if="selectedRole && !selectedRole.isSystem">
          <a-button
            type="primary"
            @click="handleSavePermissions"
            :loading="permissionSaving"
            :disabled="!hasPermissionChanges"
          >
            保存权限
          </a-button>
        </template>

        <div v-if="selectedRole" class="permission-assignment">
          <div v-if="selectedRole.isSystem" class="system-role-notice">
            <a-alert
              message="系统角色"
              description="系统角色的权限不能修改"
              type="info"
              show-icon
            />
          </div>

          <div v-else class="permission-groups">
            <div
              v-for="(permissions, module) in groupedPermissions"
              :key="module"
              class="permission-group"
            >
              <div class="permission-group-header">
                <a-checkbox
                  :checked="isModuleAllSelected(module)"
                  :indeterminate="isModuleIndeterminate(module)"
                  @change="toggleModulePermissions(module, $event.target.checked)"
                >
                  {{ getModuleName(module) }}
                </a-checkbox>
              </div>
              <div class="permission-group-content">
                <a-checkbox-group
                  v-model:value="selectedPermissionIds"
                  class="permission-checkboxes"
                >
                  <a-row :gutter="[16, 8]">
                    <a-col
                      v-for="permission in permissions"
                      :key="permission.id"
                      :span="12"
                    >
                      <a-checkbox :value="permission.id">
                        {{ permission.name }}
                      </a-checkbox>
                    </a-col>
                  </a-row>
                </a-checkbox-group>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-role-selected">
          <a-empty description="请从左侧选择一个角色来配置权限" />
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { roleApi, type Role, type Permission } from '@/api/role'

interface Props {
  canCreateRole: boolean
  canUpdateRole: boolean
  canDeleteRole: boolean
}

interface Emits {
  (e: 'create-role'): void
  (e: 'edit-role', role: Role): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// 角色相关数据
const roleList = ref<Role[]>([])
const roleLoading = ref(false)
const selectedRoleId = ref<number | null>(null)
const selectedRole = computed(() => roleList.value.find(role => role.id === selectedRoleId.value) || null)

// 权限相关数据
const permissionList = ref<Permission[]>([])
const selectedPermissionIds = ref<number[]>([])
const permissionSaving = ref(false)

// 权限分组
const groupedPermissions = computed(() => {
  const groups: Record<string, Permission[]> = {}
  permissionList.value.forEach(permission => {
    if (!groups[permission.module]) {
      groups[permission.module] = []
    }
    groups[permission.module].push(permission)
  })
  return groups
})

// 检查权限变更
const hasPermissionChanges = computed(() => {
  if (!selectedRole.value) return false
  const currentPermissionIds = selectedRole.value.permissions?.map(p => p.id) || []
  return JSON.stringify(currentPermissionIds.sort()) !== JSON.stringify(selectedPermissionIds.value.sort())
})

// 初始化
onMounted(() => {
  loadRoleList()
  loadPermissionList()
})

// 加载角色列表
const loadRoleList = async () => {
  roleLoading.value = true
  try {
    roleList.value = await roleApi.getRoleList()
  } catch (error) {
    message.error('加载角色列表失败')
    console.error('加载角色列表失败:', error)
  } finally {
    roleLoading.value = false
  }
}

// 加载权限列表
const loadPermissionList = async () => {
  try {
    permissionList.value = await roleApi.getPermissionList()
  } catch (error) {
    message.error('加载权限列表失败')
    console.error('加载权限列表失败:', error)
  }
}

// 选择角色
const handleSelectRole = (role: Role) => {
  selectedRoleId.value = role.id
  selectedPermissionIds.value = role.permissions?.map(p => p.id) || []
}

// 创建角色
const handleCreateRole = () => {
  emit('create-role')
}

// 编辑角色
const handleEditRole = (role: Role) => {
  emit('edit-role', role)
}

// 删除角色
const handleDeleteRole = (role: Role) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除角色"${role.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: () => deleteRole(role.id)
  })
}

// 执行删除角色
const deleteRole = async (roleId: number) => {
  try {
    await roleApi.deleteRole(roleId)
    message.success('角色删除成功')
    await loadRoleList()
    // 如果删除的是当前选中的角色，清空选择
    if (selectedRoleId.value === roleId) {
      selectedRoleId.value = null
      selectedPermissionIds.value = []
    }
  } catch (error) {
    message.error('角色删除失败')
    console.error('角色删除失败:', error)
  }
}

// 刷新角色列表
const handleRefreshRoles = () => {
  loadRoleList()
}

// 保存角色权限
const handleSavePermissions = async () => {
  if (!selectedRole.value) return

  permissionSaving.value = true
  try {
    await roleApi.updateRolePermissions(selectedRole.value.id, {
      permissionIds: selectedPermissionIds.value
    })
    message.success('权限保存成功')
    await loadRoleList() // 重新加载角色列表
    // 重新选择当前角色以更新权限显示
    const updatedRole = roleList.value.find(r => r.id === selectedRole.value?.id)
    if (updatedRole) {
      handleSelectRole(updatedRole)
    }
  } catch (error) {
    message.error('权限保存失败')
    console.error('权限保存失败:', error)
  } finally {
    permissionSaving.value = false
  }
}

// 获取模块名称
const getModuleName = (module: string): string => {
  const moduleNames: Record<string, string> = {
    user: '用户管理',
    enterprise: '企业管理',
    department: '部门管理',
    role: '角色管理',
    order: '订单管理',
    production: '生产计划',
    inventory: '库存管理',
    salary_rate: '工价设置',
    salary_list: '工资列表',
    digital: '数字空间',
    equipment: '设备管理'
  }
  return moduleNames[module] || module
}

// 检查模块是否全选
const isModuleAllSelected = (module: string): boolean => {
  const modulePermissions = groupedPermissions.value[module] || []
  return modulePermissions.length > 0 &&
         modulePermissions.every(p => selectedPermissionIds.value.includes(p.id))
}

// 检查模块是否部分选中
const isModuleIndeterminate = (module: string): boolean => {
  const modulePermissions = groupedPermissions.value[module] || []
  const selectedCount = modulePermissions.filter(p => selectedPermissionIds.value.includes(p.id)).length
  return selectedCount > 0 && selectedCount < modulePermissions.length
}

// 切换模块权限
const toggleModulePermissions = (module: string, checked: boolean) => {
  const modulePermissions = groupedPermissions.value[module] || []
  const modulePermissionIds = modulePermissions.map(p => p.id)

  if (checked) {
    // 添加模块所有权限
    modulePermissionIds.forEach(id => {
      if (!selectedPermissionIds.value.includes(id)) {
        selectedPermissionIds.value.push(id)
      }
    })
  } else {
    // 移除模块所有权限
    selectedPermissionIds.value = selectedPermissionIds.value.filter(
      id => !modulePermissionIds.includes(id)
    )
  }
}

// 暴露方法给父组件
defineExpose({
  loadRoleList,
  selectedRole,
  groupedPermissions
})
</script>

<style scoped>
/* 权限管理和组织管理内容布局 */
.permission-content {
  flex: 1;
  display: flex;
  gap: 16px;
  min-height: 0;
}

/* Left: 角色列表 */
.left {
  width: 300px;
  flex-shrink: 0;
}

.left .ant-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.left .ant-card :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 0;
}

/* Right: 权限分配 */
.right {
  flex: 1;
  min-width: 0;
}

.right .ant-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.right .ant-card :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 0;
}

/* 权限管理样式 */
.role-list-container {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  max-height: 100%;
}

.role-list {
  background: transparent;
}

.role-item {
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.2s;
}

.role-item:hover {
  background-color: #f5f5f5;
}

.role-item-selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.role-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.role-name {
  font-weight: 500;
  color: #262626;
}

.role-description {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-code {
  font-size: 12px;
  color: #999;
  font-family: 'Courier New', monospace;
}

.role-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.role-permissions {
  font-size: 12px;
  color: #1890ff;
}

.permission-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-assignment {
  height: 100%;
  overflow-y: auto;
}

.system-role-notice {
  margin-bottom: 16px;
}

.permission-groups {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.permission-group {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
}

.permission-group-header {
  background: #fafafa;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.permission-group-content {
  padding: 16px;
}

.permission-checkboxes {
  width: 100%;
}

.permission-checkboxes .ant-checkbox-wrapper {
  margin-bottom: 8px;
}

.no-role-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>
