<!--
  企业信息卡片组件
  {{CHENGQI: Action: Added; Timestamp: 2025-01-04 15:40:00 +08:00; Reason: 重构UserManagementView.vue，拆分企业信息卡片; Principle_Applied: 单一职责原则;}}
-->

<template>
  <a-card :bordered="false" class="enterprise-card">
    <template #extra>
      <a-button
        v-if="canUpdate"
        type="primary"
        size="small"
        @click="handleEdit"
      >
        <EditOutlined />
        编辑企业信息
      </a-button>
    </template>

    <div class="enterprise-info">
      <div class="enterprise-basic">
        <div class="enterprise-logo">
          <img
            v-if="enterpriseInfo?.logoUrl"
            :src="enterpriseInfo.logoUrl"
            :alt="enterpriseInfo.name"
            class="logo-img"
          />
          <div v-else class="logo-placeholder">
            <BankOutlined />
          </div>
        </div>
        <div class="enterprise-details">
          <h3 class="enterprise-name">{{ enterpriseInfo?.name || '加载中...' }}</h3>
          <div class="enterprise-meta">
            <span class="enterprise-code">企业编号: {{ enterpriseInfo?.code || '-' }}</span>
            <span class="enterprise-id">企业ID: {{ enterpriseInfo?.id || '-' }}</span>
          </div>
          <div v-if="enterpriseInfo?.description" class="enterprise-description">
            {{ enterpriseInfo.description }}
          </div>
        </div>
      </div>
      <div class="enterprise-stats">
        <a-statistic
          title="总人数"
          :value="enterpriseInfo?.statistics?.userCount || 0"
          suffix="人"
          class="stat-item"
        />
        <a-statistic
          title="部门数"
          :value="enterpriseInfo?.statistics?.departmentCount || 0"
          suffix="个"
          class="stat-item"
        />
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { EditOutlined, BankOutlined } from '@ant-design/icons-vue'
import type { EnterpriseInfo } from '@/types/user'

interface Props {
  enterpriseInfo: EnterpriseInfo | null
  canUpdate: boolean
}

interface Emits {
  (e: 'edit'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const handleEdit = () => {
  emit('edit')
}
</script>

<style scoped>
.enterprise-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enterprise-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.enterprise-basic {
  display: flex;
  align-items: center;
  gap: 16px;
}

.enterprise-logo {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
}

.logo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logo-placeholder {
  font-size: 24px;
  color: #999;
}

.enterprise-details {
  flex: 1;
}

.enterprise-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.enterprise-meta {
  display: flex;
  gap: 24px;
  color: #666;
  font-size: 14px;
}

.enterprise-description {
  margin-top: 8px;
  color: #999;
  font-size: 13px;
  line-height: 1.4;
}

.enterprise-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enterprise-info {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .enterprise-basic {
    flex-direction: column;
    text-align: center;
  }

  .enterprise-meta {
    flex-direction: column;
    gap: 8px;
  }

  .enterprise-stats {
    gap: 16px;
  }
}
</style>
