<!--
  用户新增/编辑模态框组件
  {{CHENGQI: Action: Added; Timestamp: 2025-01-04 15:34:00 +08:00; Reason: 重构UserManagementView.vue，拆分用户模态框; Principle_Applied: 单一职责原则;}}
-->

<template>
  <a-modal
    :open="visible"
    :title="mode === 'create' ? '新增用户' : '编辑用户'"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="$emit('update:visible', $event)"
    width="600px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="用户名" name="username">
        <a-input
          v-model:value="formData.username"
          placeholder="请输入用户名"
          :disabled="mode === 'edit'"
        />
      </a-form-item>

      <a-form-item label="真实姓名" name="realName">
        <a-input
          v-model:value="formData.realName"
          placeholder="请输入真实姓名"
        />
      </a-form-item>

      <a-form-item label="邮箱" name="email">
        <a-input
          v-model:value="formData.email"
          placeholder="请输入邮箱（可选）"
          type="email"
        />
      </a-form-item>

      <a-form-item label="手机号" name="phone">
        <a-input
          v-model:value="formData.phone"
          placeholder="请输入手机号"
        />
      </a-form-item>

      <a-form-item label="所属部门" name="departmentId">
        <a-tree-select
          v-model:value="formData.departmentId"
          :tree-data="departmentTree"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          placeholder="请选择所属部门"
          tree-default-expand-all
          allow-clear
        />
      </a-form-item>

      <a-form-item label="角色" name="roleIds">
        <a-select
          v-model:value="formData.roleIds"
          mode="multiple"
          placeholder="请选择角色"
          :loading="roleLoading"
          allow-clear
        >
          <a-select-option
            v-for="role in roleList"
            :key="role.id"
            :value="role.id"
          >
            {{ role.name }}
            <span v-if="role.isSystem" class="text-gray-500">(系统角色)</span>
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item v-if="mode === 'create'" label="密码" name="password">
        <a-input-password
          v-model:value="formData.password"
          placeholder="请输入密码"
        />
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="formData.status">
          <a-radio :value="1">启用</a-radio>
          <a-radio :value="0">禁用</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import type { DepartmentTreeNode } from '@/types/user'
import type { Role } from '@/api/role'
import type { CreateUserRequest, UpdateUserRequest } from '@/types/user'

interface Props {
  visible: boolean
  loading: boolean
  mode: 'create' | 'edit'
  departmentTree: DepartmentTreeNode[]
  roleList: Role[]
  roleLoading: boolean
  initialData?: {
    username?: string
    realName?: string
    email?: string
    phone?: string
    departmentId?: number
    roleIds?: number[]
    status?: number
  }
  selectedDepartmentId?: number
}

interface FormData {
  username: string
  realName: string
  email: string
  phone: string
  departmentId?: number
  roleIds: number[]
  status: number
  password: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'ok', data: CreateUserRequest | UpdateUserRequest): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false,
  mode: 'create',
  departmentTree: () => [],
  roleList: () => [],
  roleLoading: false
})

const emit = defineEmits<Emits>()

const formRef = ref()
const formData = reactive<FormData>({
  username: '',
  realName: '',
  email: '',
  phone: '',
  departmentId: undefined,
  roleIds: [],
  status: 1,
  password: ''
})

// 表单验证规则
const formRules = computed(() => ({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度必须在3-50个字符之间', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '真实姓名长度必须在2-50个字符之间', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  departmentId: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  roleIds: [
    { required: true, type: 'array', min: 1, message: '请选择至少一个角色', trigger: 'change' }
  ],
  ...(props.mode === 'create' && {
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度必须在6-20个字符之间', trigger: 'blur' }
    ]
  })
}))

// 监听模态框显示状态，重置或填充表单
watch(() => props.visible, (visible) => {
  if (visible) {
    if (props.mode === 'edit' && props.initialData) {
      // 编辑模式：填充表单数据
      formData.username = props.initialData.username || ''
      formData.realName = props.initialData.realName || ''
      formData.email = props.initialData.email || ''
      formData.phone = props.initialData.phone || ''
      formData.departmentId = props.initialData.departmentId
      formData.roleIds = props.initialData.roleIds || []
      formData.status = props.initialData.status ?? 1
      formData.password = ''
    } else {
      // 创建模式：重置表单并设置默认值
      resetForm()
      // 设置默认部门
      if (props.mode === 'create' && props.selectedDepartmentId) {
        formData.departmentId = props.selectedDepartmentId
      }
    }
  } else if (!visible) {
    // 隐藏时重置表单
    resetForm()
  }
})

// 重置表单数据
const resetForm = () => {
  formData.username = ''
  formData.realName = ''
  formData.email = ''
  formData.phone = ''
  formData.departmentId = undefined
  formData.roleIds = []
  formData.status = 1
  formData.password = ''
}

// 处理确认
const handleOk = async () => {
  try {
    await formRef.value.validate()
    
    if (props.mode === 'create') {
      const createData: CreateUserRequest = {
        username: formData.username,
        realName: formData.realName,
        email: formData.email,
        phone: formData.phone,
        departmentId: formData.departmentId!,
        roleIds: formData.roleIds,
        password: formData.password,
        status: formData.status
      }
      emit('ok', createData)
    } else {
      const updateData: UpdateUserRequest = {
        realName: formData.realName,
        email: formData.email,
        phone: formData.phone,
        departmentId: formData.departmentId!,
        roleIds: formData.roleIds,
        status: formData.status
      }
      emit('ok', updateData)
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  // 不需要在这里重置，因为watch会在visible变为false时自动重置
}
</script>

<style scoped>
.text-gray-500 {
  color: #999;
}
</style>
