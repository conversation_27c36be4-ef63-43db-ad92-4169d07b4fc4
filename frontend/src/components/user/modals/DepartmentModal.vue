<!--
  部门新增/编辑模态框组件
  {{CHENGQI: Action: Added; Timestamp: 2025-01-04 15:32:00 +08:00; Reason: 重构UserManagementView.vue，拆分部门模态框; Principle_Applied: 单一职责原则;}}
-->

<template>
  <a-modal
    :open="visible"
    :title="mode === 'create' ? '新增部门' : '编辑部门'"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="$emit('update:visible', $event)"
    width="500px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="部门名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入部门名称" />
      </a-form-item>

      <a-form-item label="上级部门" name="parentId">
        <a-tree-select
          v-model:value="formData.parentId"
          :tree-data="departmentTreeSelectData"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          placeholder="请选择上级部门（不选择则默认在总部下）"
          allow-clear
          tree-default-expand-all
        />
      </a-form-item>

      <a-form-item label="部门描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入部门描述"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="排序号" name="sortOrder">
        <a-input-number
          v-model:value="formData.sortOrder"
          placeholder="请输入排序号"
          :min="0"
          style="width: 100%"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { DepartmentTreeNode } from '@/types/user'

interface Props {
  visible: boolean
  loading: boolean
  mode: 'create' | 'edit'
  departmentTree: DepartmentTreeNode[]
  currentEditingDepartmentId?: number | null
  initialData?: {
    name?: string
    parentId?: number
    description?: string
    sortOrder?: number
  }
}

interface FormData {
  name: string
  parentId?: number
  description: string
  sortOrder?: number
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'ok', data: FormData): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false,
  mode: 'create',
  departmentTree: () => [],
  currentEditingDepartmentId: null
})

const emit = defineEmits<Emits>()

const formRef = ref()
const formData = reactive<FormData>({
  name: '',
  parentId: undefined,
  description: '',
  sortOrder: undefined
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 100, message: '部门名称长度必须在2-100个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '部门描述不能超过500个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, message: '排序号必须是非负整数', trigger: 'blur' }
  ]
}

// 部门树选择数据（过滤当前编辑的部门）
const departmentTreeSelectData = computed(() => {
  const filterCurrentDepartment = (nodes: DepartmentTreeNode[]): DepartmentTreeNode[] => {
    return nodes
      .filter(node => node.id !== props.currentEditingDepartmentId)
      .map(node => ({
        ...node,
        children: node.children ? filterCurrentDepartment(node.children) : []
      }))
  }
  return filterCurrentDepartment(props.departmentTree)
})

// 监听模态框显示状态，重置或填充表单
watch(() => props.visible, (visible) => {
  if (visible) {
    if (props.mode === 'edit' && props.initialData) {
      // 编辑模式：填充表单数据
      formData.name = props.initialData.name || ''
      formData.parentId = props.initialData.parentId
      formData.description = props.initialData.description || ''
      formData.sortOrder = props.initialData.sortOrder
    } else {
      // 创建模式：重置表单
      resetForm()
    }
  } else if (!visible) {
    // 隐藏时重置表单
    resetForm()
  }
})

// 重置表单数据
const resetForm = () => {
  formData.name = ''
  formData.parentId = undefined
  formData.description = ''
  formData.sortOrder = undefined
}

// 处理确认
const handleOk = async () => {
  try {
    await formRef.value.validate()
    emit('ok', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  // 不需要在这里重置，因为watch会在visible变为false时自动重置
}
</script>
