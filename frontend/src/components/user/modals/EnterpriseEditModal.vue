<!--
  企业编辑模态框组件
  {{CHENGQI: Action: Added; Timestamp: 2025-01-04 15:30:00 +08:00; Reason: 重构UserManagementView.vue，拆分企业编辑模态框; Principle_Applied: 单一职责原则;}}
-->

<template>
  <a-modal
    :open="visible"
    title="编辑企业信息"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="$emit('update:visible', $event)"
    width="600px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="企业名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入企业名称" />
      </a-form-item>

      <a-form-item label="企业编码" name="code">
        <a-input
          v-model:value="formData.code"
          placeholder="请输入企业编码（大写字母、数字、下划线）"
          :disabled="!canEditCode"
        />
        <div v-if="!canEditCode" class="form-help">
          <small style="color: #999;">只有超级管理员可以修改企业编码</small>
        </div>
      </a-form-item>

      <a-form-item label="企业Logo" name="logoUrl">
        <a-input v-model:value="formData.logoUrl" placeholder="请输入Logo URL" />
      </a-form-item>

      <a-form-item label="企业描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入企业描述"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="formData.status">
          <a-radio :value="1">正常</a-radio>
          <a-radio :value="0">禁用</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { UpdateEnterpriseRequest } from '@/api/enterprise'

interface Props {
  visible: boolean
  loading: boolean
  enterpriseData?: {
    name: string
    code: string
    logoUrl?: string
    description?: string
    status: number
  }
  canEditCode: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'ok', data: UpdateEnterpriseRequest): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false,
  canEditCode: false
})

const emit = defineEmits<Emits>()

const formRef = ref()
const formData = reactive({
  name: '',
  code: '',
  logoUrl: '',
  description: '',
  status: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
    { min: 2, max: 100, message: '企业名称长度必须在2-100个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入企业编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '企业编码只能包含大写字母、数字和下划线', trigger: 'blur' },
    { min: 2, max: 50, message: '企业编码长度必须在2-50个字符之间', trigger: 'blur' }
  ],
  logoUrl: [
    { type: 'url', message: 'Logo URL格式不正确', trigger: 'blur' }
  ]
}

// 监听模态框显示状态，重置或填充表单
watch(() => props.visible, (visible) => {
  if (visible && props.enterpriseData) {
    // 显示时填充表单数据
    formData.name = props.enterpriseData.name
    formData.code = props.enterpriseData.code
    formData.logoUrl = props.enterpriseData.logoUrl || ''
    formData.description = props.enterpriseData.description || ''
    formData.status = props.enterpriseData.status
  } else if (!visible) {
    // 隐藏时重置表单
    resetForm()
  }
})

// 重置表单数据
const resetForm = () => {
  formData.name = ''
  formData.code = ''
  formData.logoUrl = ''
  formData.description = ''
  formData.status = 1
}

// 处理确认
const handleOk = async () => {
  try {
    await formRef.value.validate()
    
    const updateData: UpdateEnterpriseRequest = {
      name: formData.name,
      logoUrl: formData.logoUrl || undefined,
      description: formData.description || undefined,
      status: formData.status
    }

    // 只有超级管理员才能修改企业编码
    if (props.canEditCode) {
      updateData.code = formData.code
    }

    emit('ok', updateData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  // 不需要在这里重置，因为watch会在visible变为false时自动重置
}
</script>

<style scoped>
.form-help {
  margin-top: 4px;
}
</style>
