<!--
  重置密码模态框组件
  {{CHENGQI: Action: Added; Timestamp: 2025-01-04 15:36:00 +08:00; Reason: 重构UserManagementView.vue，拆分重置密码模态框; Principle_Applied: 单一职责原则;}}
-->

<template>
  <a-modal
    :open="visible"
    :title="`重置密码 - ${userName}`"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="$emit('update:visible', $event)"
    width="400px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="新密码" name="newPassword">
        <a-input-password
          v-model:value="formData.newPassword"
          placeholder="请输入新密码（6-20位）"
        />
      </a-form-item>

      <a-form-item label="确认密码" name="confirmPassword">
        <a-input-password
          v-model:value="formData.confirmPassword"
          placeholder="请再次输入新密码"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

interface Props {
  visible: boolean
  loading: boolean
  userName: string
}

interface FormData {
  newPassword: string
  confirmPassword: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'ok', data: { newPassword: string }): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false,
  userName: ''
})

const emit = defineEmits<Emits>()

const formRef = ref()
const formData = reactive<FormData>({
  newPassword: '',
  confirmPassword: ''
})

// 自定义验证器：确认密码
const validateConfirmPassword = (_rule: any, value: string) => {
  if (value && value !== formData.newPassword) {
    return Promise.reject(new Error('两次输入的密码不一致'))
  }
  return Promise.resolve()
}

// 表单验证规则
const formRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度必须在6-20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 监听模态框显示状态，重置表单
watch(() => props.visible, (visible) => {
  if (visible) {
    // 显示时重置表单数据
    resetForm()
  } else if (!visible) {
    // 隐藏时也重置表单
    resetForm()
  }
})

// 重置表单数据
const resetForm = () => {
  formData.newPassword = ''
  formData.confirmPassword = ''
}

// 处理确认
const handleOk = async () => {
  try {
    await formRef.value.validate()
    emit('ok', { newPassword: formData.newPassword })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  // 不需要在这里重置，因为watch会在visible变为false时自动重置
}
</script>
