<!--
  角色创建/编辑模态框组件
  {{CHENGQI: Action: Added; Timestamp: 2025-01-04 15:38:00 +08:00; Reason: 重构UserManagementView.vue，拆分角色模态框; Principle_Applied: 单一职责原则;}}
-->

<template>
  <a-modal
    :open="visible"
    :title="mode === 'create' ? '新增角色' : '编辑角色'"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="$emit('update:visible', $event)"
    width="600px"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="角色名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入角色名称" />
      </a-form-item>

      <a-form-item label="角色编码" name="code">
        <a-input
          v-model:value="formData.code"
          placeholder="请输入角色编码（大写字母、数字、下划线）"
          :disabled="mode === 'edit'"
        />
      </a-form-item>

      <a-form-item label="角色描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入角色描述"
          :rows="3"
        />
      </a-form-item>

      <a-form-item label="权限配置">
        <div class="permission-selection">
          <div
            v-for="(permissions, module) in groupedPermissions"
            :key="module"
            class="permission-group"
          >
            <div class="permission-group-header">
              <a-checkbox
                :checked="isModuleAllSelected(module)"
                :indeterminate="isModuleIndeterminate(module)"
                @change="toggleModulePermissions(module, $event.target.checked)"
              >
                {{ getModuleName(module) }}
              </a-checkbox>
            </div>
            <div class="permission-group-content">
              <a-checkbox-group
                v-model:value="formData.permissionIds"
                class="permission-checkboxes"
              >
                <a-row :gutter="[16, 8]">
                  <a-col
                    v-for="permission in permissions"
                    :key="permission.id"
                    :span="12"
                  >
                    <a-checkbox :value="permission.id">
                      {{ permission.name }}
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </div>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { Permission, CreateRoleData, UpdateRoleData } from '@/api/role'

interface Props {
  visible: boolean
  loading: boolean
  mode: 'create' | 'edit'
  groupedPermissions: Record<string, Permission[]>
  initialData?: {
    name?: string
    code?: string
    description?: string
    permissionIds?: number[]
  }
}

interface FormData {
  name: string
  code: string
  description: string
  permissionIds: number[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'ok', data: CreateRoleData | UpdateRoleData): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false,
  mode: 'create',
  groupedPermissions: () => ({})
})

const emit = defineEmits<Emits>()

const formRef = ref()
const formData = reactive<FormData>({
  name: '',
  code: '',
  description: '',
  permissionIds: []
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度必须在2-50个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { pattern: /^[A-Z_][A-Z0-9_]*$/, message: '角色编码只能包含大写字母、数字和下划线，且必须以字母或下划线开头', trigger: 'blur' },
    { min: 2, max: 50, message: '角色编码长度必须在2-50个字符之间', trigger: 'blur' }
  ]
}

// 获取模块名称
const getModuleName = (module: string): string => {
  const moduleNames: Record<string, string> = {
    user: '用户管理',
    enterprise: '企业管理',
    department: '部门管理',
    role: '角色管理',
    order: '订单管理',
    production: '生产计划',
    inventory: '库存管理',
    salary_rate: '工价设置',
    salary_list: '工资列表',
    digital: '数字空间',
    equipment: '设备管理'
  }
  return moduleNames[module] || module
}

// 检查模块是否全选
const isModuleAllSelected = (module: string): boolean => {
  const modulePermissions = props.groupedPermissions[module] || []
  return modulePermissions.length > 0 &&
         modulePermissions.every(p => formData.permissionIds.includes(p.id))
}

// 检查模块是否部分选中
const isModuleIndeterminate = (module: string): boolean => {
  const modulePermissions = props.groupedPermissions[module] || []
  const selectedCount = modulePermissions.filter(p => formData.permissionIds.includes(p.id)).length
  return selectedCount > 0 && selectedCount < modulePermissions.length
}

// 切换模块权限
const toggleModulePermissions = (module: string, checked: boolean) => {
  const modulePermissions = props.groupedPermissions[module] || []
  const modulePermissionIds = modulePermissions.map(p => p.id)

  if (checked) {
    // 添加模块所有权限
    modulePermissionIds.forEach(id => {
      if (!formData.permissionIds.includes(id)) {
        formData.permissionIds.push(id)
      }
    })
  } else {
    // 移除模块所有权限
    formData.permissionIds = formData.permissionIds.filter(
      id => !modulePermissionIds.includes(id)
    )
  }
}

// 监听模态框显示状态，重置或填充表单
watch(() => props.visible, (visible) => {
  if (visible) {
    if (props.mode === 'edit' && props.initialData) {
      // 编辑模式：填充表单数据
      formData.name = props.initialData.name || ''
      formData.code = props.initialData.code || ''
      formData.description = props.initialData.description || ''
      formData.permissionIds = props.initialData.permissionIds || []
    } else {
      // 创建模式：重置表单
      resetForm()
    }
  } else if (!visible) {
    // 隐藏时重置表单
    resetForm()
  }
})

// 重置表单数据
const resetForm = () => {
  formData.name = ''
  formData.code = ''
  formData.description = ''
  formData.permissionIds = []
}

// 处理确认
const handleOk = async () => {
  try {
    await formRef.value.validate()
    
    if (props.mode === 'create') {
      const createData: CreateRoleData = {
        name: formData.name,
        code: formData.code,
        description: formData.description,
        permissionIds: formData.permissionIds
      }
      emit('ok', createData)
    } else {
      const updateData: UpdateRoleData = {
        name: formData.name,
        description: formData.description,
        permissionIds: formData.permissionIds
      }
      emit('ok', updateData)
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  // 不需要在这里重置，因为watch会在visible变为false时自动重置
}
</script>

<style scoped>
.permission-selection {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.permission-group {
  border-bottom: 1px solid #f0f0f0;
}

.permission-group:last-child {
  border-bottom: none;
}

.permission-group-header {
  background: #fafafa;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.permission-group-content {
  padding: 16px;
}

.permission-checkboxes {
  width: 100%;
}

.permission-checkboxes .ant-checkbox-wrapper {
  margin-bottom: 8px;
}
</style>
