<!--
  用户列表组件
  {{CHENGQI: Action: Modified; Timestamp: 2025-01-04 16:00:00 +08:00; Reason: 将搜索功能重新集成到用户列表组件中，恢复原有布局; Principle_Applied: 保持原有设计一致性;}}
-->

<template>
  <a-card :bordered="false" class="user-list-card">
    <template #title>
      <div class="user-list-header">
        <span>用户列表</span>
        <span v-if="selectedDepartmentName" class="department-filter">
          - {{ selectedDepartmentName }}
        </span>
      </div>
    </template>

    <template #extra>
      <a-space>
        <a-button
          type="primary"
          @click="handleCreate"
          :disabled="!canCreate"
        >
          <PlusOutlined />
          新增用户
        </a-button>
        <a-button @click="handleRefresh" :loading="loading">
          <ReloadOutlined />
          刷新
        </a-button>
      </a-space>
    </template>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="搜索用户名、姓名、邮箱、手机号"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.status"
            placeholder="状态"
            allow-clear
            style="width: 100%"
          >
            <a-select-option :value="1">正常</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 用户表格 -->
    <a-table
      :columns="columns"
      :data-source="userList"
      :loading="loading"
      :pagination="pagination"
      :scroll="{ x: 1200 }"
      row-key="id"
      @change="handleTableChange"
      class="user-table"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'avatar'">
          <a-avatar :src="record.avatarUrl" :size="32">
            {{ record.realName?.charAt(0) }}
          </a-avatar>
        </template>

        <template v-else-if="column.key === 'userInfo'">
          <div class="user-info-cell">
            <div class="user-name">{{ record.realName }}</div>
            <div class="user-username">{{ record.username }}</div>
          </div>
        </template>

        <template v-else-if="column.key === 'contact'">
          <div class="contact-info">
            <div v-if="record.email" class="contact-item">
              <MailOutlined />
              {{ record.email }}
            </div>
            <div v-if="record.phone" class="contact-item">
              <PhoneOutlined />
              {{ record.phone }}
            </div>
          </div>
        </template>

        <template v-else-if="column.key === 'department'">
          <span>{{ record.department?.name || '-' }}</span>
        </template>

        <template v-else-if="column.key === 'roles'">
          <a-space wrap>
            <a-tag
              v-for="role in record.roles"
              :key="role.id"
              color="blue"
            >
              {{ role.name }}
            </a-tag>
            <span v-if="!record.roles?.length">-</span>
          </a-space>
        </template>

        <template v-else-if="column.key === 'status'">
          <a-tag :color="record.status === 1 ? 'green' : 'red'">
            {{ record.status === 1 ? '正常' : '禁用' }}
          </a-tag>
        </template>

        <template v-else-if="column.key === 'lastLoginAt'">
          <span>{{ formatDateTime(record.lastLoginAt) }}</span>
        </template>

        <template v-else-if="column.key === 'actions'">
          <a-space>
            <a-button
              type="link"
              size="small"
              @click="handleEdit(record)"
              :disabled="!canUpdate"
            >
              编辑
            </a-button>
            <a-button
              type="link"
              size="small"
              @click="handleResetPassword(record)"
              :disabled="!canUpdate"
            >
              重置密码
            </a-button>
            <a-popconfirm
              v-if="!isSystemAdmin(record)"
              title="确定要删除这个用户吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record.id)"
            >
              <a-button
                type="link"
                size="small"
                danger
                :disabled="!canDelete"
              >
                删除
              </a-button>
            </a-popconfirm>
            <a-tooltip v-else title="系统管理员不能删除">
              <a-button
                type="link"
                size="small"
                danger
                disabled
              >
                删除
              </a-button>
            </a-tooltip>
          </a-space>
        </template>
      </template>
    </a-table>
  </a-card>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  ReloadOutlined,
  MailOutlined,
  PhoneOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import type { User, UserTableColumn, UserListQuery } from '@/types/user'

interface Props {
  userList: User[]
  loading: boolean
  pagination: any
  selectedDepartmentName: string
  canCreate: boolean
  canUpdate: boolean
  canDelete: boolean
  initialSearchForm?: Partial<UserListQuery>
}

interface Emits {
  (e: 'create'): void
  (e: 'edit', user: User): void
  (e: 'delete', userId: number): void
  (e: 'reset-password', user: User): void
  (e: 'table-change', pagination: any): void
  (e: 'refresh'): void
  (e: 'search', searchForm: UserListQuery): void
  (e: 'reset'): void
}

const props = withDefaults(defineProps<Props>(), {
  initialSearchForm: () => ({})
})
const emit = defineEmits<Emits>()

// 搜索表单
const searchForm = reactive<UserListQuery>({
  keyword: '',
  status: undefined,
  departmentId: undefined,
  ...props.initialSearchForm
})

// 表格列配置
const columns: UserTableColumn[] = [
  {
    title: '头像',
    dataIndex: 'avatar',
    key: 'avatar',
    width: 60,
    fixed: 'left'
  },
  {
    title: '用户信息',
    dataIndex: 'userInfo',
    key: 'userInfo',
    width: 150,
    fixed: 'left'
  },
  {
    title: '联系方式',
    dataIndex: 'contact',
    key: 'contact',
    width: 200
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '角色',
    dataIndex: 'roles',
    key: 'roles',
    width: 150
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '最后登录',
    dataIndex: 'lastLoginAt',
    key: 'lastLoginAt',
    width: 150
  },
  {
    title: '操作',
    dataIndex: 'actions',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 处理创建用户
const handleCreate = () => {
  emit('create')
}

// 处理编辑用户
const handleEdit = (user: User) => {
  emit('edit', user)
}

// 处理删除用户
const handleDelete = (userId: number) => {
  emit('delete', userId)
}

// 处理重置密码
const handleResetPassword = (user: User) => {
  emit('reset-password', user)
}

// 处理表格变化
const handleTableChange = (pagination: any) => {
  emit('table-change', pagination)
}

// 处理刷新
const handleRefresh = () => {
  emit('refresh')
}

// 处理搜索
const handleSearch = () => {
  emit('search', { ...searchForm })
}

// 处理重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = undefined
  searchForm.departmentId = undefined
  emit('reset')
}

// 判断是否为系统管理员
const isSystemAdmin = (user: User): boolean => {
  if (!user.roles || user.roles.length === 0) return false
  return user.roles.some((role: { code: string }) =>
    role.code === 'ENTERPRISE_ADMIN' || role.code === 'SUPER_ADMIN'
  )
}

// 格式化日期时间
const formatDateTime = (dateTime?: string): string => {
  if (!dateTime) return '-'
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 暴露搜索表单给父组件
defineExpose({
  searchForm
})
</script>

<style scoped>
.user-list-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.user-list-card :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 0;
}

.user-list-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.department-filter {
  color: #1890ff;
  font-weight: normal;
}

.user-table {
  background: white;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* 表格单元格样式 */
.user-info-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 500;
  color: #262626;
}

.user-username {
  font-size: 12px;
  color: #999;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

/* 搜索栏样式 */
.search-bar {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-bar :deep(.ant-row) {
    flex-direction: column;
    gap: 8px;
  }

  .search-bar :deep(.ant-col) {
    width: 100% !important;
  }
}
</style>
