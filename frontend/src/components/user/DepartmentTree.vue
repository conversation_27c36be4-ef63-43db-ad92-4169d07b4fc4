<!--
  部门树组件
  {{CHENGQI: Action: Added; Timestamp: 2025-01-04 15:44:00 +08:00; Reason: 重构UserManagementView.vue，拆分部门树; Principle_Applied: 单一职责原则;}}
-->

<template>
  <a-card title="部门列表" :bordered="false" class="department-card">
    <template #extra>
      <a-space>
        <a-button
          v-if="canCreate"
          type="primary"
          size="small"
          @click="handleCreate()"
        >
          <PlusOutlined />
          新增部门
        </a-button>
        <a-button
          type="text"
          size="small"
          @click="handleRefresh"
          :loading="loading"
        >
          <ReloadOutlined />
        </a-button>
      </a-space>
    </template>

    <div class="department-tree-container">
      <a-tree
        v-if="departmentTree.length > 0"
        :tree-data="departmentTree"
        :field-names="{ title: 'name', key: 'id', children: 'children' }"
        :selected-keys="selectedKeys"
        :expanded-keys="expandedKeys"
        @select="handleSelect"
        @expand="handleExpand"
        class="department-tree"
      >
        <template #title="{ id, name, userCount }">
          <div 
            class="department-node" 
            @mouseenter="showActions(id)" 
            @mouseleave="hideActions"
          >
            <span class="department-info">
              {{ name }}
              <span class="user-count">({{ userCount }}人)</span>
            </span>
            <div v-if="hoveredDepartmentId === id" class="department-actions">
              <a-button
                v-if="canCreate"
                type="text"
                size="small"
                @click.stop="handleCreate(id)"
                title="新增子部门"
              >
                <PlusOutlined />
              </a-button>
              <a-button
                v-if="canUpdate"
                type="text"
                size="small"
                @click.stop="handleEdit(id)"
                title="编辑部门"
              >
                <EditOutlined />
              </a-button>
              <a-button
                v-if="canDelete"
                type="text"
                size="small"
                danger
                @click.stop="handleDelete(id)"
                title="删除部门"
              >
                <DeleteOutlined />
              </a-button>
            </div>
          </div>
        </template>
      </a-tree>

      <a-empty
        v-else-if="!loading"
        description="暂无部门数据"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />

      <div v-else class="loading-container">
        <a-spin />
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Empty } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { DepartmentTreeNode } from '@/types/user'

interface Props {
  departmentTree: DepartmentTreeNode[]
  loading: boolean
  selectedKeys: number[]
  expandedKeys: number[]
  canCreate: boolean
  canUpdate: boolean
  canDelete: boolean
}

interface Emits {
  (e: 'select', selectedKeys: number[], info: any): void
  (e: 'expand', expandedKeys: number[]): void
  (e: 'create', parentId?: number): void
  (e: 'edit', departmentId: number): void
  (e: 'delete', departmentId: number): void
  (e: 'refresh'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const hoveredDepartmentId = ref<number | null>(null)

// 处理部门选择
const handleSelect = (selectedKeys: number[], info: any) => {
  emit('select', selectedKeys, info)
}

// 处理部门展开/收起
const handleExpand = (expandedKeys: number[]) => {
  emit('expand', expandedKeys)
}

// 处理创建部门
const handleCreate = (parentId?: number) => {
  emit('create', parentId)
}

// 处理编辑部门
const handleEdit = (departmentId: number) => {
  emit('edit', departmentId)
}

// 处理删除部门
const handleDelete = (departmentId: number) => {
  emit('delete', departmentId)
}

// 处理刷新
const handleRefresh = () => {
  emit('refresh')
}

// 显示部门操作按钮
const showActions = (departmentId: number) => {
  hoveredDepartmentId.value = departmentId
}

// 隐藏部门操作按钮
const hideActions = () => {
  hoveredDepartmentId.value = null
}
</script>

<style scoped>
.department-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.department-card :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 0;
}

.department-tree-container {
  flex: 1;
  overflow-y: auto;
}

.department-tree {
  background: transparent;
}

.department-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 2px 0;
}

.department-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.department-actions {
  display: flex;
  gap: 4px;
  opacity: 0.8;
}

.department-actions .ant-btn {
  padding: 0 4px;
  height: 24px;
  line-height: 22px;
  border: none;
  box-shadow: none;
}

.department-actions .ant-btn:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.user-count {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
