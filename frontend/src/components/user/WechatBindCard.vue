<!--
  微信绑定卡片组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-23 12:36:52 +08:00; Reason: Task-008 微信绑定组件开发, 创建微信绑定UI组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <a-card title="微信绑定" :bordered="false" class="wechat-bind-card">
    <template #extra>
      <a-tag
        :color="wechatStatusColor"
        class="status-tag"
      >
        {{ wechatStatusText }}
      </a-tag>
    </template>

    <!-- 已绑定状态 -->
    <div v-if="userStore.isWechatBound" class="wechat-bound">
      <div class="wechat-info">
        <div class="wechat-avatar">
          <a-avatar
            :size="60"
            :src="userStore.wechatAvatar"
            class="avatar"
          >
            <template #icon>
              <WechatOutlined />
            </template>
          </a-avatar>
        </div>
        <div class="wechat-details">
          <div class="wechat-nickname">
            <strong>{{ userStore.wechatNickname || '微信用户' }}</strong>
          </div>
          <div class="wechat-meta">
            <div class="bind-time">
              <CalendarOutlined />
              绑定时间: {{ userStore.wechatBoundTime }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="action-buttons">
        <a-button
          type="primary"
          danger
          :loading="unbinding"
          @click="showUnbindConfirm"
        >
          <DisconnectOutlined />
          解绑微信
        </a-button>
      </div>
    </div>

    <!-- 未绑定状态 -->
    <div v-else class="wechat-unbound">
      <div class="unbound-info">
        <div class="unbound-icon">
          <WechatOutlined style="font-size: 48px; color: #ccc;" />
        </div>
        <div class="unbound-text">
          <h4>未绑定微信</h4>
          <p>绑定微信后，可以使用微信快速登录</p>
        </div>
      </div>

      <div class="action-buttons">
        <a-button
          type="primary"
          :loading="binding"
          @click="startBind"
        >
          <WechatOutlined />
          绑定微信
        </a-button>
      </div>
    </div>

    <!-- 绑定过程中的二维码显示 -->
    <a-modal
      v-model:open="showQrCode"
      title="微信绑定"
      :footer="null"
      :closable="!binding"
      :maskClosable="!binding"
      width="400px"
      centered
    >
      <div class="qr-code-container">
        <div v-if="qrCodeUrl" class="qr-code">
          <img :src="qrCodeUrl" alt="微信绑定二维码" class="qr-image" />
          <p class="qr-tip">请使用微信扫描二维码进行绑定</p>
        </div>
        <div v-else class="qr-loading">
          <a-spin size="large" />
          <p>正在生成二维码...</p>
        </div>
        
        <div class="qr-actions">
          <a-button
            v-if="!binding"
            @click="cancelBind"
          >
            取消绑定
          </a-button>
          <a-button
            v-if="authUrl"
            type="link"
            @click="openWechatAuth"
          >
            在新窗口中打开
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 解绑确认对话框 -->
    <a-modal
      v-model:open="showUnbindModal"
      title="确认解绑"
      :confirm-loading="unbinding"
      @ok="confirmUnbind"
      @cancel="cancelUnbind"
    >
      <div class="unbind-confirm">
        <ExclamationCircleOutlined style="color: #faad14; font-size: 22px; margin-right: 8px;" />
        <span>确定要解绑微信账号吗？解绑后将无法使用微信快速登录。</span>
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  WechatOutlined,
  CalendarOutlined,
  DisconnectOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { wechatApi, wechatUtils } from '@/api/wechat'

// Props 和 Emits
interface Props {
  loading?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  disabled: false
})

const emit = defineEmits<{
  bind: []
  unbind: []
  refresh: []
}>()

// 状态管理
const userStore = useUserStore()

// 响应式状态
const binding = ref(false)
const unbinding = ref(false)
const showQrCode = ref(false)
const showUnbindModal = ref(false)
const authUrl = ref('')
const qrCodeUrl = ref('')
const bindingTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const wechatStatusText = computed(() => {
  return wechatApi.getStatusText(userStore.wechatBindInfo.status)
})

const wechatStatusColor = computed(() => {
  return wechatApi.getStatusColor(userStore.wechatBindInfo.status)
})

// 方法
const startBind = async () => {
  if (props.disabled || binding.value) return

  try {
    binding.value = true
    showQrCode.value = true

    // 获取微信授权URL
    const authUrlResponse = await userStore.getWechatAuthUrl()
    authUrl.value = authUrlResponse

    // 生成二维码
    qrCodeUrl.value = wechatApi.generateQrCodeUrl(authUrlResponse)

    // 开始轮询检查绑定状态
    startBindingCheck()

    emit('bind')
  } catch (error) {
    console.error('开始微信绑定失败:', error)
    message.error('获取微信授权失败，请稍后重试')
    cancelBind()
  }
}

const cancelBind = () => {
  binding.value = false
  showQrCode.value = false
  authUrl.value = ''
  qrCodeUrl.value = ''
  stopBindingCheck()
}

const openWechatAuth = () => {
  if (authUrl.value) {
    wechatUtils.openWechatAuth(authUrl.value)
  }
}

const startBindingCheck = () => {
  // 每3秒检查一次绑定状态
  bindingTimer.value = setInterval(async () => {
    try {
      await userStore.refreshUserProfile()
      if (userStore.isWechatBound) {
        // 绑定成功
        message.success('微信绑定成功！')
        cancelBind()
        emit('refresh')
      }
    } catch (error) {
      console.error('检查绑定状态失败:', error)
    }
  }, 3000)

  // 30秒后自动停止检查
  setTimeout(() => {
    if (binding.value) {
      message.warning('绑定超时，请重试')
      cancelBind()
    }
  }, 30000)
}

const stopBindingCheck = () => {
  if (bindingTimer.value) {
    clearInterval(bindingTimer.value)
    bindingTimer.value = null
  }
}

const showUnbindConfirm = () => {
  if (props.disabled || unbinding.value) return
  showUnbindModal.value = true
}

const confirmUnbind = async () => {
  try {
    unbinding.value = true
    await userStore.unbindWechat()
    message.success('微信解绑成功！')
    showUnbindModal.value = false
    emit('unbind')
    emit('refresh')
  } catch (error) {
    console.error('微信解绑失败:', error)
    message.error('微信解绑失败，请稍后重试')
  } finally {
    unbinding.value = false
  }
}

const cancelUnbind = () => {
  showUnbindModal.value = false
}

// 生命周期
onMounted(() => {
  // 组件挂载时检查是否支持微信绑定
  if (!wechatUtils.isSupportWechatBind()) {
    console.warn('当前环境不支持微信绑定')
  }
})

onUnmounted(() => {
  // 组件卸载时清理定时器
  stopBindingCheck()
})
</script>

<style scoped>
.wechat-bind-card .status-tag {
  font-weight: 500;
}

.wechat-bind-card .wechat-bound .wechat-info {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.wechat-bind-card .wechat-bound .wechat-info .wechat-avatar {
  margin-right: 16px;
}

.wechat-bind-card .wechat-bound .wechat-info .wechat-avatar .avatar {
  border: 2px solid #f0f0f0;
}

.wechat-bind-card .wechat-bound .wechat-info .wechat-details {
  flex: 1;
}

.wechat-bind-card .wechat-bound .wechat-info .wechat-details .wechat-nickname {
  font-size: 16px;
  margin-bottom: 8px;
  color: #262626;
}

.wechat-bind-card .wechat-bound .wechat-info .wechat-details .wechat-meta .bind-time {
  color: #8c8c8c;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.wechat-bind-card .wechat-bound .action-buttons {
  text-align: center;
}

.wechat-bind-card .wechat-unbound {
  text-align: center;
}

.wechat-bind-card .wechat-unbound .unbound-info {
  margin-bottom: 24px;
}

.wechat-bind-card .wechat-unbound .unbound-info .unbound-icon {
  margin-bottom: 16px;
}

.wechat-bind-card .wechat-unbound .unbound-info .unbound-text h4 {
  margin-bottom: 8px;
  color: #262626;
}

.wechat-bind-card .wechat-unbound .unbound-info .unbound-text p {
  color: #8c8c8c;
  margin: 0;
}

.wechat-bind-card .wechat-unbound .action-buttons {
  text-align: center;
}

.qr-code-container {
  text-align: center;
}

.qr-code-container .qr-code .qr-image {
  width: 200px;
  height: 200px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
}

.qr-code-container .qr-code .qr-tip {
  color: #8c8c8c;
  margin-bottom: 24px;
}

.qr-code-container .qr-loading {
  padding: 40px 0;
}

.qr-code-container .qr-loading p {
  margin-top: 16px;
  color: #8c8c8c;
}

.qr-code-container .qr-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.unbind-confirm {
  display: flex;
  align-items: center;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wechat-bind-card .wechat-bound .wechat-info {
    flex-direction: column;
    text-align: center;
  }

  .wechat-bind-card .wechat-bound .wechat-info .wechat-avatar {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .qr-code-container .qr-code .qr-image {
    width: 160px;
    height: 160px;
  }
}
</style>
