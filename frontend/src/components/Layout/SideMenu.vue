<!--
  侧边栏菜单组件
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-005 基础布局, 创建侧边栏菜单; Principle_Applied: 权限控制菜单;}}
-->

<template>
  <div class="side-menu-container">
    <a-menu
      v-model:selectedKeys="selectedKeys"
      v-model:openKeys="openKeys"
      mode="inline"
      theme="light"
      :inline-collapsed="collapsed"
      class="side-menu"
      @click="handleMenuClick"
    >
      <template v-for="item in menuItems" :key="item.key">
        <a-menu-item
          v-if="(!item.children || item.children.length === 0) && hasMenuPermission(item)"
          :key="item.key"
          :disabled="item.disabled"
        >
          <component :is="item.icon" />
          <span>{{ item.title }}</span>
        </a-menu-item>

        <a-sub-menu
          v-else-if="item.children && item.children.length > 0 && hasMenuPermission(item)"
          :key="`submenu-${item.key}`"
          :disabled="item.disabled"
        >
          <template #icon>
            <component :is="item.icon" />
          </template>
          <template #title>{{ item.title }}</template>

          <a-menu-item
            v-for="child in item.children"
            :key="child.key"
            :disabled="child.disabled"
            v-show="hasMenuPermission(child)"
          >
            <span>{{ child.title }}</span>
          </a-menu-item>
        </a-sub-menu>
      </template>
    </a-menu>

    <!-- 用户管理区域 - 放在菜单底部 -->
    <div class="user-management" :class="{ collapsed }">
      <!-- 用户信息Popover -->
      <a-popover
        v-model:open="userMenuVisible"
        placement="topLeft"
        trigger="click"
        :arrow="false"
        overlayClassName="user-menu-popover"
      >
        <template #content>
          <div class="user-menu-content">
            <div class="user-info-header">
              <a-avatar
                :size="50"
                :src="userStore.user?.avatarUrl"
                class="user-avatar-large"
              >
                {{ userStore.user?.realName?.charAt(0) }}
              </a-avatar>
              <div class="user-info-text">
                <div class="user-name-large">{{ userStore.user?.realName }}</div>
                <div class="user-enterprise">{{ userStore.user?.enterprise?.name }}</div>
              </div>
            </div>

            <a-divider style="margin: 12px 0;" />

            <div class="user-menu-actions">
              <!-- 组织管理 - 只有有组织管理权限的用户才显示 -->
              <div
                v-if="userStore.hasPermission('user:view')"
                class="menu-action-item"
                @click="handleOrganizationManagement"
              >
                <BankOutlined class="menu-icon" />
                <span>组织管理</span>
              </div>

              <!-- 个人信息 - 所有用户都可以访问 -->
              <div
                class="menu-action-item"
                @click="handlePersonalInfo"
              >
                <UserOutlined class="menu-icon" />
                <span>个人信息</span>
              </div>

              <!-- 退出登录 -->
              <div
                class="menu-action-item logout-item"
                @click="handleLogout"
              >
                <LogoutOutlined class="menu-icon" />
                <span>退出登录</span>
              </div>
            </div>
          </div>
        </template>

        <div v-if="!collapsed" class="user-info-item">
          <a-avatar
            :size="40"
            :src="userStore.user?.avatarUrl"
            class="user-avatar"
          >
            {{ userStore.user?.realName?.charAt(0) }}
          </a-avatar>
          <div class="user-details">
            <div class="user-name">{{ userStore.user?.realName }}</div>
            <div class="user-title">{{ userStore.user?.enterprise?.name }}</div>
          </div>
        </div>

        <!-- 折叠状态下只显示头像 -->
        <div v-if="collapsed" class="user-info-collapsed">
          <a-avatar
            :size="32"
            :src="userStore.user?.avatarUrl"
            class="user-avatar"
          >
            {{ userStore.user?.realName?.charAt(0) }}
          </a-avatar>
        </div>
      </a-popover>

      <!-- 折叠展开按钮 -->
      <a-button
        type="text"
        @click="props.toggleCollapsed"
        class="collapse-trigger"
        :class="{ collapsed }"
        size="small"
      >
        <template #icon>
          <MenuUnfoldOutlined v-if="collapsed" />
          <MenuFoldOutlined v-else />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ShoppingOutlined,
  ScheduleOutlined,
  DollarOutlined,
  CloudOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BankOutlined,
  UserOutlined,
  LogoutOutlined,
  ToolOutlined,
  SettingOutlined,
  UnorderedListOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useUserStore } from '../../stores/user'



interface MenuItem {
  key: string
  title: string
  icon?: any // 子菜单项可以没有图标
  path?: string
  permissions?: string[]
  roles?: string[]
  disabled?: boolean
  children?: MenuItem[]
  permissionMode?: 'all' | 'any' // 权限检查模式：all需要所有权限，any需要任一权限
}

interface Props {
  collapsed: boolean
  toggleCollapsed: () => void
}

const props = defineProps<Props>()

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 菜单项配置
const menuItems: MenuItem[] = [
  {
    key: 'enterprises',
    title: '企业管理',
    icon: BankOutlined,
    path: '/enterprises',
    permissions: ['enterprise:view']
  },
  {
    key: 'equipment',
    title: '设备管理',
    icon: ToolOutlined,
    path: '/equipment',
    permissions: ['equipment:view']
  },
  {
    key: 'orders',
    title: '订单管理',
    icon: ShoppingOutlined,
    path: '/orders',
    permissions: ['order:view']
  },
  {
    key: 'production',
    title: '生产计划',
    icon: ScheduleOutlined,
    path: '/production',
    permissions: ['production:view']
  },
  {
    key: 'salary',
    title: '工资管理',
    icon: DollarOutlined,
    children: [
      {
        key: 'salary-rates',
        title: '工价设置',
        path: '/salary/rates',
        permissions: [
          'salary_rate:view', 'salary_rate:create', 'salary_rate:update', 'salary_rate:delete'
        ],
        permissionMode: 'any'
      },
      {
        key: 'salary-list',
        title: '工资列表',
        path: '/salary/list',
        permissions: [
          'salary_list:view', 'salary_list:create', 'salary_list:update', 'salary_list:delete',
          'salary_list:export', 'salary_list:approve'
        ],
        permissionMode: 'any'
      }
    ]
  },
  {
    key: 'digital',
    title: '数字空间',
    icon: CloudOutlined,
    path: '/digital',
    permissions: ['digital:view']
  }
]

// 当前选中的菜单项
const selectedKeys = ref<string[]>([])
// 当前展开的子菜单
const openKeys = ref<string[]>([])
// 用户菜单弹框显示状态
const userMenuVisible = ref(false)

// 根据当前路由设置选中的菜单项
const updateSelectedKeys = () => {
  const currentPath = route.path
  
  // 查找匹配的菜单项
  const findMenuKey = (items: MenuItem[], path: string): string | null => {
    for (const item of items) {
      if (item.path === path) {
        return item.key
      }
      if (item.children) {
        const childKey = findMenuKey(item.children, path)
        if (childKey) {
          // 如果是子菜单项，同时展开父菜单
          if (!openKeys.value.includes(item.key)) {
            openKeys.value.push(item.key)
          }
          return childKey
        }
      }
    }
    return null
  }
  
  const menuKey = findMenuKey(menuItems, currentPath)
  if (menuKey) {
    selectedKeys.value = [menuKey]
  }
}

// 检查菜单权限
const hasMenuPermission = (item: MenuItem): boolean => {
  // 如果有子菜单，检查是否有任一子菜单的权限
  if (item.children && item.children.length > 0) {
    return item.children.some(child => hasMenuPermission(child))
  }

  // 如果没有权限要求，默认显示
  if (!item.permissions && !item.roles) {
    return true
  }

  // 检查角色权限
  if (item.roles && item.roles.length > 0) {
    const hasRole = userStore.hasRoles(item.roles)
    if (!hasRole) {
      return false
    }
  }

  // 检查功能权限
  if (item.permissions && item.permissions.length > 0) {
    if (item.permissionMode === 'any') {
      // 任一权限模式：拥有任一权限即可
      return userStore.hasAnyPermission(item.permissions)
    } else {
      // 默认全部权限模式：需要拥有所有权限
      return userStore.hasPermissions(item.permissions)
    }
  }

  return true
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string | number }) => {
  // 查找对应的菜单项
  const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
    for (const item of items) {
      if (item.key === targetKey) {
        return item
      }
      if (item.children) {
        const child = findMenuItem(item.children, targetKey)
        if (child) return child
      }
    }
    return null
  }

  const menuItem = findMenuItem(menuItems, String(key))
  if (menuItem && menuItem.path) {
    router.push(menuItem.path)
  }
}



// 处理组织管理
const handleOrganizationManagement = () => {
  userMenuVisible.value = false
  router.push('/users')
}

// 处理个人信息
const handlePersonalInfo = () => {
  userMenuVisible.value = false
  router.push('/profile')
}

// 处理退出登录
const handleLogout = async () => {
  userMenuVisible.value = false
  try {
    await userStore.logout()
    message.success('退出登录成功')
    router.push('/login')
  } catch (error) {
    message.error('退出登录失败')
    console.error('退出登录失败:', error)
  }
}

// 监听路由变化
watch(route, updateSelectedKeys, { immediate: true })
</script>

<style scoped>
.side-menu-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100%;
}

.side-menu {
  flex: 1;
  border-right: none;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: 0;
  padding-bottom: 80px;
}

.side-menu::-webkit-scrollbar {
  width: 6px;
}

.side-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.side-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.side-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 菜单项基础样式 - 确保一级菜单完全对齐 */
:deep(.ant-menu-item) {
  margin: 4px 8px !important;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
  padding: 0 16px !important;
}

:deep(.ant-menu-submenu) {
  margin: 4px 8px !important;
  border-radius: 6px;
  padding: 0 !important;
}

:deep(.ant-menu-submenu-title) {
  height: 40px;
  line-height: 40px;
  border-radius: 6px;
  padding: 0 16px !important;
  margin: 0 !important;
}

/* 确保图标对齐 */
:deep(.ant-menu-item .anticon),
:deep(.ant-menu-submenu-title .anticon) {
  margin-right: 8px;
  font-size: 16px;
  vertical-align: middle;
}

/* 调整展开箭头位置，确保不影响内容对齐 */
:deep(.ant-menu-submenu-arrow) {
  right: 16px;
}

/* 子菜单项样式 - 增加合适的缩进 */
:deep(.ant-menu-submenu .ant-menu-item) {
  padding-left: 48px !important; /* 相对于一级菜单增加32px缩进 */
  margin: 2px 8px;
  height: 36px;
  line-height: 36px;
}

:deep(.ant-menu-item-selected) {
  background-color: #e6f7ff;
  color: #1890ff;
}

:deep(.ant-menu-item:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-menu-submenu-title:hover) {
  background-color: #f5f5f5;
}

/* 折叠状态下的样式 */
:deep(.ant-menu-inline-collapsed .ant-menu-item) {
  padding: 0 20px;
  text-align: center;
}

:deep(.ant-menu-inline-collapsed .ant-menu-submenu) {
  padding: 0;
}

/* 用户管理区域样式 */
.user-management {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-management.collapsed {
  flex-direction: column;
  gap: 8px;
  padding: 12px 8px;
}

.user-info-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
  cursor: pointer;
  flex: 1;
}

.user-info-item:hover {
  background-color: #f5f5f5;
}

.user-info-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
  cursor: pointer;
  width: 100%;
}

.user-info-collapsed:hover {
  background-color: #f5f5f5;
}

.collapse-trigger {
  font-size: 16px;
  color: #8c8c8c;
  flex-shrink: 0;
  margin-left: 8px;
}

.collapse-trigger.collapsed {
  margin-left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
}

.collapse-trigger:hover {
  color: #1890ff;
}

.user-avatar {
  background-color: #1890ff;
  flex-shrink: 0;
}

.user-details {
  margin-left: 12px;
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-title {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 用户菜单Popover样式 */
:deep(.user-menu-popover .ant-popover-inner) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.user-menu-content {
  width: 240px;
  padding: 16px;
}

.user-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.user-avatar-large {
  background-color: #1890ff;
  flex-shrink: 0;
}

.user-info-text {
  margin-left: 12px;
  flex: 1;
}

.user-name-large {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.user-enterprise {
  font-size: 13px;
  color: #8c8c8c;
}

.user-menu-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.menu-action-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #262626;
}

.menu-action-item:hover {
  background-color: #f5f5f5;
}

.menu-action-item.logout-item {
  color: #ff4d4f;
}

.menu-action-item.logout-item:hover {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.menu-icon {
  margin-right: 12px;
  font-size: 16px;
}
</style>
