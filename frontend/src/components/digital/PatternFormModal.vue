<!--
  花样创建/编辑表单模态框
-->

<template>
  <a-modal
    v-model:open="visible"
    :title="isEdit ? '编辑花样' : '新增花样'"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      @finish="handleFinish"
    >
      <a-row :gutter="16">
        <!-- 基本信息 -->
        <a-col :span="24">
          <h3 class="form-section-title">基本信息</h3>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="花样名称" name="name">
            <a-input
              v-model:value="formData.name"
              placeholder="请输入花样名称"
              :maxlength="100"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="花样编号" name="code">
            <a-input
              v-model:value="formData.code"
              placeholder="请输入花样编号"
              :maxlength="50"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="文件类型" name="fileType">
            <a-select
              v-model:value="formData.fileType"
              placeholder="请选择文件类型"
              allow-clear
            >
              <a-select-option
                v-for="type in fileTypeOptions"
                :key="type.value"
                :value="type.value"
              >
                {{ type.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="分组" name="groupId">
            <a-select
              v-model:value="formData.groupId"
              placeholder="请选择分组"
              allow-clear
            >
              <a-select-option
                v-for="group in groups"
                :key="group.id"
                :value="group.id"
              >
                {{ group.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 花样属性 -->
        <a-col :span="24">
          <h3 class="form-section-title">花样属性</h3>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="针数" name="stitch">
            <a-input-number
              v-model:value="formData.stitch"
              placeholder="针数"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="跳针次数" name="jumps">
            <a-input-number
              v-model:value="formData.jumps"
              placeholder="跳针次数"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="剪线次数" name="trim">
            <a-input-number
              v-model:value="formData.trim"
              placeholder="剪线次数"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="换色数量" name="colors">
            <a-input-number
              v-model:value="formData.colors"
              placeholder="换色数量"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="金片数量" name="goldPieceNum">
            <a-input-number
              v-model:value="formData.goldPieceNum"
              placeholder="金片数量"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>

        <!-- 尺寸信息 -->
        <a-col :span="24">
          <h3 class="form-section-title">尺寸信息</h3>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="宽度" name="width">
            <a-input-number
              v-model:value="formData.width"
              placeholder="宽度"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="高度" name="height">
            <a-input-number
              v-model:value="formData.height"
              placeholder="高度"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="X最小值" name="minX">
            <a-input-number
              v-model:value="formData.minX"
              placeholder="X最小值"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="X最大值" name="maxX">
            <a-input-number
              v-model:value="formData.maxX"
              placeholder="X最大值"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="Y最小值" name="minY">
            <a-input-number
              v-model:value="formData.minY"
              placeholder="Y最小值"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="Y最大值" name="maxY">
            <a-input-number
              v-model:value="formData.maxY"
              placeholder="Y最大值"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>

        <!-- 线长信息 -->
        <a-col :span="24">
          <h3 class="form-section-title">线长信息</h3>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="底线长度" name="baseLine">
            <a-input-number
              v-model:value="formData.baseLine"
              placeholder="底线长度"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="面线长度" name="surfaceLine">
            <a-input-number
              v-model:value="formData.surfaceLine"
              placeholder="面线长度"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>

        <!-- 其他信息 -->
        <a-col :span="24">
          <h3 class="form-section-title">其他信息</h3>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="创建人" name="createUser">
            <a-input
              v-model:value="formData.createUser"
              placeholder="请输入创建人"
              :maxlength="50"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="图片" name="image">
            <a-input
              v-model:value="formData.image"
              placeholder="请输入图片URL"
              :maxlength="255"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="formData.remark"
              placeholder="请输入备注"
              :rows="3"
              :maxlength="1000"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { patternApi } from '@/api/pattern'
import type { Pattern, CreatePatternRequest, UpdatePatternRequest, PatternFormFields } from '@/types/pattern'
import { PATTERN_FILE_TYPES } from '@/types/pattern'

// Props
interface Props {
  visible: boolean
  pattern?: Pattern | null
  mode: 'create' | 'edit'
  groups?: Array<{ id: number; name: string }> // 新增：从父组件传入分组数据
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  pattern: null,
  mode: 'create',
  groups: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const visible = ref(props.visible)
const groups = ref<Array<{ id: number; name: string }>>([])

// 计算属性
const isEdit = computed(() => props.mode === 'edit')
const fileTypeOptions = computed(() => PATTERN_FILE_TYPES)

// 表单数据
const formData = reactive<PatternFormFields>({
  name: '',
  code: '',
  remark: '',
  groupId: undefined,
  fileType: '',
  stitch: undefined,
  jumps: undefined,
  trim: undefined,
  colors: undefined,
  baseLine: undefined,
  surfaceLine: undefined,
  goldPieceNum: undefined,
  minX: undefined,
  maxX: undefined,
  minY: undefined,
  maxY: undefined,
  width: undefined,
  height: undefined,
  goldPieceLine: '',
  needle: '',
  mergeGoldPieceLine: '',
  image: '',
  createUser: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入花样名称', trigger: 'blur' },
    { max: 100, message: '花样名称不能超过100个字符', trigger: 'blur' }
  ],
  code: [
    { max: 50, message: '花样编号不能超过50个字符', trigger: 'blur' }
  ],
  remark: [
    { max: 1000, message: '备注不能超过1000个字符', trigger: 'blur' }
  ],
  createUser: [
    { max: 50, message: '创建人不能超过50个字符', trigger: 'blur' }
  ],
  image: [
    { max: 255, message: '图片URL不能超过255个字符', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
    if (props.pattern && isEdit.value) {
      loadPatternData()
    }
  }
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    code: '',
    remark: '',
    groupId: undefined,
    fileType: '',
    stitch: undefined,
    jumps: undefined,
    trim: undefined,
    colors: undefined,
    baseLine: undefined,
    surfaceLine: undefined,
    goldPieceNum: undefined,
    minX: undefined,
    maxX: undefined,
    minY: undefined,
    maxY: undefined,
    width: undefined,
    height: undefined,
    goldPieceLine: '',
    needle: '',
    mergeGoldPieceLine: '',
    image: '',
    createUser: ''
  })
  formRef.value?.clearValidate()
}

/**
 * 加载花样数据（编辑模式）
 */
const loadPatternData = () => {
  if (!props.pattern) return

  const pattern = props.pattern
  Object.assign(formData, {
    name: pattern.name,
    code: pattern.code || '',
    remark: pattern.remark || '',
    groupId: pattern.groupId,
    fileType: pattern.fileType || '',
    stitch: pattern.stitch,
    jumps: pattern.jumps,
    trim: pattern.trim,
    colors: pattern.colors,
    baseLine: pattern.baseLine,
    surfaceLine: pattern.surfaceLine,
    goldPieceNum: pattern.goldPieceNum,
    minX: pattern.minX,
    maxX: pattern.maxX,
    minY: pattern.minY,
    maxY: pattern.maxY,
    width: pattern.width,
    height: pattern.height,
    goldPieceLine: pattern.goldPieceLine || '',
    needle: pattern.needle || '',
    mergeGoldPieceLine: pattern.mergeGoldPieceLine || '',
    image: pattern.image || '',
    createUser: pattern.createUser || ''
  })
}

/**
 * 设置分组列表（仅使用传入的数据，不再发起API请求）
 */
const setGroups = () => {
  if (props.groups && props.groups.length > 0) {
    groups.value = props.groups
  } else {
    groups.value = []
  }
}

/**
 * 表单提交
 */
const handleSubmit = () => {
  formRef.value?.validate().then(() => {
    handleFinish()
  }).catch(() => {
    message.error('请检查表单输入')
  })
}

/**
 * 表单验证通过后的处理
 */
const handleFinish = async () => {
  try {
    loading.value = true

    if (isEdit.value && props.pattern) {
      // 编辑模式
      const updateData: UpdatePatternRequest = { ...formData }
      await patternApi.updatePattern(props.pattern.id, updateData)
      message.success('编辑花样成功')
    } else {
      // 创建模式
      const createData: CreatePatternRequest = { ...formData }
      await patternApi.createPattern(createData)
      message.success('创建花样成功')
    }

    visible.value = false
    emit('success')
  } catch (error) {
    console.error('保存花样失败:', error)
    message.error('保存失败')
  } finally {
    loading.value = false
  }
}

/**
 * 取消操作
 */
const handleCancel = () => {
  visible.value = false
}

// 监听props变化，同步分组数据
watch(() => props.groups, () => {
  setGroups()
}, { immediate: true })
</script>

<style scoped>
.form-section-title {
  margin: 24px 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.form-section-title:first-child {
  margin-top: 0;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-modal-body) {
  max-height: 600px;
  overflow-y: auto;
}
</style>
