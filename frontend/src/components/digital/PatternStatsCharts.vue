<!--
  花样统计图表组件
-->

<template>
  <div class="pattern-stats-charts">
    <a-row :gutter="16">
      <!-- 文件类型分布饼图 -->
      <a-col :span="12">
        <a-card title="文件类型分布" :bordered="false" class="chart-card">
          <div ref="fileTypeChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 分组分布柱状图 -->
      <a-col :span="12">
        <a-card title="分组分布" :bordered="false" class="chart-card">
          <div ref="groupChartRef" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 创建人分布 -->
      <a-col :span="24">
        <a-card title="创建人分布" :bordered="false" class="chart-card">
          <div ref="createUserChartRef" class="chart-container-wide"></div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { PatternStats } from '@/types/pattern'

// Props
interface Props {
  stats: PatternStats
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 图表引用
const fileTypeChartRef = ref<HTMLDivElement>()
const groupChartRef = ref<HTMLDivElement>()
const createUserChartRef = ref<HTMLDivElement>()

// 图表实例
let fileTypeChart: echarts.ECharts | null = null
let groupChart: echarts.ECharts | null = null
let createUserChart: echarts.ECharts | null = null

// 颜色配置
const colors = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
  '#fa8c16', '#13c2c2', '#eb2f96', '#a0d911', '#2f54eb'
]

/**
 * 初始化文件类型饼图
 */
const initFileTypeChart = () => {
  if (!fileTypeChartRef.value) return

  fileTypeChart = echarts.init(fileTypeChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '文件类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: []
      }
    ]
  }

  fileTypeChart.setOption(option)
}

/**
 * 初始化分组柱状图
 */
const initGroupChart = () => {
  if (!groupChartRef.value) return

  groupChart = echarts.init(groupChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '花样数量',
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        data: []
      }
    ]
  }

  groupChart.setOption(option)
}

/**
 * 初始化创建人横向柱状图
 */
const initCreateUserChart = () => {
  if (!createUserChartRef.value) return

  createUserChart = echarts.init(createUserChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '花样数量',
        type: 'bar',
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#ffecd2' },
            { offset: 1, color: '#fcb69f' }
          ])
        },
        data: []
      }
    ]
  }

  createUserChart.setOption(option)
}

/**
 * 更新图表数据
 */
const updateCharts = () => {
  if (!props.stats) return

  // 更新文件类型饼图
  if (fileTypeChart && props.stats.byFileType.length > 0) {
    const data = props.stats.byFileType.map((item, index) => ({
      name: item.fileType.toUpperCase(),
      value: item.count,
      itemStyle: {
        color: colors[index % colors.length]
      }
    }))

    fileTypeChart.setOption({
      series: [{ data }]
    })
  }

  // 更新分组柱状图
  if (groupChart && props.stats.byGroup.length > 0) {
    const xAxisData = props.stats.byGroup.map(item => item.groupName)
    const seriesData = props.stats.byGroup.map(item => item.count)

    groupChart.setOption({
      xAxis: { data: xAxisData },
      series: [{ data: seriesData }]
    })
  }

  // 更新创建人横向柱状图
  if (createUserChart && props.stats.byCreateUser.length > 0) {
    const yAxisData = props.stats.byCreateUser.map(item => item.createUser)
    const seriesData = props.stats.byCreateUser.map(item => item.count)

    createUserChart.setOption({
      yAxis: { data: yAxisData },
      series: [{ data: seriesData }]
    })
  }
}

/**
 * 调整图表大小
 */
const resizeCharts = () => {
  fileTypeChart?.resize()
  groupChart?.resize()
  createUserChart?.resize()
}

// 监听数据变化
watch(() => props.stats, () => {
  nextTick(() => {
    updateCharts()
  })
}, { deep: true })

// 监听窗口大小变化
window.addEventListener('resize', resizeCharts)

// 生命周期
onMounted(() => {
  nextTick(() => {
    initFileTypeChart()
    initGroupChart()
    initCreateUserChart()
    updateCharts()
  })
})
</script>

<style scoped>
.pattern-stats-charts {
  margin-top: 24px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.chart-card :deep(.ant-card-body) {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart-container-wide {
  width: 100%;
  height: 200px;
}
</style>
