<!--
  花样分组表单弹窗组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-25 15:35:00 +08:00; Reason: 创建花样分组表单组件，支持新增和编辑分组; Principle_Applied: 组件化设计、表单验证;}}
-->

<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    :width="500"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirm-loading="loading"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="分组名称" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入分组名称"
          :maxlength="50"
          show-count
        />
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="formData.status">
          <a-radio :value="TagStatus.ENABLED">正常</a-radio>
          <a-radio :value="TagStatus.DISABLED">禁用</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { tagApi } from '../../api/tag'
import type { Tag, CreateTagRequest, UpdateTagRequest } from '../../types/tag'
import { TagStatus, TagType } from '../../types/tag'

// Props
interface Props {
  visible: boolean
  group?: Tag | null
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  group: null,
  isEdit: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  success: []
  cancel: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  name: '',
  status: TagStatus.ENABLED
})

// 表单验证规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 1, max: 50, message: '分组名称长度为1-50个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 计算属性
const modalTitle = computed(() => {
  return props.isEdit ? '编辑分组' : '新增分组'
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    if (props.isEdit && props.group) {
      loadGroupData()
    }
  }
})

/**
 * 重置表单
 */
const resetForm = () => {
  formData.name = ''
  formData.status = TagStatus.ENABLED
  formRef.value?.clearValidate()
}

/**
 * 加载分组数据
 */
const loadGroupData = () => {
  if (props.group) {
    formData.name = props.group.name
    formData.status = props.group.status
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    if (props.isEdit && props.group) {
      // 编辑分组
      const updateData: UpdateTagRequest = {
        name: formData.name.trim(),
        status: formData.status
      }
      await tagApi.updateTag(props.group.id, updateData)
      message.success('分组更新成功')
    } else {
      // 新增分组
      const createData: CreateTagRequest = {
        name: formData.name.trim(),
        type: TagType.PATTERN_GROUP,
        status: formData.status
      }
      await tagApi.createTag(createData)
      message.success('分组创建成功')
    }

    emit('success')
    handleCancel()

  } catch (error) {
    console.error('提交失败:', error)
    if (error instanceof Error) {
      message.error(error.message || '操作失败，请稍后重试')
    } else {
      message.error('操作失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

/**
 * 取消操作
 */
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
