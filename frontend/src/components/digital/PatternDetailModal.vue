<!--
  花样详情查看模态框
-->

<template>
  <a-modal
    v-model:open="visible"
    title="花样详情"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div v-if="pattern" class="pattern-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="detail-item">
              <span class="label">花样名称:</span>
              <span class="value">{{ pattern.name }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <span class="label">花样编号:</span>
              <span class="value">{{ pattern.code || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <span class="label">文件类型:</span>
              <span class="value">
                <a-tag v-if="pattern.fileType" color="blue">
                  {{ pattern.fileType.toUpperCase() }}
                </a-tag>
                <span v-else>-</span>
              </span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <span class="label">分组:</span>
              <span class="value">
                <a-tag v-if="pattern.group" color="green">
                  {{ pattern.group.name }}
                </a-tag>
                <span v-else>未分组</span>
              </span>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 花样属性 -->
      <div class="detail-section">
        <h3 class="section-title">花样属性</h3>
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">针数:</span>
              <span class="value">{{ pattern.stitch?.toLocaleString() || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">跳针次数:</span>
              <span class="value">{{ pattern.jumps || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">剪线次数:</span>
              <span class="value">{{ pattern.trim || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">换色数量:</span>
              <span class="value">{{ pattern.colors || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">金片数量:</span>
              <span class="value">{{ pattern.goldPieceNum || '-' }}</span>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 尺寸信息 -->
      <div class="detail-section">
        <h3 class="section-title">尺寸信息</h3>
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">宽度:</span>
              <span class="value">{{ pattern.width || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">高度:</span>
              <span class="value">{{ pattern.height || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">尺寸:</span>
              <span class="value">
                {{ pattern.width && pattern.height ? `${pattern.width} × ${pattern.height}` : '-' }}
              </span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <span class="label">X范围:</span>
              <span class="value">
                {{ pattern.minX !== undefined && pattern.maxX !== undefined ? `${pattern.minX} ~ ${pattern.maxX}` : '-' }}
              </span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <span class="label">Y范围:</span>
              <span class="value">
                {{ pattern.minY !== undefined && pattern.maxY !== undefined ? `${pattern.minY} ~ ${pattern.maxY}` : '-' }}
              </span>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 线长信息 -->
      <div class="detail-section">
        <h3 class="section-title">线长信息</h3>
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="detail-item">
              <span class="label">底线长度:</span>
              <span class="value">{{ pattern.baseLine || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="detail-item">
              <span class="label">面线长度:</span>
              <span class="value">{{ pattern.surfaceLine || '-' }}</span>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 创建信息 -->
      <div class="detail-section">
        <h3 class="section-title">创建信息</h3>
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">创建人:</span>
              <span class="value">{{ pattern.createUser || '-' }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ formatDateTime(pattern.createdAt) }}</span>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="detail-item">
              <span class="label">更新时间:</span>
              <span class="value">{{ formatDateTime(pattern.updatedAt) }}</span>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 备注 -->
      <div v-if="pattern.remark" class="detail-section">
        <h3 class="section-title">备注</h3>
        <div class="remark-content">
          {{ pattern.remark }}
        </div>
      </div>

      <!-- 图片预览 -->
      <div v-if="pattern.image" class="detail-section">
        <h3 class="section-title">图片预览</h3>
        <div class="image-preview">
          <a-image
            :src="pattern.image"
            :alt="pattern.name"
            :preview="true"
            fallback="/images/pattern-placeholder.png"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">关闭</a-button>
        <a-button type="primary" @click="handleEdit">编辑</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { Pattern } from '@/types/pattern'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  visible: boolean
  pattern?: Pattern | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  pattern: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'edit': [pattern: Pattern]
}>()

// 响应式数据
const visible = ref(props.visible)

// 监听props变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleCancel = () => {
  visible.value = false
}

const handleEdit = () => {
  if (props.pattern) {
    emit('edit', props.pattern)
    visible.value = false
  }
}
</script>

<style scoped>
.pattern-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.label {
  min-width: 80px;
  color: #8c8c8c;
  font-size: 14px;
}

.value {
  color: #262626;
  font-size: 14px;
  font-weight: 500;
}

.remark-content {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  color: #262626;
  line-height: 1.6;
}

.image-preview {
  text-align: center;
}

.image-preview :deep(.ant-image) {
  max-width: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
