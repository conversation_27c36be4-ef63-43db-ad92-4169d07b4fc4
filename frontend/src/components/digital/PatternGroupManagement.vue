<!--
  花样分组管理组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-25 15:30:00 +08:00; Reason: 创建花样分组管理功能，参考设备分组管理实现; Principle_Applied: 组件化设计、功能复用;}}
-->

<template>
  <a-modal
    :visible="visible"
    title="花样分组管理"
    :width="900"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="pattern-group-management">
      <!-- 操作区域 -->
      <div class="action-bar">
        <div class="action-left">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索分组名称"
            style="width: 250px"
            @search="handleSearch"
            allow-clear
          />

          <a-select
            v-model:value="statusFilter"
            placeholder="选择状态"
            style="width: 120px; margin-left: 12px"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option :value="TagStatus.ENABLED">正常</a-select-option>
            <a-select-option :value="TagStatus.DISABLED">禁用</a-select-option>
          </a-select>
        </div>

        <div class="action-right">
          <a-space>
            <a-button @click="handleRefresh" :loading="loading">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>

            <a-button
              type="primary"
              @click="showCreateModal"
            >
              <template #icon><PlusOutlined /></template>
              新增分组
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 分组列表 -->
      <div class="content-area">
        <a-table
          :columns="columns"
          :data-source="groupList"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          @change="handleTableChange"
          size="small"
        >
          <!-- 状态列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>

            <!-- 创建时间列 -->
            <template v-else-if="column.key === 'createdAt'">
              {{ formatDateTime(record.createdAt) }}
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
              <a-space size="small">
                <a-button type="link" size="small" @click="showEditModal(record)">
                  <EditOutlined />
                  编辑
                </a-button>

                <a-button
                  type="link"
                  size="small"
                  :style="{ color: record.status === 1 ? '#ff4d4f' : '#52c41a' }"
                  @click="handleToggleStatus(record)"
                >
                  <CheckOutlined v-if="record.status === TagStatus.DISABLED" />
                  <StopOutlined v-else />
                  {{ record.status === 1 ? '禁用' : '启用' }}
                </a-button>

                <a-button
                  type="link"
                  size="small"
                  danger
                  @click="showDeleteConfirm(record)"
                >
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 分组表单弹窗 -->
    <PatternGroupFormModal
      v-model:visible="formModalVisible"
      :group="selectedGroup"
      :is-edit="isEdit"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  StopOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import { tagApi } from '../../api/tag'
import type { Tag, TagListQuery } from '../../types/tag'
import { TagStatus, TagType } from '../../types/tag'
// import PatternGroupFormModal from './PatternGroupFormModal.vue'
import dayjs from 'dayjs'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  cancel: []
  success: []
}>()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref<TagStatus | undefined>(undefined)

// 分组列表数据
const groupList = ref<Tag[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: false,
  showQuickJumper: false,
  showTotal: (total: number) => `共 ${total} 条`
})

// 表单弹窗
const formModalVisible = ref(false)
const selectedGroup = ref<Tag | null>(null)
const isEdit = ref(false)

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '分组名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center'
  }
]

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchGroupList()
  }
})

/**
 * 获取分组列表
 */
const fetchGroupList = async () => {
  try {
    loading.value = true

    const params: TagListQuery = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      type: TagType.PATTERN_GROUP,
      search: searchKeyword.value || undefined,
      status: statusFilter.value
    }

    const response = await tagApi.getTagList(params)
    groupList.value = response.tags
    pagination.total = response.total

  } catch (error) {
    console.error('获取分组列表失败:', error)
    message.error('获取分组列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  pagination.current = 1
  fetchGroupList()
}

/**
 * 刷新列表
 */
const handleRefresh = () => {
  fetchGroupList()
}

/**
 * 表格变化处理
 */
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchGroupList()
}

/**
 * 显示新增弹窗
 */
const showCreateModal = () => {
  selectedGroup.value = null
  isEdit.value = false
  formModalVisible.value = true
}

/**
 * 显示编辑弹窗
 */
const showEditModal = (group: Tag) => {
  selectedGroup.value = group
  isEdit.value = true
  formModalVisible.value = true
}

/**
 * 切换状态
 */
const handleToggleStatus = async (group: Tag) => {
  try {
    const newStatus = group.status === TagStatus.ENABLED ? TagStatus.DISABLED : TagStatus.ENABLED
    if (process.env.NODE_ENV === 'development') {
      console.log(`正在${newStatus === TagStatus.ENABLED ? '启用' : '禁用'}分组: ${group.name} (ID: ${group.id})`)
    }

    await tagApi.updateTagStatus(group.id, newStatus)
    message.success('状态更新成功')

    if (process.env.NODE_ENV === 'development') {
      console.log('分组状态更新成功，刷新列表并通知父组件')
    }
    fetchGroupList()
    // 通知父组件刷新搜索选项
    emit('success')
  } catch (error) {
    console.error('状态更新失败:', error)
    message.error('状态更新失败')
  }
}

/**
 * 显示删除确认
 */
const showDeleteConfirm = (group: Tag) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分组"${group.name}"吗？`,
    onOk: () => handleDelete(group.id)
  })
}

/**
 * 删除分组
 */
const handleDelete = async (id: number) => {
  try {
    await tagApi.deleteTag(id)
    message.success('删除成功')
    fetchGroupList()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

/**
 * 表单提交成功
 */
const handleFormSuccess = () => {
  formModalVisible.value = false
  fetchGroupList()
  emit('success')
}

/**
 * 表单取消
 */
const handleFormCancel = () => {
  formModalVisible.value = false
}

/**
 * 关闭弹窗
 */
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status: TagStatus) => {
  return status === TagStatus.ENABLED ? 'green' : 'red'
}

/**
 * 获取状态文本
 */
const getStatusText = (status: TagStatus) => {
  return status === TagStatus.ENABLED ? '正常' : '禁用'
}

/**
 * 格式化时间
 */
const formatDateTime = (date: string | Date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}
</script>

<style scoped>
.pattern-group-management {
  padding: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.action-left {
  display: flex;
  align-items: center;
}

.action-right {
  flex-shrink: 0;
}

.content-area {
  margin-top: 16px;
}
</style>
