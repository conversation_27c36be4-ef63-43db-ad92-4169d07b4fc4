<!--
  标签管理弹框组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-23 17:30:00 +08:00; Reason: 设备管理页面集成标签管理功能, 创建弹框组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    :width="900"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="tag-management-modal">
      <!-- 操作区域 -->
      <div class="action-bar">
        <div class="action-left">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索标签名称"
            style="width: 250px"
            @search="handleSearch"
            allow-clear
          />

          <a-select
            v-model:value="statusFilter"
            placeholder="选择状态"
            style="width: 120px; margin-left: 12px"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option :value="TagStatus.ENABLED">正常</a-select-option>
            <a-select-option :value="TagStatus.DISABLED">禁用</a-select-option>
          </a-select>
        </div>

        <div class="action-right">
          <a-space>
            <a-button @click="handleRefresh" :loading="loading">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>

            <a-button
              type="primary"
              @click="showCreateModal"
            >
              <template #icon><PlusOutlined /></template>
              新增{{ typeLabel }}
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 统一使用表格展示 -->
        <div class="table-container">
          <a-table
            :columns="getTableColumns()"
            :data-source="getTableData()"
            :loading="loading"
            :pagination="supportHierarchy ? false : pagination"
            :expanded-row-keys="expandedRowKeys"
            :expandable="supportHierarchy ? getExpandableConfig() : undefined"
            row-key="id"
            @change="handleTableChange"
            @expand="handleRowExpand"
            size="small"
          >
            <!-- 名称列（支持层级缩进） -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="name-cell" :style="{ paddingLeft: supportHierarchy ? `${(record.level - 1) * 24}px` : '0' }">
                  <span v-if="supportHierarchy && record.level > 1" class="level-indicator">
                    {{ '└─ ' }}
                  </span>
                  <span class="name-text">{{ record.name }}</span>
                  <!-- 只有支持层级的类型才显示级别标签 -->
                  <a-tag
                    v-if="supportHierarchy"
                    :color="getLevelColor(record.level)"
                    size="small"
                    class="level-tag"
                  >
                    L{{ record.level }}
                  </a-tag>
                </div>
              </template>

              <!-- 状态列 -->
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>

              <!-- 创建时间列 -->
              <template v-else-if="column.key === 'createdAt'">
                {{ formatDateTime(record.createdAt) }}
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.key === 'actions'">
                <a-space size="small">
                  <a-button
                    v-if="supportHierarchy"
                    type="link"
                    size="small"
                    @click="showCreateChildModal(record.id)"
                    title="添加子级"
                  >
                    <PlusOutlined />
                    添加
                  </a-button>

                  <a-button type="link" size="small" @click="showEditModal(record.id)">
                    <EditOutlined />
                    编辑
                  </a-button>

                  <a-button
                    type="link"
                    size="small"
                    :style="{ color: record.status === 1 ? '#ff4d4f' : '#52c41a' }"
                    @click="handleToggleStatus(record)"
                  >
                    <CheckOutlined v-if="record.status === TagStatus.DISABLED" />
                    <StopOutlined v-else />
                    {{ record.status === 1 ? '禁用' : '启用' }}
                  </a-button>

                  <a-button
                    type="link"
                    size="small"
                    danger
                    @click="showDeleteConfirm(record.id)"
                  >
                    <DeleteOutlined />
                    删除
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <!-- 标签表单弹窗 -->
    <TagFormModal
      v-model:visible="formModalVisible"
      :tag="selectedTag"
      :is-edit="isEdit"
      :tag-type="tagType"
      :parent-options="parentOptions"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  StopOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import { tagApi, tagUtils } from '../../api/tag'
import type {
  Tag,
  TagListQuery,
  TagTreeNode,
  TagType
} from '../../types/tag'
import { TagStatus } from '../../types/tag'
import { defineAsyncComponent } from 'vue'

const TagFormModal = defineAsyncComponent(() => import('./TagFormModal.vue'))
import dayjs from 'dayjs'

// Props
interface Props {
  visible: boolean
  tagType: TagType
  title?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  cancel: []
  success: []
}>()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref<TagStatus | undefined>(undefined)

// 标签列表数据
const tagList = ref<Tag[]>([])
const treeData = ref<TagTreeNode[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: false,
  showQuickJumper: false,
  showTotal: (total: number) => `共 ${total} 条`
})

// 表格展开状态
const expandedRowKeys = ref<number[]>([])
const selectedKeys = ref<number[]>([])

// 表单弹窗
const formModalVisible = ref(false)
const selectedTag = ref<Tag | null>(null)
const isEdit = ref(false)

// 计算属性
const modalTitle = computed(() => {
  return props.title || `${typeLabel.value}管理`
})

const typeLabel = computed(() => {
  return tagUtils.getTagTypeName(props.tagType)
})

const supportHierarchy = computed(() => {
  return tagUtils.supportHierarchy(props.tagType)
})

const parentOptions = computed(() => {
  if (!supportHierarchy.value) {
    return []
  }
  
  return treeData.value.map(tag => ({
    label: tag.name,
    value: tag.id,
    children: buildParentOptions(tag.children || [])
  }))
})

/**
 * 获取表格列配置
 */
const getTableColumns = () => {
  const baseColumns: any[] = [
    {
      title: '名称',
      key: 'name',
      width: 250
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      align: 'center'
    },
    {
      title: '创建时间',
      key: 'createdAt',
      width: 160
    },
    {
      title: '操作',
      key: 'actions',
      width: supportHierarchy.value ? 160 : 120,
      align: 'center'
    }
  ]

  return baseColumns
}

/**
 * 获取表格数据
 */
const getTableData = () => {
  if (supportHierarchy.value) {
    // 车间产线：只显示顶级数据，子级通过展开显示
    return getTopLevelData(treeData.value)
  } else {
    // 其他类型：直接返回列表数据
    return tagList.value
  }
}

/**
 * 获取顶级数据（只显示第一层）
 */
const getTopLevelData = (nodes: TagTreeNode[]): Tag[] => {
  return nodes.map(node => ({
    ...node,
    parentName: undefined, // 顶级没有父级
    hasChildren: !!(node.children && node.children.length > 0)
  } as Tag))
}

/**
 * 将树形数据扁平化为表格数据
 */
const flattenTreeData = (nodes: TagTreeNode[], parentNode?: TagTreeNode): Tag[] => {
  const result: Tag[] = []

  const traverse = (nodeList: TagTreeNode[], parent?: TagTreeNode) => {
    for (const node of nodeList) {
      // 添加父级信息
      const flatNode = {
        ...node,
        parentName: parent ? parent.name : undefined,
        pid: parent ? parent.id : undefined
      } as Tag

      result.push(flatNode)

      if (node.children && node.children.length > 0) {
        traverse(node.children, node)
      }
    }
  }

  traverse(nodes, parentNode)
  return result
}

/**
 * 获取层级颜色
 */
const getLevelColor = (level: number): string => {
  const colors = ['blue', 'green', 'orange', 'purple', 'red']
  return colors[(level - 1) % colors.length]
}

/**
 * 获取展开配置
 */
const getExpandableConfig = () => {
  return {
    indentSize: 24,
    expandedRowRender: (record: Tag) => {
      // 返回子级数据的简单列表
      const node = findNodeInTree(treeData.value, record.id)
      if (node && node.children && node.children.length > 0) {
        return h('div', { class: 'children-container' },
          node.children.map(child =>
            h('div', {
              key: child.id,
              class: 'child-row',
              style: {
                padding: '8px 16px',
                borderBottom: '1px solid #f0f0f0',
                display: 'flex',
                alignItems: 'center',
                gap: '12px'
              }
            }, [
              h('span', { style: { flex: 1, fontWeight: '500' } }, child.name),
              h('span', {
                style: {
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '10px',
                  backgroundColor: getLevelColor(child.level) === 'blue' ? '#e6f7ff' : '#f6ffed',
                  color: getLevelColor(child.level) === 'blue' ? '#1890ff' : '#52c41a',
                  border: `1px solid ${getLevelColor(child.level) === 'blue' ? '#91d5ff' : '#b7eb8f'}`
                }
              }, `L${child.level}`),
              h('span', {
                style: {
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '10px',
                  backgroundColor: child.status === 1 ? '#f6ffed' : '#fff2e8',
                  color: child.status === 1 ? '#52c41a' : '#fa8c16',
                  border: `1px solid ${child.status === 1 ? '#b7eb8f' : '#ffd591'}`
                }
              }, getStatusText(child.status))
            ])
          )
        )
      }
      return null
    },
    rowExpandable: (record: Tag) => {
      // 检查是否有子级
      const node = findNodeInTree(treeData.value, record.id)
      return !!(node && node.children && node.children.length > 0)
    }
  }
}

/**
 * 在树形数据中查找节点
 */
const findNodeInTree = (nodes: TagTreeNode[], id: number): TagTreeNode | null => {
  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeInTree(node.children, id)
      if (found) return found
    }
  }
  return null
}

// 渲染子级表格方法已整合到 getExpandableConfig 中

// 方法
const buildParentOptions = (children: TagTreeNode[]): any[] => {
  return children.map(child => ({
    label: child.name,
    value: child.id,
    children: buildParentOptions(child.children || [])
  }))
}

const getStatusText = (status: TagStatus): string => {
  return tagUtils.getTagStatusName(status)
}

const getStatusColor = (status: TagStatus): string => {
  return tagUtils.getTagStatusColor(status)
}

const formatDateTime = (dateTime: string): string => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取标签数据
 */
const fetchTagData = async () => {
  try {
    loading.value = true

    if (supportHierarchy.value) {
      // 获取树形数据，直接在后端进行筛选
      const queryParams: { status?: TagStatus; search?: string } = {}
      if (statusFilter.value !== undefined) {
        queryParams.status = statusFilter.value
      }
      if (searchKeyword.value) {
        queryParams.search = searchKeyword.value
      }

      treeData.value = await tagApi.getTagTree(props.tagType, queryParams)

      // 搜索时自动展开所有匹配的节点
      if (searchKeyword.value) {
        expandedRowKeys.value = getExpandedKeysForSearch(treeData.value)
      } else {
        // 清空搜索时恢复默认展开状态
        expandedRowKeys.value = []
      }
    } else {
      // 获取列表数据
      const params: TagListQuery = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        type: props.tagType,
        search: searchKeyword.value || undefined,
        status: statusFilter.value
      }

      const response = await tagApi.getTagList(params)
      tagList.value = response.tags
      pagination.total = response.total
    }
  } catch (error) {
    console.error('获取标签数据失败:', error)
    message.error('获取标签数据失败')
  } finally {
    loading.value = false
  }
}

/* 前端过滤函数已移除，现在使用后端查询 */

/**
 * 获取搜索时需要展开的节点键
 */
const getExpandedKeysForSearch = (nodes: TagTreeNode[]): number[] => {
  const keys: number[] = []

  const traverse = (nodeList: TagTreeNode[]) => {
    for (const node of nodeList) {
      if (node.children && node.children.length > 0) {
        keys.push(node.id)
        traverse(node.children)
      }
    }
  }

  traverse(nodes)
  return keys
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.current = 1
  fetchTagData()
}

/**
 * 处理刷新
 */
const handleRefresh = () => {
  searchKeyword.value = ''
  statusFilter.value = undefined
  pagination.current = 1
  fetchTagData()
}

/**
 * 处理表格变化
 */
const handleTableChange: TableProps['onChange'] = (pag) => {
  if (pag) {
    pagination.current = pag.current || 1
    pagination.pageSize = pag.pageSize || 10
  }
  fetchTagData()
}

/**
 * 处理行展开
 */
const handleRowExpand = (expanded: boolean, record: Tag) => {
  if (expanded) {
    // 展开：添加到展开列表
    if (!expandedRowKeys.value.includes(record.id)) {
      expandedRowKeys.value.push(record.id)
    }
  } else {
    // 折叠：从展开列表移除
    const index = expandedRowKeys.value.indexOf(record.id)
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1)
    }
  }
}

/**
 * 显示创建弹窗
 */
const showCreateModal = () => {
  selectedTag.value = null
  isEdit.value = false
  formModalVisible.value = true
}

/**
 * 显示创建子级弹窗
 */
const showCreateChildModal = (parentId: number) => {
  selectedTag.value = { pid: parentId } as Tag
  isEdit.value = false
  formModalVisible.value = true
}

/**
 * 显示编辑弹窗
 */
const showEditModal = async (id: number) => {
  try {
    selectedTag.value = await tagApi.getTagById(id)
    isEdit.value = true
    formModalVisible.value = true
  } catch (error) {
    console.error('获取标签详情失败:', error)
    message.error('获取标签详情失败')
  }
}

/**
 * 显示删除确认
 */
const showDeleteConfirm = async (id: number) => {
  try {
    // 检查是否有子级
    const hasChildren = await checkHasChildren(id)

    if (hasChildren) {
      Modal.confirm({
        title: '确认删除',
        content: '该产线下还有子级产线，删除后子级产线也将被删除。确定要继续吗？',
        okText: '确定删除',
        cancelText: '取消',
        okType: 'danger',
        onOk: () => handleDelete(id)
      })
    } else {
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除这个产线吗？此操作不可恢复。',
        okText: '确定',
        cancelText: '取消',
        okType: 'danger',
        onOk: () => handleDelete(id)
      })
    }
  } catch (error) {
    console.error('检查子级失败:', error)
    // 如果检查失败，使用普通删除确认
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个产线吗？此操作不可恢复。',
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => handleDelete(id)
    })
  }
}

/**
 * 检查是否有子级
 */
const checkHasChildren = async (id: number): Promise<boolean> => {
  if (!supportHierarchy.value) return false

  // 在树形数据中查找是否有子级
  const findNode = (nodes: TagTreeNode[], targetId: number): TagTreeNode | null => {
    for (const node of nodes) {
      if (node.id === targetId) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findNode(node.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  const node = findNode(treeData.value, id)
  return node ? !!(node.children && node.children.length > 0) : false
}

/**
 * 处理删除
 */
const handleDelete = async (id: number) => {
  try {
    await tagApi.deleteTag(id)
    message.success('删除标签成功')
    fetchTagData()
    // 通知父组件刷新搜索选项
    console.log('🏷️ TagManagementModal: 删除标签成功，触发success事件')
    emit('success')
  } catch (error) {
    console.error('删除标签失败:', error)
    message.error('删除标签失败，请稍后重试')
  }
}

/**
 * 处理状态切换
 */
const handleToggleStatus = async (tag: Tag) => {
  const newStatus = tag.status === TagStatus.ENABLED ? TagStatus.DISABLED : TagStatus.ENABLED
  const actionText = newStatus === TagStatus.DISABLED ? '禁用' : '启用'

  // 检查是否有子级
  const hasChildren = supportHierarchy.value ? await checkHasChildren(tag.id) : false

  let confirmContent = `确定要${actionText}这个${supportHierarchy.value ? '产线' : '标签'}吗？`
  if (hasChildren) {
    if (newStatus === TagStatus.DISABLED) {
      confirmContent = `确定要${actionText}这个产线吗？禁用后，其所有子级产线也将被禁用。`
    } else if (newStatus === TagStatus.ENABLED) {
      confirmContent = `确定要${actionText}这个产线吗？启用后，其所有子级产线也将被启用。`
    }
  }

  Modal.confirm({
    title: `确认${actionText}`,
    content: confirmContent,
    okText: '确定',
    cancelText: '取消',
    okType: newStatus === TagStatus.DISABLED ? 'danger' : 'primary',
    onOk: async () => {
      try {
        await tagApi.updateTagStatus(tag.id, newStatus)
        message.success(`${actionText}成功`)
        fetchTagData()
        // 通知父组件刷新搜索选项
        console.log(`🏷️ TagManagementModal: ${actionText}成功，触发success事件`)
        emit('success')
      } catch (error) {
        console.error(`${actionText}失败:`, error)
        message.error(`${actionText}失败，请稍后重试`)
      }
    }
  })
}

/**
 * 处理表单成功
 */
const handleFormSuccess = () => {
  formModalVisible.value = false
  fetchTagData()
  // 通知父组件刷新搜索选项
  console.log('🏷️ TagManagementModal: 表单操作成功，触发success事件')
  emit('success')
}

/**
 * 处理表单取消
 */
const handleFormCancel = () => {
  formModalVisible.value = false
  selectedTag.value = null
}

/**
 * 处理弹窗取消
 */
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 监听props变化
watch(() => props.visible, (visible) => {
  if (visible) {
    fetchTagData()
  }
})

watch(() => props.tagType, () => {
  if (props.visible) {
    fetchTagData()
  }
})
</script>

<style scoped>
.tag-management-modal {
  padding: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.action-left {
  display: flex;
  align-items: center;
}

.action-right {
  display: flex;
  align-items: center;
}

.content-area {
  min-height: 400px;
  max-height: 500px;
  overflow-y: auto;
}

.tree-container {
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  padding: 16px;
}

.tree-node-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-name {
  flex: 1;
  font-weight: 500;
}

.node-status {
  margin: 0;
}

.node-level {
  font-size: 12px;
  color: #8c8c8c;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node-title:hover .node-actions {
  opacity: 1;
}

.table-container {
  background: white;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

/* 名称列样式 */
.name-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.level-indicator {
  color: #bfbfbf;
  font-family: monospace;
  font-size: 12px;
  white-space: nowrap;
}

.name-text {
  font-weight: 500;
  flex: 1;
}

.level-tag {
  margin: 0;
  font-size: 10px;
  line-height: 16px;
  padding: 0 4px;
}

/* 父级相关样式已移除 */

/* 表格行样式优化 */
:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
}

/* 层级行的背景色区分 */
:deep(.ant-table-tbody > tr:nth-child(even)) {
  background-color: #fafafa;
}

/* 展开行样式 */
.children-container {
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 8px 0;
}

.child-row {
  transition: background-color 0.2s;
}

.child-row:hover {
  background-color: #e6f7ff;
}

.child-row:last-child {
  border-bottom: none !important;
}

/* 展开按钮样式优化 */
:deep(.ant-table-row-expand-icon) {
  color: #1890ff;
}

:deep(.ant-table-row-expand-icon:hover) {
  color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .action-left,
  .action-right {
    justify-content: center;
  }
}
</style>
