<!--
  标签表单弹窗组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 标签管理模块开发, 创建标签表单弹窗组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    :width="600"
    :confirm-loading="submitting"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="handleFinish"
    >
      <!-- 标签名称 -->
      <a-form-item label="标签名称" name="name">
        <a-input
          v-model:value="formData.name"
          placeholder="请输入标签名称"
          :maxlength="100"
          show-count
        />
      </a-form-item>

      <!-- 父级标签（仅车间产线支持） -->
      <a-form-item 
        v-if="supportHierarchy" 
        label="父级标签" 
        name="pid"
      >
        <a-tree-select
          v-model:value="formData.pid"
          :tree-data="parentOptions"
          placeholder="请选择父级标签（可选）"
          allow-clear
          tree-default-expand-all
          :field-names="{ label: 'label', value: 'value', children: 'children' }"
        />
      </a-form-item>

      <!-- 状态 -->
      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="formData.status">
          <a-radio :value="TagStatus.ENABLED">正常</a-radio>
          <a-radio :value="TagStatus.DISABLED">禁用</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 类型信息（只读显示） -->
      <a-form-item label="标签类型">
        <a-input 
          :value="tagTypeName" 
          readonly 
          disabled
        />
      </a-form-item>

      <!-- 层级信息（编辑时显示） -->
      <a-form-item v-if="isEdit && tag" label="层级信息">
        <a-descriptions size="small" :column="2">
          <a-descriptions-item label="当前层级">
            第 {{ tag.level }} 级
          </a-descriptions-item>
          <a-descriptions-item label="路径">
            {{ tag.path }}
          </a-descriptions-item>
        </a-descriptions>
      </a-form-item>
    </a-form>

    <!-- 提示信息 -->
    <div class="form-tips">
      <a-alert
        v-if="supportHierarchy"
        message="车间产线支持多级层级结构，可以创建子级标签"
        type="info"
        show-icon
        :closable="false"
      />
      <a-alert
        v-else
        message="该类型标签为同级结构，不支持层级关系"
        type="info"
        show-icon
        :closable="false"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { tagApi, tagUtils } from '../../api/tag'
import type {
  Tag,
  CreateTagRequest,
  UpdateTagRequest,
  TagType
} from '../../types/tag'
import { TagStatus } from '../../types/tag'

// 定义组件名称
defineOptions({
  name: 'TagFormModal'
})

// Props
interface Props {
  visible: boolean
  tag?: Tag | null
  isEdit: boolean
  tagType: TagType
  parentOptions: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  success: []
  cancel: []
  'update:visible': [visible: boolean]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive<CreateTagRequest & { pid?: number }>({
  name: '',
  type: props.tagType,
  pid: undefined,
  status: TagStatus.ENABLED
})

// 计算属性
const modalTitle = computed(() => {
  return props.isEdit ? '编辑标签' : '新增标签'
})

const tagTypeName = computed(() => {
  return tagUtils.getTagTypeName(props.tagType)
})

const supportHierarchy = computed(() => {
  return tagUtils.supportHierarchy(props.tagType)
})

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { max: 100, message: '标签名称长度不能超过100个字符', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        const validation = tagUtils.validateTagName(value)
        if (!validation.valid) {
          return Promise.reject(validation.message)
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 方法
const initFormData = () => {
  if (props.isEdit && props.tag) {
    // 编辑模式，填充现有数据
    Object.assign(formData, {
      name: props.tag.name,
      type: props.tag.type,
      pid: props.tag.pid,
      status: props.tag.status
    })
  } else {
    // 新增模式，重置表单
    Object.assign(formData, {
      name: '',
      type: props.tagType,
      pid: props.tag?.pid || undefined, // 支持预设父级ID
      status: TagStatus.ENABLED
    })
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    await handleFinish()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleFinish = async () => {
  try {
    submitting.value = true

    if (props.isEdit && props.tag) {
      // 编辑标签
      const updateData: UpdateTagRequest = {
        name: formData.name,
        status: formData.status
      }
      
      // 只有车间产线支持修改父级
      if (supportHierarchy.value) {
        updateData.pid = formData.pid
      }
      
      await tagApi.updateTag(props.tag.id, updateData)
      message.success('更新标签成功')
    } else {
      // 创建标签
      const createData: CreateTagRequest = {
        name: formData.name,
        type: formData.type,
        status: formData.status
      }
      
      // 只有车间产线支持设置父级
      if (supportHierarchy.value && formData.pid) {
        createData.pid = formData.pid
      }
      
      await tagApi.createTag(createData)
      message.success('创建标签成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
    if (error instanceof Error) {
      message.error(error.message || '操作失败，请稍后重试')
    } else {
      message.error('操作失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 监听props变化
watch(() => [props.visible, props.tag, props.isEdit], () => {
  if (props.visible) {
    initFormData()
  }
}, { immediate: true })

watch(() => props.tagType, (newType) => {
  formData.type = newType
})

// 生命周期
onMounted(() => {
  initFormData()
})
</script>

<style scoped>
.form-tips {
  margin-top: 16px;
}

.form-tips .ant-alert {
  margin-bottom: 0;
}

/* 表单项间距调整 */
:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}

/* 描述列表样式 */
:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #666;
}

:deep(.ant-descriptions-item-content) {
  color: #333;
}

/* 树选择器样式 */
:deep(.ant-tree-select) {
  width: 100%;
}

/* 单选按钮组样式 */
:deep(.ant-radio-group) {
  display: flex;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.ant-modal) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  :deep(.ant-descriptions) {
    font-size: 12px;
  }
}
</style>
