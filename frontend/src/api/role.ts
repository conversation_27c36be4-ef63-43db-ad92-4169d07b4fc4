/**
 * 角色管理API
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 11:37:00 +08:00; Reason: 创建角色API用于用户管理中的角色分配; Principle_Applied: API模块化;}}
 */

import { http } from './request'

export interface Role {
  id: number
  name: string
  code: string
  description: string
  isSystem: boolean
  enterpriseId: number | null
  permissions?: Permission[]
}

export interface Permission {
  id: number
  name: string
  code: string
  module: string
}

export interface RoleListParams {
  enterpriseId?: number
}

export interface CreateRoleData {
  name: string
  code: string
  description?: string
  permissionIds?: number[]
}

export interface UpdateRoleData {
  name?: string
  description?: string
  permissionIds?: number[]
}

export interface UpdateRolePermissionsData {
  permissionIds: number[]
}

/**
 * 角色管理API
 */
export const roleApi = {
  /**
   * 获取角色列表
   */
  getRoleList: (params?: RoleListParams): Promise<Role[]> => {
    return http.get('/roles', { params })
  },

  /**
   * 获取角色详情
   */
  getRoleById: (id: number): Promise<Role> => {
    return http.get(`/roles/${id}`)
  },

  /**
   * 创建角色
   */
  createRole: (data: CreateRoleData): Promise<Role> => {
    return http.post('/roles', data)
  },

  /**
   * 更新角色
   */
  updateRole: (id: number, data: UpdateRoleData): Promise<Role> => {
    return http.put(`/roles/${id}`, data)
  },

  /**
   * 删除角色
   */
  deleteRole: (id: number): Promise<void> => {
    return http.delete(`/roles/${id}`)
  },

  /**
   * 更新角色权限
   */
  updateRolePermissions: (id: number, data: UpdateRolePermissionsData): Promise<Role> => {
    return http.put(`/roles/${id}/permissions`, data)
  },

  /**
   * 获取权限列表
   */
  getPermissionList: (): Promise<Permission[]> => {
    return http.get('/roles/permissions/list')
  },
}
