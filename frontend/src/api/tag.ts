/**
 * 标签管理API
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 标签管理模块开发, 创建标签API接口; Principle_Applied: API模块化;}}
 */

import { http } from './request';
import type {
  Tag,
  TagListQuery,
  TagListResponse,
  CreateTagRequest,
  UpdateTagRequest,
  TagTreeNode,
  TagTypeOption
} from '../types/tag';
import { TagType, TagStatus } from '../types/tag';

/**
 * 标签管理API
 */
export const tagApi = {
  /**
   * 获取标签列表
   */
  getTagList: (params: TagListQuery): Promise<TagListResponse> => {
    return http.get('/tags', { params });
  },

  /**
   * 获取标签树形结构
   */
  getTagTree: (type: TagType, params?: { status?: TagStatus; search?: string }): Promise<TagTreeNode[]> => {
    const queryParams = new URLSearchParams();
    if (params?.status !== undefined) {
      queryParams.append('status', params.status.toString());
    }
    if (params?.search) {
      queryParams.append('search', params.search);
    }

    const queryString = queryParams.toString();
    const url = `/tags/tree/${type}${queryString ? `?${queryString}` : ''}`;

    return http.get(url);
  },

  /**
   * 获取标签详情
   */
  getTagById: (id: number): Promise<Tag> => {
    return http.get(`/tags/${id}`);
  },

  /**
   * 创建标签
   */
  createTag: (data: CreateTagRequest): Promise<Tag> => {
    return http.post('/tags', data);
  },

  /**
   * 更新标签
   */
  updateTag: (id: number, data: UpdateTagRequest): Promise<Tag> => {
    return http.put(`/tags/${id}`, data);
  },

  /**
   * 删除标签
   */
  deleteTag: (id: number): Promise<void> => {
    return http.delete(`/tags/${id}`);
  },

  /**
   * 更新标签状态
   */
  updateTagStatus: (id: number, status: TagStatus): Promise<Tag> => {
    return http.put(`/tags/${id}/status`, { status });
  },

  /**
   * 获取标签类型列表
   */
  getTagTypes: (): Promise<TagTypeOption[]> => {
    return http.get('/tags/types');
  }
};

/**
 * 标签工具类
 */
export const tagUtils = {
  /**
   * 获取标签类型名称
   */
  getTagTypeName: (type: TagType): string => {
    const typeNames = {
      [TagType.WORKSHOP_LINE]: '车间产线',
      [TagType.MACHINE_GROUP]: '机器分组',
      [TagType.PATTERN_GROUP]: '花样分组',
      [TagType.PATTERN_UNIT]: '花样计量单位'
    };
    return typeNames[type] || '未知类型';
  },

  /**
   * 获取标签状态名称
   */
  getTagStatusName: (status: TagStatus): string => {
    return status === TagStatus.ENABLED ? '正常' : '禁用';
  },

  /**
   * 获取标签状态颜色
   */
  getTagStatusColor: (status: TagStatus): string => {
    return status === TagStatus.ENABLED ? 'success' : 'default';
  },

  /**
   * 检查标签类型是否支持层级
   */
  supportHierarchy: (type: TagType): boolean => {
    return type === TagType.WORKSHOP_LINE;
  },

  /**
   * 构建标签路径显示文本
   */
  buildPathText: (tag: Tag, separator: string = ' / '): string => {
    if (!tag.path) return tag.name;
    
    // 这里简化处理，实际应该根据path获取完整路径
    return tag.name;
  },

  /**
   * 将标签列表转换为树形结构
   */
  buildTagTree: (tags: Tag[]): TagTreeNode[] => {
    const tagMap = new Map<number, TagTreeNode>();
    const rootTags: TagTreeNode[] = [];

    // 创建节点映射
    tags.forEach(tag => {
      tagMap.set(tag.id, {
        ...tag,
        key: tag.id,
        title: tag.name,
        children: []
      });
    });

    // 构建树形结构
    tags.forEach(tag => {
      const node = tagMap.get(tag.id)!;
      
      if (tag.pid && tagMap.has(tag.pid)) {
        const parent = tagMap.get(tag.pid)!;
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(node);
      } else {
        rootTags.push(node);
      }
    });

    return rootTags;
  },

  /**
   * 扁平化树形结构
   */
  flattenTagTree: (tree: TagTreeNode[]): Tag[] => {
    const result: Tag[] = [];
    
    const traverse = (nodes: TagTreeNode[]) => {
      nodes.forEach(node => {
        result.push(node);
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };
    
    traverse(tree);
    return result;
  },

  /**
   * 查找标签节点
   */
  findTagNode: (tree: TagTreeNode[], id: number): TagTreeNode | null => {
    for (const node of tree) {
      if (node.id === id) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = this.findTagNode(node.children, id);
        if (found) return found;
      }
    }
    return null;
  },

  /**
   * 获取标签的所有祖先节点
   */
  getTagAncestors: (tree: TagTreeNode[], targetId: number): TagTreeNode[] => {
    const ancestors: TagTreeNode[] = [];
    
    const findPath = (nodes: TagTreeNode[], path: TagTreeNode[]): boolean => {
      for (const node of nodes) {
        const currentPath = [...path, node];
        
        if (node.id === targetId) {
          ancestors.push(...currentPath.slice(0, -1)); // 不包含自己
          return true;
        }
        
        if (node.children && node.children.length > 0) {
          if (findPath(node.children, currentPath)) {
            return true;
          }
        }
      }
      return false;
    };
    
    findPath(tree, []);
    return ancestors;
  },

  /**
   * 验证标签名称
   */
  validateTagName: (name: string): { valid: boolean; message?: string } => {
    if (!name || !name.trim()) {
      return { valid: false, message: '标签名称不能为空' };
    }
    
    if (name.length > 100) {
      return { valid: false, message: '标签名称长度不能超过100个字符' };
    }
    
    // 检查特殊字符
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(name)) {
      return { valid: false, message: '标签名称不能包含特殊字符 < > : " / \\ | ? *' };
    }
    
    return { valid: true };
  },

  /**
   * 格式化标签数据用于导出
   */
  formatTagsForExport: (tags: Tag[]): any[] => {
    return tags.map(tag => ({
      ID: tag.id,
      名称: tag.name,
      类型: this.getTagTypeName(tag.type),
      状态: this.getTagStatusName(tag.status),
      层级: tag.level,
      路径: tag.path,
      父级ID: tag.pid || '',
      创建时间: tag.createdAt,
      更新时间: tag.updatedAt
    }));
  },

  /**
   * 导出标签数据为CSV
   */
  exportToCSV: (tags: Tag[], filename: string): void => {
    const data = this.formatTagsForExport(tags);
    const headers = Object.keys(data[0] || {});
    
    let csvContent = headers.join(',') + '\n';
    
    data.forEach(row => {
      const values = headers.map(header => {
        const value = row[header];
        // 处理包含逗号或引号的值
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      });
      csvContent += values.join(',') + '\n';
    });
    
    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },

  /**
   * 生成标签编码
   */
  generateTagCode: (type: TagType, name: string): string => {
    const typePrefix = {
      [TagType.WORKSHOP_LINE]: 'WL',
      [TagType.MACHINE_GROUP]: 'MG',
      [TagType.PATTERN_GROUP]: 'PG',
      [TagType.PATTERN_UNIT]: 'PU'
    };
    
    const prefix = typePrefix[type] || 'TAG';
    const timestamp = Date.now().toString().slice(-6);
    const nameCode = name.substring(0, 2).toUpperCase();
    
    return `${prefix}${nameCode}${timestamp}`;
  }
};
