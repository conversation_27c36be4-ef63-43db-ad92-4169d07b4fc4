/**
 * 企业管理API接口
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 14:30:00 +08:00; Reason: Task-006 企业管理模块, 创建企业API接口; Principle_Applied: API接口封装;}}
 */

import { http } from './request'

// 企业管理员信息接口
export interface EnterpriseAdmin {
  id: number
  username: string
  realName: string
  email?: string
  phone?: string
}

// 企业信息接口
export interface Enterprise {
  id: number
  name: string
  code: string
  logoUrl?: string
  description?: string
  status: number
  createdAt: string
  updatedAt: string
  statistics?: {
    userCount: number
    departmentCount: number
  }
  admin?: EnterpriseAdmin | null
}

// 企业列表查询参数
export interface EnterpriseListQuery {
  page?: number
  pageSize?: number
  keyword?: string
  status?: number
}

// 企业列表响应
export interface EnterpriseListResponse {
  list: Enterprise[]
  total: number
  page: number
  pageSize: number
}

// 创建企业请求
export interface CreateEnterpriseRequest {
  name: string
  code: string
  logoUrl?: string
  description?: string
  status?: number
  // 企业管理员账号信息
  adminUsername: string
  adminPassword: string
  adminRealName: string
  adminEmail?: string
  adminPhone?: string
}

// 更新企业请求
export interface UpdateEnterpriseRequest {
  name?: string
  code?: string
  logoUrl?: string
  description?: string
  status?: number
}

export const enterpriseApi = {
  /**
   * 获取企业列表
   */
  getEnterpriseList: (params: EnterpriseListQuery): Promise<EnterpriseListResponse> => {
    return http.get('/enterprises', { params })
  },

  /**
   * 获取企业详情
   */
  getEnterpriseById: (id: number): Promise<Enterprise> => {
    return http.get(`/enterprises/${id}`)
  },

  /**
   * 创建企业
   */
  createEnterprise: (data: CreateEnterpriseRequest): Promise<Enterprise> => {
    return http.post('/enterprises', data)
  },

  /**
   * 更新企业
   */
  updateEnterprise: (id: number, data: UpdateEnterpriseRequest): Promise<Enterprise> => {
    return http.put(`/enterprises/${id}`, data)
  },

  /**
   * 删除企业
   */
  deleteEnterprise: (id: number): Promise<void> => {
    return http.delete(`/enterprises/${id}`)
  },

  /**
   * 重置企业管理员密码
   */
  resetAdminPassword: (id: number, newPassword: string): Promise<void> => {
    return http.post(`/enterprises/${id}/reset-admin-password`, { newPassword })
  }
}
