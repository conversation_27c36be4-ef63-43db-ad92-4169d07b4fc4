/**
 * 设备类型API接口
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建前端API接口; Principle_Applied: API封装;}}
 */

import { http } from './request';
import type {
  DeviceModel,
  DeviceModelListQuery,
  DeviceModelListResponse,
  CreateDeviceModelRequest,
  UpdateDeviceModelRequest,
  DeviceModelOption
} from '../types/deviceModel';
import { DeviceModelStatus } from '../types/deviceModel';

/**
 * 设备类型API
 */
export const deviceModelApi = {
  /**
   * 获取设备类型列表
   */
  async getDeviceModelList(params: DeviceModelListQuery): Promise<DeviceModelListResponse> {
    return await http.get('/device-models', { params });
  },

  /**
   * 获取设备类型详情
   */
  async getDeviceModelById(id: number): Promise<DeviceModel> {
    return await http.get(`/device-models/${id}`);
  },

  /**
   * 创建设备类型
   */
  async createDeviceModel(data: CreateDeviceModelRequest): Promise<DeviceModel> {
    return await http.post('/device-models', data);
  },

  /**
   * 更新设备类型
   */
  async updateDeviceModel(id: number, data: UpdateDeviceModelRequest): Promise<DeviceModel> {
    return await http.put(`/device-models/${id}`, data);
  },

  /**
   * 删除设备类型
   */
  async deleteDeviceModel(id: number): Promise<void> {
    await http.delete(`/device-models/${id}`);
  },

  /**
   * 更新设备类型状态
   */
  async updateDeviceModelStatus(id: number, status: DeviceModelStatus): Promise<DeviceModel> {
    return await http.put(`/device-models/${id}/status`, { status });
  },

  /**
   * 获取设备类型选项列表
   */
  async getDeviceModelOptions(): Promise<DeviceModelOption[]> {
    return await http.get('/device-models/options');
  }
};

/**
 * 设备类型工具函数
 */
export const deviceModelUtils = {
  /**
   * 格式化设备类型显示名称
   */
  formatDisplayName(deviceModel: DeviceModel): string {
    return `${deviceModel.name} (${deviceModel.code})`;
  },

  /**
   * 验证型号编码格式
   */
  validateCode(code: string): boolean {
    // 型号编码只能包含字母、数字、下划线和连字符
    const codeRegex = /^[a-zA-Z0-9_-]+$/;
    return codeRegex.test(code);
  },

  /**
   * 获取设备类型状态文本
   */
  getStatusText(status: DeviceModelStatus): string {
    return status === DeviceModelStatus.ENABLED ? '启用' : '禁用';
  },

  /**
   * 获取设备类型状态颜色
   */
  getStatusColor(status: DeviceModelStatus): string {
    return status === DeviceModelStatus.ENABLED ? 'success' : 'default';
  },

  /**
   * 获取设备关联状态文本
   */
  getDeviceCountText(deviceModel: DeviceModel): string {
    const deviceCount = deviceModel.devices?.length || 0;
    return deviceCount > 0 ? `已关联 ${deviceCount} 台设备` : '未关联设备';
  },

  /**
   * 检查是否可以删除设备类型
   */
  canDelete(deviceModel: DeviceModel): boolean {
    return !deviceModel.devices || deviceModel.devices.length === 0;
  },

  /**
   * 格式化配置参数显示
   */
  formatParameter(parameter?: string): string {
    if (!parameter) return '无配置';
    
    try {
      // 尝试解析JSON格式的配置
      const config = JSON.parse(parameter);
      return JSON.stringify(config, null, 2);
    } catch {
      // 如果不是JSON格式，直接返回原文本
      return parameter;
    }
  },

  /**
   * 验证配置参数格式
   */
  validateParameter(parameter?: string): { valid: boolean; error?: string } {
    if (!parameter || parameter.trim() === '') {
      return { valid: true };
    }

    try {
      JSON.parse(parameter);
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: '配置参数必须是有效的JSON格式'
      };
    }
  }
};
