/**
 * 微信绑定API
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 12:13:12 +08:00; Reason: Task-006 前端API接口封装, 创建微信专用API文件; Principle_Applied: API接口封装;}}
 */

import { http } from './request'
import type {
  WechatAuthUrlResponse,
  WechatBindRequest,
  WechatBindResponse,
  WechatUnbindResponse,
  WechatCallbackParams,
  WechatUserInfo,
  WechatBindInfo,
  WechatBindStatus
} from '@/types/wechat'

/**
 * 微信绑定相关API
 */
export const wechatApi = {
  /**
   * 获取微信授权URL
   * @returns Promise<WechatAuthUrlResponse> 包含授权URL和state参数
   */
  getAuthUrl: (): Promise<WechatAuthUrlResponse> => {
    return http.get('/auth/wechat/auth-url')
  },

  /**
   * 处理微信授权回调
   * @param params 微信回调参数
   * @returns Promise<{ userInfo: WechatUserInfo; tempToken: string }> 用户信息和临时token
   */
  handleCallback: (params: WechatCallbackParams): Promise<{
    userInfo: WechatUserInfo;
    tempToken: string;
  }> => {
    return http.post('/auth/wechat/callback', params)
  },

  /**
   * 绑定微信账号
   * @param data 绑定请求数据
   * @returns Promise<WechatBindResponse> 绑定结果
   */
  bind: (data: WechatBindRequest): Promise<WechatBindResponse> => {
    return http.post('/auth/wechat/bind', data)
  },

  /**
   * 解绑微信账号
   * @returns Promise<WechatUnbindResponse> 解绑结果
   */
  unbind: (): Promise<WechatUnbindResponse> => {
    return http.delete('/auth/wechat/unbind')
  },

  /**
   * 检查微信绑定状态
   * @param userInfo 用户信息（包含微信字段）
   * @returns WechatBindInfo 微信绑定状态信息
   */
  checkBindStatus: (userInfo: any): WechatBindInfo => {
    if (!userInfo) {
      return {
        status: 'unbound' as WechatBindStatus,
        error: '用户信息不存在'
      }
    }

    if (userInfo.wechatOpenid) {
      return {
        status: 'bound' as WechatBindStatus,
        openid: userInfo.wechatOpenid,
        unionid: userInfo.wechatUnionid,
        nickname: userInfo.wechatNickname,
        avatar: userInfo.wechatAvatar,
        boundAt: userInfo.wechatBoundAt
      }
    }

    return {
      status: 'unbound' as WechatBindStatus
    }
  },

  /**
   * 生成微信绑定二维码URL（如果需要）
   * @param authUrl 微信授权URL
   * @returns string 二维码生成URL
   */
  generateQrCodeUrl: (authUrl: string): string => {
    // 使用第三方二维码生成服务，这里使用 qr-server.com
    const encodedUrl = encodeURIComponent(authUrl)
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodedUrl}`
  },

  /**
   * 验证微信授权参数
   * @param params 微信回调参数
   * @returns boolean 参数是否有效
   */
  validateCallbackParams: (params: WechatCallbackParams): boolean => {
    // 检查是否有错误
    if (params.error) {
      return false
    }

    // 检查必需参数
    if (!params.code || !params.state) {
      return false
    }

    // 检查参数格式
    if (params.code.length < 10 || params.state.length < 10) {
      return false
    }

    return true
  },

  /**
   * 格式化微信绑定时间
   * @param boundAt 绑定时间字符串
   * @returns string 格式化后的时间
   */
  formatBoundTime: (boundAt?: string): string => {
    if (!boundAt) {
      return '未绑定'
    }

    try {
      const date = new Date(boundAt)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch (error) {
      console.error('格式化微信绑定时间失败:', error)
      return '时间格式错误'
    }
  },

  /**
   * 获取微信绑定状态显示文本
   * @param status 绑定状态
   * @returns string 状态显示文本
   */
  getStatusText: (status: WechatBindStatus): string => {
    const statusMap: Record<WechatBindStatus, string> = {
      'unbound': '未绑定',
      'binding': '绑定中',
      'bound': '已绑定',
      'unbinding': '解绑中',
      'error': '绑定错误'
    }

    return statusMap[status] || '未知状态'
  },

  /**
   * 获取微信绑定状态颜色
   * @param status 绑定状态
   * @returns string Ant Design 颜色类型
   */
  getStatusColor: (status: WechatBindStatus): string => {
    const colorMap: Record<WechatBindStatus, string> = {
      'unbound': 'default',
      'binding': 'processing',
      'bound': 'success',
      'unbinding': 'warning',
      'error': 'error'
    }

    return colorMap[status] || 'default'
  }
}

/**
 * 微信绑定工具函数
 */
export const wechatUtils = {
  /**
   * 检查是否在微信浏览器中
   * @returns boolean 是否在微信浏览器
   */
  isWechatBrowser: (): boolean => {
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('micromessenger')
  },

  /**
   * 检查是否支持微信绑定
   * @returns boolean 是否支持
   */
  isSupportWechatBind: (): boolean => {
    // 检查是否有必要的API支持
    return typeof window !== 'undefined' && 
           typeof navigator !== 'undefined' &&
           typeof fetch !== 'undefined'
  },

  /**
   * 打开微信授权页面
   * @param authUrl 授权URL
   */
  openWechatAuth: (authUrl: string): void => {
    if (wechatUtils.isWechatBrowser()) {
      // 在微信浏览器中直接跳转
      window.location.href = authUrl
    } else {
      // 在其他浏览器中打开新窗口
      window.open(authUrl, '_blank', 'width=400,height=600,scrollbars=yes,resizable=yes')
    }
  },

  /**
   * 生成随机状态参数
   * @returns string 随机状态参数
   */
  generateState: (): string => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  }
}

// 默认导出
export default wechatApi
