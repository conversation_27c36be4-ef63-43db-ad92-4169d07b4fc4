/**
 * 设备管理API
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 14:30:51 +08:00; Reason: 设备管理模块开发, 创建前端设备API封装; Principle_Applied: API封装;}}
 */

import { http } from './request';
import type {
  Device,
  DeviceListQuery,
  DeviceListResponse,
  CreateDeviceRequest,
  UpdateDeviceRequest,
  UpdateWifiConfigRequest,
  DeviceStats,
  DeviceSearchOptions,
  DeviceFilterOptions,
  WiFiSignalLevel
} from '../types/device';
import { WiFiState, RegisterWay, WiFiKeyMgmt } from '../types/device';

/**
 * 设备管理API
 */
export const deviceApi = {
  /**
   * 获取设备列表
   */
  getDeviceList: (params: DeviceListQuery): Promise<DeviceListResponse> => {
    return http.get('/devices', { params });
  },

  /**
   * 获取设备详情
   */
  getDeviceById: (id: number): Promise<Device> => {
    return http.get(`/devices/${id}`);
  },

  /**
   * 创建设备
   */
  createDevice: (data: CreateDeviceRequest): Promise<Device> => {
    return http.post('/devices', data);
  },

  /**
   * 更新设备
   */
  updateDevice: (id: number, data: UpdateDeviceRequest): Promise<Device> => {
    return http.put(`/devices/${id}`, data);
  },

  /**
   * 删除设备
   */
  deleteDevice: (id: number): Promise<void> => {
    return http.delete(`/devices/${id}`);
  },

  /**
   * 批量删除设备
   */
  batchDeleteDevices: async (ids: number[]): Promise<void> => {
    await Promise.all(ids.map(id => deviceApi.deleteDevice(id)));
  },

  /**
   * 更新设备WiFi配置
   */
  updateWifiConfig: (id: number, data: UpdateWifiConfigRequest): Promise<Device> => {
    return http.put(`/devices/${id}/wifi`, data);
  },

  /**
   * 获取设备统计信息
   */
  getDeviceStats: (): Promise<DeviceStats> => {
    return http.get('/devices/stats');
  },

  /**
   * 获取设备搜索选项
   */
  getSearchOptions: (): Promise<DeviceSearchOptions> => {
    return http.get('/devices/search-options');
  }
};

/**
 * 设备工具类
 */
export class DeviceUtils {
  /**
   * 获取WiFi状态显示文本
   */
  static getWifiStateText(state?: string): string {
    const stateMap: Record<string, string> = {
      [WiFiState.CONNECTED]: '已连接',
      [WiFiState.DISCONNECTED]: '未连接',
      [WiFiState.CONNECTING]: '连接中',
      [WiFiState.ERROR]: '连接错误',
      [WiFiState.UNKNOWN]: '未知'
    };
    return stateMap[state || WiFiState.UNKNOWN] || '未知';
  }

  /**
   * 获取WiFi状态颜色
   */
  static getWifiStateColor(state?: string): string {
    const colorMap: Record<string, string> = {
      [WiFiState.CONNECTED]: 'success',
      [WiFiState.DISCONNECTED]: 'default',
      [WiFiState.CONNECTING]: 'processing',
      [WiFiState.ERROR]: 'error',
      [WiFiState.UNKNOWN]: 'warning'
    };
    return colorMap[state || WiFiState.UNKNOWN] || 'default';
  }

  /**
   * 获取设备状态显示文本
   */
  static getDeviceStatusText(device: Device): string {
    if (device.wifiState === WiFiState.CONNECTED) {
      return '在线';
    }
    return '离线';
  }

  /**
   * 获取设备状态颜色
   */
  static getDeviceStatusColor(device: Device): string {
    if (device.wifiState === WiFiState.CONNECTED) {
      return 'success';
    }
    return 'default';
  }

  /**
   * 获取注册方式显示文本
   */
  static getRegisterWayText(way?: string): string {
    const wayMap: Record<string, string> = {
      [RegisterWay.MANUAL]: '手动添加',
      [RegisterWay.AUTO]: '自动发现',
      [RegisterWay.SCAN]: '扫描添加',
      [RegisterWay.IMPORT]: '批量导入'
    };
    return wayMap[way || RegisterWay.MANUAL] || '手动添加';
  }

  /**
   * 获取WiFi加密方式显示文本
   */
  static getWifiKeyMgmtText(keyMgmt?: string): string {
    const keyMgmtMap: Record<string, string> = {
      [WiFiKeyMgmt.NONE]: '无加密',
      [WiFiKeyMgmt.WEP]: 'WEP',
      [WiFiKeyMgmt.WPA]: 'WPA',
      [WiFiKeyMgmt.WPA2]: 'WPA2',
      [WiFiKeyMgmt.WPA3]: 'WPA3'
    };
    return keyMgmtMap[keyMgmt || WiFiKeyMgmt.WPA2] || 'WPA2';
  }

  /**
   * 解析WiFi信号强度
   */
  static parseWifiSignalLevel(signalLevel?: string): WiFiSignalLevel {
    if (!signalLevel) {
      return { level: 0, label: '无信号', color: '#ccc', icon: 'wifi-off' };
    }

    const level = parseInt(signalLevel);
    if (level >= -50) {
      return { level: 4, label: '信号极强', color: '#52c41a', icon: 'wifi' };
    } else if (level >= -60) {
      return { level: 3, label: '信号强', color: '#1890ff', icon: 'wifi' };
    } else if (level >= -70) {
      return { level: 2, label: '信号中等', color: '#faad14', icon: 'wifi' };
    } else if (level >= -80) {
      return { level: 1, label: '信号弱', color: '#ff7875', icon: 'wifi' };
    } else {
      return { level: 0, label: '信号极弱', color: '#f5222d', icon: 'wifi' };
    }
  }

  /**
   * 格式化MAC地址
   */
  static formatMacAddress(mac?: string): string {
    if (!mac) return '';
    return mac.toUpperCase().replace(/[:-]/g, ':');
  }

  /**
   * 验证IP地址格式
   */
  static isValidIpAddress(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  }

  /**
   * 验证MAC地址格式
   */
  static isValidMacAddress(mac: string): boolean {
    const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
    return macRegex.test(mac);
  }

  /**
   * 生成设备编号
   */
  static generateDeviceCode(prefix: string = 'DEV'): string {
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * 获取设备筛选选项
   */
  static getFilterOptions(devices: Device[]): DeviceFilterOptions {
    // 从关联的设备类型信息中获取设备类型选项
    const deviceModels = [...new Set(devices
      .filter(d => d.deviceModelInfo)
      .map(d => d.deviceModelInfo!.name))]
      .map(name => ({ label: name, value: name }));

    const vendors = [...new Set(devices.map(d => d.vendor).filter(Boolean))]
      .map(vendor => ({ label: vendor!, value: vendor! }));

    const wifiStates = [
      { label: '已连接', value: WiFiState.CONNECTED },
      { label: '未连接', value: WiFiState.DISCONNECTED },
      { label: '连接中', value: WiFiState.CONNECTING },
      { label: '连接错误', value: WiFiState.ERROR },
      { label: '未知', value: WiFiState.UNKNOWN }
    ];

    const registerWays = [
      { label: '手动添加', value: RegisterWay.MANUAL },
      { label: '自动发现', value: RegisterWay.AUTO },
      { label: '扫描添加', value: RegisterWay.SCAN },
      { label: '批量导入', value: RegisterWay.IMPORT }
    ];

    return {
      deviceModels,
      vendors,
      wifiStates,
      registerWays
    };
  }

  /**
   * 导出设备数据为CSV
   */
  static exportToCSV(devices: Device[], filename: string = 'devices.csv'): void {
    const headers = [
      '设备名称', '设备编号', '设备SN', 'MAC地址', 'IP地址',
      '机型', '厂商', '状态', 'WiFi状态', '创建时间'
    ];

    const rows = devices.map(device => [
      device.name,
      device.code || '',
      device.sn || '',
      device.mac || '',
      device.ip || '',
      device.deviceModelInfo?.name || '',
      device.vendor || '',
      this.getDeviceStatusText(device),
      this.getWifiStateText(device.wifiState),
      new Date(device.createdAt).toLocaleString()
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * 搜索设备
   */
  static searchDevices(devices: Device[], keyword: string): Device[] {
    if (!keyword.trim()) return devices;

    const searchTerm = keyword.toLowerCase();
    return devices.filter(device =>
      device.name.toLowerCase().includes(searchTerm) ||
      (device.code && device.code.toLowerCase().includes(searchTerm)) ||
      (device.sn && device.sn.toLowerCase().includes(searchTerm)) ||
      (device.deviceModelInfo?.name && device.deviceModelInfo.name.toLowerCase().includes(searchTerm)) ||
      (device.vendor && device.vendor.toLowerCase().includes(searchTerm)) ||
      (device.ip && device.ip.includes(searchTerm)) ||
      (device.mac && device.mac.toLowerCase().includes(searchTerm))
    );
  }

  /**
   * 按字段排序设备
   */
  static sortDevices(devices: Device[], field: keyof Device, order: 'asc' | 'desc' = 'asc'): Device[] {
    return [...devices].sort((a, b) => {
      const aValue = a[field];
      const bValue = b[field];

      if (aValue === undefined || aValue === null) return 1;
      if (bValue === undefined || bValue === null) return -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return order === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return order === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });
  }
}

// 导出工具类
export const deviceUtils = DeviceUtils;
