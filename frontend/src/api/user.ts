/**
 * 用户管理API
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 20:53:00 +08:00; Reason: Task-006 用户管理模块, 创建用户API; Principle_Applied: API接口封装;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 12:13:12 +08:00; Reason: Task-006 前端API接口封装, 添加微信相关API方法; Principle_Applied: API接口封装;}}
 */

import { http } from './request'
import type {
  User,
  UserListQuery,
  UserListResponse,
  CreateUserRequest,
  UpdateUserRequest,
  ResetPasswordRequest,
  DepartmentTreeNode,
  EnterpriseInfo,
  Role,
  CreateDepartmentRequest,
  UpdateDepartmentRequest
} from '@/types/user'
import type {
  WechatAuthUrlResponse,
  WechatBindRequest,
  WechatBindResponse,
  WechatUnbindResponse
} from '@/types/wechat'

export const userApi = {
  /**
   * 获取用户列表
   */
  getUserList: (params: UserListQuery): Promise<UserListResponse> => {
    return http.get('/users', { params })
  },

  /**
   * 获取用户详情
   */
  getUserById: (id: number): Promise<User> => {
    return http.get(`/users/${id}`)
  },

  /**
   * 创建用户
   */
  createUser: (data: CreateUserRequest): Promise<User> => {
    return http.post('/users', data)
  },

  /**
   * 更新用户
   */
  updateUser: (id: number, data: UpdateUserRequest): Promise<User> => {
    return http.put(`/users/${id}`, data)
  },

  /**
   * 删除用户
   */
  deleteUser: (id: number): Promise<void> => {
    return http.delete(`/users/${id}`)
  },

  /**
   * 重置用户密码
   */
  resetPassword: (id: number, data: ResetPasswordRequest): Promise<void> => {
    return http.post(`/users/${id}/reset-password`, data)
  },

  /**
   * 更新个人信息
   */
  updateProfile: (data: { realName: string; email?: string; phone?: string }): Promise<any> => {
    return http.put('/auth/profile', data)
  },

  /**
   * 修改密码
   */
  changePassword: (data: { currentPassword: string; newPassword: string }): Promise<void> => {
    return http.post('/auth/change-password', data)
  },

  // ==================== 微信相关API ====================

  /**
   * 获取微信授权URL
   */
  getWechatAuthUrl: (): Promise<WechatAuthUrlResponse> => {
    return http.get('/auth/wechat/auth-url')
  },

  /**
   * 绑定微信账号
   */
  bindWechat: (data: WechatBindRequest): Promise<WechatBindResponse> => {
    return http.post('/auth/wechat/bind', data)
  },

  /**
   * 解绑微信账号
   */
  unbindWechat: (): Promise<WechatUnbindResponse> => {
    return http.delete('/auth/wechat/unbind')
  }
}

export const departmentApi = {
  /**
   * 获取部门树
   */
  getDepartmentTree: (enterpriseId?: number): Promise<DepartmentTreeNode[]> => {
    const params = enterpriseId ? { enterpriseId } : {}
    return http.get('/departments/tree', { params })
  },

  /**
   * 获取部门详情
   */
  getDepartmentById: (id: number): Promise<any> => {
    return http.get(`/departments/${id}`)
  },

  /**
   * 获取当前企业信息
   */
  getCurrentEnterpriseInfo: (enterpriseId?: number): Promise<EnterpriseInfo> => {
    const params = enterpriseId ? { enterpriseId } : {}
    return http.get('/departments/enterprise/current', { params })
  },

  /**
   * 创建部门
   */
  createDepartment: (data: CreateDepartmentRequest): Promise<DepartmentTreeNode> => {
    return http.post('/departments', data)
  },

  /**
   * 更新部门
   */
  updateDepartment: (id: number, data: UpdateDepartmentRequest): Promise<DepartmentTreeNode> => {
    return http.put(`/departments/${id}`, data)
  },

  /**
   * 删除部门
   */
  deleteDepartment: (id: number): Promise<void> => {
    return http.delete(`/departments/${id}`)
  }
}

export const roleApi = {
  /**
   * 获取角色列表（用于用户分配角色）
   */
  getRoleList: (): Promise<Role[]> => {
    return http.get('/roles')
  }
}
