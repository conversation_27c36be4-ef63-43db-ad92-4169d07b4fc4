/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AImage: typeof import('ant-design-vue/es')['Image']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    AMonthPicker: typeof import('ant-design-vue/es')['MonthPicker']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    APopover: typeof import('ant-design-vue/es')['Popover']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    ATreeSelect: typeof import('ant-design-vue/es')['TreeSelect']
    ATypographyParagraph: typeof import('ant-design-vue/es')['TypographyParagraph']
    BreadcrumbNav: typeof import('./src/components/Layout/BreadcrumbNav.vue')['default']
    DepartmentModal: typeof import('./src/components/user/modals/DepartmentModal.vue')['default']
    DepartmentTree: typeof import('./src/components/user/DepartmentTree.vue')['default']
    DeviceDetailModal: typeof import('./src/components/equipment/DeviceDetailModal.vue')['default']
    DeviceFormModal: typeof import('./src/components/equipment/DeviceFormModal.vue')['default']
    DeviceModelDetailModal: typeof import('./src/components/equipment/DeviceModelDetailModal.vue')['default']
    DeviceModelFormModal: typeof import('./src/components/equipment/DeviceModelFormModal.vue')['default']
    DeviceModelManagement: typeof import('./src/components/equipment/DeviceModelManagement.vue')['default']
    EnterpriseEditModal: typeof import('./src/components/user/modals/EnterpriseEditModal.vue')['default']
    EnterpriseInfoCard: typeof import('./src/components/user/EnterpriseInfoCard.vue')['default']
    MainLayout: typeof import('./src/components/Layout/MainLayout.vue')['default']
    PatternDetailModal: typeof import('./src/components/digital/PatternDetailModal.vue')['default']
    PatternFormModal: typeof import('./src/components/digital/PatternFormModal.vue')['default']
    PatternGroupFormModal: typeof import('./src/components/digital/PatternGroupFormModal.vue')['default']
    PatternGroupManagement: typeof import('./src/components/digital/PatternGroupManagement.vue')['default']
    PatternStatsCharts: typeof import('./src/components/digital/PatternStatsCharts.vue')['default']
    PermissionManagement: typeof import('./src/components/user/PermissionManagement.vue')['default']
    PermissionWrapper: typeof import('./src/components/Permission/PermissionWrapper.vue')['default']
    ResetPasswordModal: typeof import('./src/components/user/modals/ResetPasswordModal.vue')['default']
    RoleModal: typeof import('./src/components/user/modals/RoleModal.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SideMenu: typeof import('./src/components/Layout/SideMenu.vue')['default']
    TagFormModal: typeof import('./src/components/tag/TagFormModal.vue')['default']
    TagManagementModal: typeof import('./src/components/tag/TagManagementModal.vue')['default']
    UserList: typeof import('./src/components/user/UserList.vue')['default']
    UserModal: typeof import('./src/components/user/modals/UserModal.vue')['default']
    UserSearchBar: typeof import('./src/components/user/UserSearchBar.vue')['default']
    WechatBindCard: typeof import('./src/components/user/WechatBindCard.vue')['default']
  }
}
