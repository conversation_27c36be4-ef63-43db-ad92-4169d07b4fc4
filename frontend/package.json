{"name": "embroidery-management-frontend", "version": "1.0.0", "description": "刺绣管理系统前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "keywords": ["embroidery", "management", "vue3", "typescript", "ant-design-vue"], "author": "Embroidery Management Team", "license": "MIT", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.0.7", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-echarts": "^7.0.3", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^4.5.0", "@vitest/coverage-v8": "^1.0.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.2", "@vue/tsconfig": "^0.4.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "jsdom": "^23.0.1", "prettier": "^3.1.0", "typescript": "~5.3.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0", "vitest": "^1.0.0", "vue-tsc": "^1.8.22"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}