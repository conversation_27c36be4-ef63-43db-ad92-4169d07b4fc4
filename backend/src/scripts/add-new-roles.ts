/**
 * 为现有企业添加新角色脚本
 * 添加老板角色（拥有企业管理员权限）和财务角色（拥有工资管理权限）
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-03 19:20:00 +08:00; Reason: 为现有企业添加老板和财务角色; Principle_Applied: 角色权限管理;}}
 */

import { sequelize } from '@/shared/database';
import { Role } from '@/shared/database/models/Role';
import { Permission } from '@/shared/database/models/Permission';
import { Enterprise } from '@/shared/database/models/Enterprise';
import { logger } from '@/shared/utils/logger';
import { Op } from 'sequelize';

// 确保模型关联已初始化
import '@/shared/database/associations';

async function addNewRoles() {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('开始为现有企业添加新角色...');

    // 查找所有企业
    const enterprises = await Enterprise.findAll({
      transaction
    });

    logger.info(`找到 ${enterprises.length} 个企业`);

    if (enterprises.length === 0) {
      logger.info('没有找到企业，跳过角色创建');
      await transaction.commit();
      return;
    }

    // 获取老板角色需要的权限（除企业管理外的所有权限）
    const bossPermissions = await Permission.findAll({
      where: {
        module: {
          [Op.ne]: 'enterprise' // 排除企业管理权限
        }
      },
      transaction
    });

    // 获取财务角色需要的权限（工资管理权限）
    const financePermissions = await Permission.findAll({
      where: {
        module: 'salary'
      },
      transaction
    });

    logger.info(`老板角色权限数量: ${bossPermissions.length}`);
    logger.info(`财务角色权限数量: ${financePermissions.length}`);

    // 为每个企业添加新角色
    for (const enterprise of enterprises) {
      logger.info(`处理企业: ${enterprise.name} (ID: ${enterprise.id})`);

      // 检查是否已存在老板角色
      const existingBossRole = await Role.findOne({
        where: {
          code: 'BOSS',
          enterpriseId: enterprise.id
        },
        transaction
      });

      if (!existingBossRole) {
        // 创建老板角色
        const bossRole = await Role.create({
          name: '老板',
          code: 'BOSS',
          description: '老板，拥有企业内所有权限',
          enterpriseId: enterprise.id,
          isSystem: false,
        }, { transaction });

        // 为老板角色分配权限
        await bossRole.setPermissions(bossPermissions);
        logger.info(`为企业 ${enterprise.name} 创建老板角色成功`);
      } else {
        logger.info(`企业 ${enterprise.name} 已存在老板角色，跳过创建`);
      }

      // 检查是否已存在财务角色
      const existingFinanceRole = await Role.findOne({
        where: {
          code: 'FINANCE',
          enterpriseId: enterprise.id
        },
        transaction
      });

      if (!existingFinanceRole) {
        // 创建财务角色
        const financeRole = await Role.create({
          name: '财务',
          code: 'FINANCE',
          description: '财务人员，负责工资管理',
          enterpriseId: enterprise.id,
          isSystem: false,
        }, { transaction });

        // 为财务角色分配权限
        await financeRole.setPermissions(financePermissions);
        logger.info(`为企业 ${enterprise.name} 创建财务角色成功`);
      } else {
        logger.info(`企业 ${enterprise.name} 已存在财务角色，跳过创建`);
      }
    }

    await transaction.commit();
    logger.info('新角色添加完成');

  } catch (error) {
    await transaction.rollback();
    logger.error('添加新角色失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addNewRoles()
    .then(() => {
      logger.info('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('脚本执行失败:', error);
      process.exit(1);
    });
}

export { addNewRoles };
