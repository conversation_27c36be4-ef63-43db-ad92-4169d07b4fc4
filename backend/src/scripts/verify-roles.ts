/**
 * 验证角色创建脚本
 * 检查老板和财务角色是否正确创建
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-04 08:58:00 +08:00; Reason: 验证新角色是否正确创建; Principle_Applied: 数据验证;}}
 */

// import { sequelize } from '@/shared/database';
import { Role } from '@/shared/database/models/Role';
import { Permission } from '@/shared/database/models/Permission';
import { Enterprise } from '@/shared/database/models/Enterprise';
import { logger } from '@/shared/utils/logger';

// 确保模型关联已初始化
import '@/shared/database/associations';

async function verifyRoles() {
  try {
    logger.info('开始验证角色创建情况...');

    // 查找所有企业
    const enterprises = await Enterprise.findAll();
    logger.info(`找到 ${enterprises.length} 个企业`);

    for (const enterprise of enterprises) {
      logger.info(`\n检查企业: ${enterprise.name} (ID: ${enterprise.id})`);
      
      // 查找该企业的所有角色
      const roles = await Role.findAll({
        where: {
          enterpriseId: enterprise.id
        },
        include: [
          {
            model: Permission,
            as: 'permissions',
            attributes: ['id', 'code', 'module']
          }
        ],
        order: [['name', 'ASC']]
      });

      logger.info(`企业 ${enterprise.name} 的角色列表:`);
      for (const role of roles) {
        const permissions = (role as any).permissions || [];
        logger.info(`  - ${role.name} (${role.code}): ${permissions.length} 个权限`);
        
        // 特别检查老板和财务角色
        if (role.code === 'BOSS') {
          logger.info(`    老板角色权限模块: ${[...new Set(permissions.map((p: any) => p.module))].join(', ')}`);
        } else if (role.code === 'FINANCE') {
          logger.info(`    财务角色权限模块: ${[...new Set(permissions.map((p: any) => p.module))].join(', ')}`);
        }
      }

      // 检查是否有老板和财务角色
      const bossRole = roles.find(r => r.code === 'BOSS');
      const financeRole = roles.find(r => r.code === 'FINANCE');
      
      if (!bossRole) {
        logger.error(`❌ 企业 ${enterprise.name} 缺少老板角色`);
      } else {
        logger.info(`✅ 企业 ${enterprise.name} 有老板角色`);
      }
      
      if (!financeRole) {
        logger.error(`❌ 企业 ${enterprise.name} 缺少财务角色`);
      } else {
        logger.info(`✅ 企业 ${enterprise.name} 有财务角色`);
      }
    }

    logger.info('\n角色验证完成');

  } catch (error) {
    logger.error('验证角色失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  verifyRoles()
    .then(() => {
      logger.info('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('脚本执行失败:', error);
      process.exit(1);
    });
}

export { verifyRoles };
