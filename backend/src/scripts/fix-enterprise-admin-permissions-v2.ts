/**
 * 修复企业管理员权限脚本 v3
 * 将企业管理员权限设置为除企业管理外的所有权限
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-03 17:45:00 +08:00; Reason: 修复企业管理员权限，给予除企业管理外的所有权限; Principle_Applied: 企业管理员全权限原则;}}
 */

import { sequelize } from '@/shared/database';
import { Role } from '@/shared/database/models/Role';
import { Permission } from '@/shared/database/models/Permission';
import { logger } from '@/shared/utils/logger';
import { Op } from 'sequelize';

// 确保模型关联已初始化
import '@/shared/database/associations';

async function fixEnterpriseAdminPermissions() {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('开始修复企业管理员权限（v3）...');

    // 查找所有企业管理员角色
    const allEnterpriseAdminRoles = await Role.findAll({
      where: {
        code: 'ENTERPRISE_ADMIN'
      },
      include: [
        {
          model: Permission,
          as: 'permissions'
        }
      ],
      transaction
    });

    // 过滤出企业角色（有enterpriseId的）
    const enterpriseAdminRoles = allEnterpriseAdminRoles.filter(role => role.enterpriseId !== null);

    logger.info(`找到 ${enterpriseAdminRoles.length} 个企业管理员角色`);

    // 获取企业管理员相关权限（除企业管理外的所有权限）
    const organizationPermissions = await Permission.findAll({
      where: {
        module: {
          [Op.ne]: 'enterprise' // 排除企业管理权限，其他所有权限都给企业管理员
        }
      },
      transaction
    });

    logger.info(`找到 ${organizationPermissions.length} 个企业管理员权限:`,
      organizationPermissions.map(p => p.code));

    // 为每个企业管理员角色重新分配权限
    for (const role of enterpriseAdminRoles) {
      const currentPermissions = (role as any).permissions || [];
      logger.info(`处理角色: ${role.name} (ID: ${role.id}), 当前权限数量: ${currentPermissions.length}`);

      // 检查当前权限是否包含企业管理权限（企业管理员不应该有企业管理权限）
      const hasEnterprisePermissions = currentPermissions.some((p: any) =>
        p.module === 'enterprise'
      );

      if (hasEnterprisePermissions) {
        logger.info(`角色 ${role.name} 有企业管理权限，需要移除`);

        // 重新设置权限，只包含企业管理员权限（组织管理和设备管理）
        await role.setPermissions(organizationPermissions);

        // 重新查询验证权限更新
        const updatedRole = await Role.findByPk(role.id, {
          include: [{ model: Permission, as: 'permissions' }],
          transaction
        });
        const updatedPermissions = (updatedRole as any)?.permissions || [];
        const stillHasEnterprisePermissions = updatedPermissions.some((p: any) =>
          p.module === 'enterprise'
        );

        if (stillHasEnterprisePermissions) {
          throw new Error(`角色 ${role.name} 仍然有企业管理权限，更新失败`);
        }
        
        logger.info(`角色 ${role.name} 权限更新成功，新权限数量: ${updatedPermissions.length}`);
        logger.info(`新权限列表: ${updatedPermissions.map((p: any) => p.code).join(', ')}`);
      } else {
        logger.info(`角色 ${role.name} 没有企业管理权限，检查是否有完整的企业管理员权限`);
        
        // 检查是否有完整的组织管理权限
        const currentPermissionCodes = currentPermissions.map((p: any) => p.code);
        const requiredPermissionCodes = organizationPermissions.map(p => p.code);
        const missingPermissions = requiredPermissionCodes.filter(code => !currentPermissionCodes.includes(code));
        
        if (missingPermissions.length > 0) {
          logger.info(`角色 ${role.name} 缺少企业管理员权限: ${missingPermissions.join(', ')}`);
          await role.setPermissions(organizationPermissions);
          logger.info(`角色 ${role.name} 权限补充完成`);
        } else {
          logger.info(`角色 ${role.name} 权限正确，无需修改`);
        }
      }
    }

    await transaction.commit();
    logger.info('企业管理员权限修复完成（v3）');

  } catch (error) {
    await transaction.rollback();
    logger.error('修复企业管理员权限失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixEnterpriseAdminPermissions()
    .then(() => {
      logger.info('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('脚本执行失败:', error);
      process.exit(1);
    });
}

export { fixEnterpriseAdminPermissions };
