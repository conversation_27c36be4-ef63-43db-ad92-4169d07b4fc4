/**
 * 清理默认企业数据脚本
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 19:00:00 +08:00; Reason: 清理默认企业数据，只保留超级管理员; Principle_Applied: 数据清理;}}
 */

import { sequelize } from '@/shared/database';
import { Enterprise, User, Department, Role, UserRole } from '@/shared/database/models';
import { logger } from '@/shared/utils/logger';
import { Op } from 'sequelize';

/**
 * 清理默认企业及相关数据
 */
async function cleanupDefaultEnterprise(): Promise<void> {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('开始清理默认企业数据...');

    // 查找默认企业
    const defaultEnterprise = await Enterprise.findOne({
      where: { code: 'DEFAULT' }
    });

    if (!defaultEnterprise) {
      logger.info('未找到默认企业，无需清理');
      await transaction.commit();
      return;
    }

    const enterpriseId = defaultEnterprise.id;
    logger.info(`找到默认企业: ${defaultEnterprise.name} (ID: ${enterpriseId})`);

    // 获取统计信息
    const userCount = await User.count({ where: { enterpriseId } });
    const departmentCount = await Department.count({ where: { enterpriseId } });
    const roleCount = await Role.count({ where: { enterpriseId } });

    logger.info('清理前统计:', {
      users: userCount,
      departments: departmentCount,
      roles: roleCount,
    });

    // 1. 删除企业下的所有用户角色关联
    const enterpriseUsers = await User.findAll({ 
      where: { enterpriseId },
      attributes: ['id'],
      transaction
    });
    
    if (enterpriseUsers.length > 0) {
      const userIds = enterpriseUsers.map(user => user.id);
      await UserRole.destroy({
        where: { userId: { [Op.in]: userIds } },
        transaction
      });
      logger.info(`删除了 ${enterpriseUsers.length} 个用户的角色关联`);
    }

    // 2. 删除企业下的所有用户
    const deletedUsers = await User.destroy({
      where: { enterpriseId },
      transaction
    });
    logger.info(`删除了 ${deletedUsers} 个用户`);

    // 3. 删除企业下的所有部门
    const deletedDepartments = await Department.destroy({
      where: { enterpriseId },
      transaction
    });
    logger.info(`删除了 ${deletedDepartments} 个部门`);

    // 4. 删除企业下的所有角色
    const deletedRoles = await Role.destroy({
      where: { enterpriseId },
      transaction
    });
    logger.info(`删除了 ${deletedRoles} 个角色`);

    // 5. 最后删除企业
    await defaultEnterprise.destroy({ transaction });
    logger.info(`删除了默认企业: ${defaultEnterprise.name}`);

    await transaction.commit();
    logger.info('默认企业数据清理完成');

  } catch (error) {
    await transaction.rollback();
    logger.error('清理默认企业数据失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  try {
    // 连接数据库
    await sequelize.authenticate();
    logger.info('数据库连接成功');

    // 执行清理
    await cleanupDefaultEnterprise();

    logger.info('脚本执行完成');
    process.exit(0);
  } catch (error) {
    logger.error('脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { cleanupDefaultEnterprise };
