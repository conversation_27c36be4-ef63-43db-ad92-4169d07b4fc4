/**
 * 修复企业管理员权限脚本
 * 移除企业管理员角色的企业管理权限（enterprise模块权限）
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-01 10:30:00 +08:00; Reason: 修复企业管理员权限，移除enterprise模块权限; Principle_Applied: 权限分离;}}
 */

import { sequelize } from '@/shared/database';
import { Role, Permission, initializeAssociations } from '@/shared/database/models';
import { logger } from '@/shared/utils/logger';
import { Op } from 'sequelize';

/**
 * 修复企业管理员权限
 */
async function fixEnterpriseAdminPermissions(): Promise<void> {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('开始修复企业管理员权限...');

    // 查找所有企业管理员角色
    const enterpriseAdminRoles = await Role.findAll({
      where: {
        code: 'ENTERPRISE_ADMIN'
      },
      include: [
        {
          model: Permission,
          as: 'permissions'
        }
      ],
      transaction
    });

    // 过滤出企业角色（有enterpriseId的）
    const enterpriseRoles = enterpriseAdminRoles.filter(role => role.enterpriseId !== null);

    logger.info(`找到 ${enterpriseRoles.length} 个企业管理员角色`);

    // 获取企业管理权限（需要移除的权限）
    const enterprisePermissions = await Permission.findAll({
      where: {
        module: 'enterprise'
      },
      transaction
    });

    logger.info(`找到 ${enterprisePermissions.length} 个企业管理权限:`, 
      enterprisePermissions.map(p => p.code));

    // 获取非企业管理权限（应该保留的权限）
    const nonEnterprisePermissions = await Permission.findAll({
      where: {
        module: { [Op.ne]: 'enterprise' }
      },
      transaction
    });

    logger.info(`找到 ${nonEnterprisePermissions.length} 个非企业管理权限`);

    // 为每个企业管理员角色重新分配权限
    for (const role of enterpriseRoles) {
      logger.info(`处理企业管理员角色: ${role.name} (ID: ${role.id}, 企业ID: ${role.enterpriseId})`);
      
      // 获取当前权限
      const currentPermissions = (role as any).permissions || [];
      logger.info(`当前权限数量: ${currentPermissions.length}`);

      // 检查是否有企业管理权限
      const hasEnterprisePermissions = currentPermissions.some((p: any) => p.module === 'enterprise');

      if (hasEnterprisePermissions) {
        logger.info(`角色 ${role.name} 有企业管理权限，需要移除`);

        // 重新设置权限，只包含非企业管理权限
        await role.setPermissions(nonEnterprisePermissions);

        // 重新查询验证权限更新
        const updatedRole = await Role.findByPk(role.id, {
          include: [{ model: Permission, as: 'permissions' }],
          transaction
        });
        const updatedPermissions = (updatedRole as any)?.permissions || [];
        const stillHasEnterprisePermissions = updatedPermissions.some((p: any) => p.module === 'enterprise');
        
        if (stillHasEnterprisePermissions) {
          throw new Error(`角色 ${role.name} 仍然有企业管理权限，更新失败`);
        }
        
        logger.info(`角色 ${role.name} 权限更新成功，新权限数量: ${updatedPermissions.length}`);
      } else {
        logger.info(`角色 ${role.name} 没有企业管理权限，无需修改`);
      }
    }

    await transaction.commit();
    logger.info('企业管理员权限修复完成');

  } catch (error) {
    await transaction.rollback();
    logger.error('修复企业管理员权限失败:', error);
    throw error;
  }
}

/**
 * 验证修复结果
 */
async function verifyFix(): Promise<void> {
  try {
    logger.info('开始验证修复结果...');

    // 查找所有企业管理员角色
    const enterpriseAdminRoles = await Role.findAll({
      where: {
        code: 'ENTERPRISE_ADMIN'
      },
      include: [
        {
          model: Permission,
          as: 'permissions'
        }
      ]
    });

    // 过滤出企业角色（有enterpriseId的）
    const enterpriseRoles = enterpriseAdminRoles.filter(role => role.enterpriseId !== null);

    for (const role of enterpriseRoles) {
      const permissions = (role as any).permissions || [];
      const enterprisePermissions = permissions.filter((p: any) => p.module === 'enterprise');

      if (enterprisePermissions.length > 0) {
        logger.error(`验证失败: 角色 ${role.name} (ID: ${role.id}) 仍然有 ${enterprisePermissions.length} 个企业管理权限:`,
          enterprisePermissions.map((p: any) => p.code));
      } else {
        logger.info(`验证成功: 角色 ${role.name} (ID: ${role.id}) 没有企业管理权限`);
      }
    }

    logger.info('验证完成');
  } catch (error) {
    logger.error('验证失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  try {
    // 连接数据库
    await sequelize.authenticate();
    logger.info('数据库连接成功');

    // 初始化模型关联
    initializeAssociations();
    logger.info('模型关联初始化完成');

    // 执行修复
    await fixEnterpriseAdminPermissions();

    // 验证修复结果
    await verifyFix();

    logger.info('脚本执行完成');
    process.exit(0);
  } catch (error) {
    logger.error('脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { fixEnterpriseAdminPermissions, verifyFix };
