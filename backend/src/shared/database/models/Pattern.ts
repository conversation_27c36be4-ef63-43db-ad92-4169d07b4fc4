import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 花样属性接口
export interface PatternAttributes {
  id: number;
  enterpriseId: number;
  code?: string;
  name: string;
  remark?: string;
  groupId?: number;
  fileType?: string;
  stitch?: number;
  jumps?: number;
  trim?: number;
  colors?: number;
  baseLine?: number;
  surfaceLine?: number;
  goldPieceNum?: number;
  minX?: number;
  maxX?: number;
  minY?: number;
  maxY?: number;
  width?: number;
  height?: number;
  goldPieceLine?: string;
  needle?: string;
  mergeGoldPieceLine?: string;
  image?: string;
  createUser?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 创建花样时的可选属性
export interface PatternCreationAttributes
  extends Optional<PatternAttributes, 'id' | 'code' | 'remark' | 'groupId' | 'fileType' | 'stitch' | 'jumps' | 'trim' | 'colors' | 'baseLine' | 'surfaceLine' | 'goldPieceNum' | 'minX' | 'maxX' | 'minY' | 'maxY' | 'width' | 'height' | 'goldPieceLine' | 'needle' | 'mergeGoldPieceLine' | 'image' | 'createUser' | 'createdAt' | 'updatedAt'> {}

/**
 * 花样模型类
 */
export class Pattern extends Model<PatternAttributes, PatternCreationAttributes> implements PatternAttributes {
  public id!: number;
  public enterpriseId!: number;
  public code?: string;
  public name!: string;
  public remark?: string;
  public groupId?: number;
  public fileType?: string;
  public stitch?: number;
  public jumps?: number;
  public trim?: number;
  public colors?: number;
  public baseLine?: number;
  public surfaceLine?: number;
  public goldPieceNum?: number;
  public minX?: number;
  public maxX?: number;
  public minY?: number;
  public maxY?: number;
  public width?: number;
  public height?: number;
  public goldPieceLine?: string;
  public needle?: string;
  public mergeGoldPieceLine?: string;
  public image?: string;
  public createUser?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public enterprise?: any;
  public group?: any;
}

// 初始化花样模型
Pattern.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '花样ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'code',
      comment: '花样编号',
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'name',
      comment: '花样名称',
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'remark',
      comment: '备注',
    },
    groupId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'group_id',
      comment: '花样分组ID',
      references: {
        model: 'em_tag',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    fileType: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'file_type',
      comment: '花样类型',
    },
    stitch: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'stitch',
      comment: '花样针数',
    },
    jumps: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'jumps',
      comment: '跳针次数',
    },
    trim: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'trim',
      comment: '剪线次数',
    },
    colors: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'colors',
      comment: '换色数量',
    },
    baseLine: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'base_line',
      comment: '底线长度',
    },
    surfaceLine: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'surface_line',
      comment: '面线长度',
    },
    goldPieceNum: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'gold_piece_num',
      comment: '金片数量',
    },
    minX: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'min_x',
      comment: 'x最小',
    },
    maxX: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'max_x',
      comment: 'x最大',
    },
    minY: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'min_y',
      comment: 'y最小',
    },
    maxY: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'max_y',
      comment: 'y最大',
    },
    width: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'width',
      comment: '宽',
    },
    height: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'height',
      comment: '高',
    },
    goldPieceLine: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'gold_piece_line',
      comment: '金片线',
    },
    needle: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'needle',
      comment: '针线',
    },
    mergeGoldPieceLine: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'merge_gold_piece_line',
      comment: '金线片合并',
    },
    image: {
      type: DataTypes.STRING(255),
      allowNull: true,
      field: 'image',
      comment: '图片',
    },
    createUser: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'create_user',
      comment: '创建人',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_pattern',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'pattern_enterprise_id_idx',
      },
      {
        fields: ['code'],
        name: 'pattern_code_idx',
      },
      {
        fields: ['name'],
        name: 'pattern_name_idx',
      },
      {
        fields: ['group_id'],
        name: 'pattern_group_id_idx',
      },
      {
        fields: ['file_type'],
        name: 'pattern_file_type_idx',
      },
      {
        fields: ['create_user'],
        name: 'pattern_create_user_idx',
      },
      // 复合索引
      {
        fields: ['enterprise_id', 'group_id'],
        name: 'pattern_enterprise_group_idx',
      },
      {
        fields: ['enterprise_id', 'file_type'],
        name: 'pattern_enterprise_file_type_idx',
      },
    ],
  }
);
