/**
 * 设备类型模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建设备类型数据模型; Principle_Applied: 数据模型设计;}}
 */

import { DataTypes, Model, Optional, Op } from 'sequelize';
import { sequelize } from '../index';

// 设备类型状态枚举
export enum DeviceModelStatus {
  DISABLED = 0,         // 禁用
  ENABLED = 1           // 启用
}

// 设备类型属性接口
export interface DeviceModelAttributes {
  id: number;
  enterpriseId: number;
  code: string;
  name: string;
  parameter?: string;
  status: DeviceModelStatus;
  createdAt: Date;
  updatedAt: Date;
}

// 创建设备类型时的可选属性
export interface DeviceModelCreationAttributes
  extends Optional<DeviceModelAttributes, 'id' | 'parameter' | 'status' | 'createdAt' | 'updatedAt'> {}

// 设备类型模型类
export class DeviceModel extends Model<DeviceModelAttributes, DeviceModelCreationAttributes> implements DeviceModelAttributes {
  public id!: number;
  public enterpriseId!: number;
  public code!: string;
  public name!: string;
  public parameter?: string;
  public status!: DeviceModelStatus;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public readonly enterprise?: any;
  public readonly devices?: any[];

  /**
   * 获取状态名称
   */
  public getStatusName(): string {
    return this.status === DeviceModelStatus.ENABLED ? '启用' : '禁用';
  }

  /**
   * 检查是否启用
   */
  public isEnabled(): boolean {
    return this.status === DeviceModelStatus.ENABLED;
  }
}

// 初始化设备类型模型
DeviceModel.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '设备类型ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '型号编码',
      validate: {
        notEmpty: {
          msg: '型号编码不能为空',
        },
        len: {
          args: [1, 50],
          msg: '型号编码长度必须在1-50个字符之间',
        },
      },
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '名称',
      validate: {
        notEmpty: {
          msg: '设备类型名称不能为空',
        },
        len: {
          args: [1, 100],
          msg: '设备类型名称长度必须在1-100个字符之间',
        },
      },
    },
    parameter: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '型号对应的配置',
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: DeviceModelStatus.ENABLED,
      comment: '状态：1-启用，0-禁用',
      validate: {
        isIn: {
          args: [[DeviceModelStatus.DISABLED, DeviceModelStatus.ENABLED]],
          msg: '状态值必须是0（禁用）或1（启用）',
        },
      },
    },

    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_device_model',
    modelName: 'DeviceModel',
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: false,
    indexes: [
      {
        unique: true,
        fields: ['enterprise_id', 'code'],
        name: 'device_model_enterprise_code_unique',
      },
      {
        fields: ['enterprise_id'],
        name: 'device_model_enterprise_id_index',
      },
      {
        fields: ['code'],
        name: 'device_model_code_index',
      },
    ],
    validate: {
      // 企业内型号编码唯一性验证
      async enterpriseCodeUnique() {
        const existing = await DeviceModel.findOne({
          where: {
            enterpriseId: this.enterpriseId as number,
            code: this.code as string,
            id: { [Op.ne]: (this.id as number) || 0 },
          },
        });
        if (existing) {
          throw new Error('该企业内型号编码已存在');
        }
      },
    },
  }
);

export default DeviceModel;
