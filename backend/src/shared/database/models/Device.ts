/**
 * 设备模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 14:30:51 +08:00; Reason: 设备管理模块开发, 创建设备数据模型; Principle_Applied: 数据模型设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 设备属性接口
export interface DeviceAttributes {
  id: number;
  enterpriseId: number;
  code?: string;
  sn?: string;
  mac?: string;
  ip?: string;
  name: string;
  remark?: string;
  controlModel?: string;
  deviceModelId?: number;
  vendor?: string;
  headSpace?: number;
  headNum?: number;
  headNeedleNum?: number;
  formularHeadSpace?: number;
  formularHeadNum?: number;
  formularLength?: number;
  displaySoftware?: string;
  controlSoftware?: string;
  productionLineId?: number;
  groupId?: number;
  registerWay?: string;
  wifiBitRate?: number;
  wifiFreq?: number;
  wifiIp?: string;
  wifiKeyMgmt?: string;
  wifiMac?: string;
  wifiSsid?: string;
  wifiState?: string;
  wifiLinkQuality?: string;
  wifiSignalLevel?: string;
  gatewayMac?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 创建设备时的可选属性
export interface DeviceCreationAttributes
  extends Optional<DeviceAttributes, 'id' | 'code' | 'sn' | 'mac' | 'ip' | 'remark' | 'controlModel' | 'deviceModelId' | 'vendor' | 'headSpace' | 'headNum' | 'headNeedleNum' | 'formularHeadSpace' | 'formularHeadNum' | 'formularLength' | 'displaySoftware' | 'controlSoftware' | 'productionLineId' | 'groupId' | 'registerWay' | 'wifiBitRate' | 'wifiFreq' | 'wifiIp' | 'wifiKeyMgmt' | 'wifiMac' | 'wifiSsid' | 'wifiState' | 'wifiLinkQuality' | 'wifiSignalLevel' | 'gatewayMac' | 'createdAt' | 'updatedAt'> {}

// 设备模型类
export class Device extends Model<DeviceAttributes, DeviceCreationAttributes> implements DeviceAttributes {
  public id!: number;
  public enterpriseId!: number;
  public code?: string;
  public sn?: string;
  public mac?: string;
  public ip?: string;
  public name!: string;
  public remark?: string;
  public controlModel?: string;
  public deviceModelId?: number;
  public vendor?: string;
  public headSpace?: number;
  public headNum?: number;
  public headNeedleNum?: number;
  public formularHeadSpace?: number;
  public formularHeadNum?: number;
  public formularLength?: number;
  public displaySoftware?: string;
  public controlSoftware?: string;
  public productionLineId?: number;
  public groupId?: number;
  public registerWay?: string;
  public wifiBitRate?: number;
  public wifiFreq?: number;
  public wifiIp?: string;
  public wifiKeyMgmt?: string;
  public wifiMac?: string;
  public wifiSsid?: string;
  public wifiState?: string;
  public wifiLinkQuality?: string;
  public wifiSignalLevel?: string;
  public gatewayMac?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

// 初始化设备模型
Device.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '设备ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '机器编号',
    },
    sn: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '机器SN',
    },
    mac: {
      type: DataTypes.STRING(17),
      allowNull: true,
      comment: '机器Mac地址',
    },
    ip: {
      type: DataTypes.STRING(15),
      allowNull: true,
      comment: '机器IP',
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '机器名称',
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注',
    },
    controlModel: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'control_model',
      comment: '电控',
    },
    deviceModelId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'device_model_id',
      comment: '设备类型ID',
      references: {
        model: 'em_device_model',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    vendor: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '电控厂商',
    },
    headSpace: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'head_space',
      comment: '机头头距',
    },
    headNum: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'head_num',
      comment: '机头头数',
    },
    headNeedleNum: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'head_needle_num',
      comment: '机头针数',
    },
    formularHeadSpace: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'formular_head_space',
      comment: '计算头距',
    },
    formularHeadNum: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'formular_head_num',
      comment: '计算头数',
    },
    formularLength: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'formular_length',
      comment: '计算长度',
    },
    displaySoftware: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'display_software',
      comment: '显示软件',
    },
    controlSoftware: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'control_software',
      comment: '主控软件',
    },
    productionLineId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'production_line_id',
      comment: '车间产线ID',
      references: {
        model: 'em_tag',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    groupId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'group_id',
      comment: '机器分组ID',
      references: {
        model: 'em_tag',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    registerWay: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'register_way',
      comment: '添加方式',
    },
    wifiBitRate: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'wifi_bit_rate',
      comment: 'WiFi速率',
    },
    wifiFreq: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'wifi_freq',
      comment: '无线频率',
    },
    wifiIp: {
      type: DataTypes.STRING(15),
      allowNull: true,
      field: 'wifi_ip',
      comment: 'WiFi的IP',
    },
    wifiKeyMgmt: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'wifi_key_mgmt',
      comment: 'WiFi加密方式',
    },
    wifiMac: {
      type: DataTypes.STRING(17),
      allowNull: true,
      field: 'wifi_mac',
      comment: 'WiFi的mac',
    },
    wifiSsid: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'wifi_ssid',
      comment: 'WiFi名称',
    },
    wifiState: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'wifi_state',
      comment: 'WiFi连接状态',
    },
    wifiLinkQuality: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'wifi_link_quality',
      comment: 'WiFi信号质量',
    },
    wifiSignalLevel: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'wifi_signal_level',
      comment: 'WiFi信号强度',
    },
    gatewayMac: {
      type: DataTypes.STRING(17),
      allowNull: true,
      field: 'gateway_mac',
      comment: '网关Mac',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_device',
    modelName: 'Device',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'device_enterprise_id_idx',
      },
      {
        fields: ['code'],
        name: 'device_code_idx',
      },
      {
        fields: ['sn'],
        name: 'device_sn_idx',
      },
      {
        fields: ['mac'],
        name: 'device_mac_idx',
      },
      {
        fields: ['name'],
        name: 'device_name_idx',
      },
      {
        fields: ['vendor'],
        name: 'device_vendor_idx',
      },
      {
        fields: ['wifi_state'],
        name: 'device_wifi_state_idx',
      },
      {
        fields: ['production_line_id'],
        name: 'device_production_line_id_idx',
      },
      {
        fields: ['group_id'],
        name: 'device_group_id_idx',
      },
      // 复合索引 - 优化多条件查询
      {
        fields: ['enterprise_id', 'device_model_id'],
        name: 'device_enterprise_model_idx',
      },
      {
        fields: ['enterprise_id', 'production_line_id'],
        name: 'device_enterprise_production_line_idx',
      },
      {
        fields: ['enterprise_id', 'group_id'],
        name: 'device_enterprise_group_idx',
      },
      {
        fields: ['enterprise_id', 'device_model_id', 'production_line_id'],
        name: 'device_enterprise_model_production_idx',
      },
    ],
  }
);

export default Device;
