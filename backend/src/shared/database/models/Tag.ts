/**
 * 标签模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 17:00:00 +08:00; Reason: 创建标签管理模块, 支持多种类型的标签管理; Principle_Applied: 数据模型设计;}}
 */

import { DataTypes, Model, Optional, Op } from 'sequelize';
import { sequelize } from '../index';

// 标签类型枚举
export enum TagType {
  WORKSHOP_LINE = 1,    // 车间产线 - 支持多级层级
  MACHINE_GROUP = 2,    // 机器分组 - 同级
  PATTERN_GROUP = 3,    // 花样分组 - 同级
  PATTERN_UNIT = 4      // 花样计量单位 - 同级
}

// 标签状态枚举
export enum TagStatus {
  DISABLED = 0,         // 禁用
  ENABLED = 1           // 正常
}

// 标签属性接口
export interface TagAttributes {
  id: number;
  enterpriseId: number;
  pid: number | null;
  level: number;
  name: string;
  path: string;
  status: TagStatus;
  type: TagType;
  createdAt: Date;
  updatedAt: Date;
}

// 创建标签时的可选属性
export interface TagCreationAttributes extends Optional<TagAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

/**
 * 标签模型类
 */
export class Tag extends Model<TagAttributes, TagCreationAttributes> implements TagAttributes {
  public id!: number;
  public enterpriseId!: number;
  public pid!: number | null;
  public level!: number;
  public name!: string;
  public path!: string;
  public status!: TagStatus;
  public type!: TagType;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public enterprise?: any;
  public parent?: Tag;
  public children?: Tag[];

  /**
   * 获取标签类型名称
   */
  public getTypeName(): string {
    const typeNames = {
      [TagType.WORKSHOP_LINE]: '车间产线',
      [TagType.MACHINE_GROUP]: '机器分组',
      [TagType.PATTERN_GROUP]: '花样分组',
      [TagType.PATTERN_UNIT]: '花样计量单位'
    };
    return typeNames[this.type] || '未知类型';
  }

  /**
   * 获取状态名称
   */
  public getStatusName(): string {
    return this.status === TagStatus.ENABLED ? '正常' : '禁用';
  }

  /**
   * 检查是否支持层级结构
   */
  public supportHierarchy(): boolean {
    return this.type === TagType.WORKSHOP_LINE;
  }

  /**
   * 构建路径
   */
  public async buildPath(): Promise<string> {
    if (!this.pid) {
      return this.id.toString();
    }

    const parent = await Tag.findByPk(this.pid);
    if (!parent) {
      return this.id.toString();
    }

    return `${parent.path}/${this.id}`;
  }

  /**
   * 获取所有祖先节点
   */
  public async getAncestors(): Promise<Tag[]> {
    const ancestors: Tag[] = [];
    let current: Tag | null = this;

    while (current && current.pid) {
      const parent: Tag | null = await Tag.findByPk(current.pid);
      if (parent) {
        ancestors.unshift(parent);
        current = parent;
      } else {
        break;
      }
    }

    return ancestors;
  }

  /**
   * 获取所有后代节点
   */
  public async getDescendants(): Promise<Tag[]> {
    const descendants: Tag[] = [];
    
    const children = await Tag.findAll({
      where: {
        enterpriseId: this.enterpriseId,
        type: this.type,
        path: {
          [Op.like]: `${this.path}/%`
        }
      },
      order: [['path', 'ASC']]
    });

    return children;
  }
}

// 定义模型
Tag.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '标签ID'
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID'
    },
    pid: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: null,
      comment: '父级ID'
    },
    level: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '层级深度'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '标签名称'
    },
    path: {
      type: DataTypes.STRING(500),
      allowNull: false,
      comment: '层级路径'
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: TagStatus.ENABLED,
      comment: '状态：1-正常，0-禁用'
    },
    type: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '类型：1-车间产线，2-机器分组，3-花样分组，4-花样计量单位'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间'
    }
  },
  {
    sequelize,
    tableName: 'em_tag',
    modelName: 'Tag',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    indexes: [
      {
        name: 'idx_tag_enterprise_type',
        fields: ['enterprise_id', 'type']
      },
      {
        name: 'idx_tag_pid',
        fields: ['pid']
      },
      {
        name: 'idx_tag_path',
        fields: ['path']
      },
      {
        name: 'idx_tag_status',
        fields: ['status']
      }
    ],
    comment: '标签表'
  }
);

export default Tag;
