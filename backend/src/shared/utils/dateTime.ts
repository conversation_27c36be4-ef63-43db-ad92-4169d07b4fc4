/**
 * 日期时间格式化工具
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 18:45:00 +08:00; Reason: 统一时间格式化处理, 服务端统一格式化为 yyyy-MM-dd HH:mm:ss; Principle_Applied: DRY原则;}}
 */

/**
 * 格式化日期时间为 yyyy-MM-dd HH:mm:ss 格式
 * @param date 日期对象、日期字符串或时间戳
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: Date | string | number | null | undefined): string => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 格式化日期为 yyyy-MM-dd 格式
 * @param date 日期对象、日期字符串或时间戳
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: Date | string | number | null | undefined): string => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

/**
 * 格式化时间为 HH:mm:ss 格式
 * @param date 日期对象、日期字符串或时间戳
 * @returns 格式化后的时间字符串
 */
export const formatTime = (date: Date | string | number | null | undefined): string => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');
  
  return `${hours}:${minutes}:${seconds}`;
};
