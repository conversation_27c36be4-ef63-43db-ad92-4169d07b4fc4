/**
 * 错误处理工具
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 21:00:00 +08:00; Reason: Task-006 用户管理模块, 创建错误处理工具; Principle_Applied: 错误处理标准化;}}
 */

export class ApiError extends Error {
  public statusCode: number;
  public errorCode: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, errorCode: string = 'INTERNAL_ERROR', details?: any) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;

    // 确保堆栈跟踪正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError);
    }
  }
}

/**
 * 创建API错误
 */
export function createApiError(
  message: string,
  statusCode: number = 500,
  errorCode: string = 'INTERNAL_ERROR',
  details?: any
): ApiError {
  return new ApiError(message, statusCode, errorCode, details);
}

/**
 * 检查是否为API错误
 */
export function isApiError(error: any): error is ApiError {
  return error instanceof ApiError;
}
