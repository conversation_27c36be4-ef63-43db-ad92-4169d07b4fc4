/**
 * 异步处理中间件
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 15:45:00 +08:00; Reason: 修复后端启动问题, 创建缺失的异步处理中间件; Principle_Applied: 错误处理;}}
 */

import { Request, Response, NextFunction } from 'express';

/**
 * 异步处理包装器
 * 用于包装异步路由处理器，自动捕获异常并传递给错误处理中间件
 */
export const asyncHandler = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 异步中间件包装器
 * 用于包装异步中间件，自动捕获异常
 */
export const asyncMiddleware = (fn: (req: Request, res: Response, next: NextFunction) => Promise<void>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

export default asyncHandler;
