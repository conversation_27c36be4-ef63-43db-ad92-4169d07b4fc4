/**
 * 应用配置文件
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建应用配置; Principle_Applied: 配置集中管理;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 11:51:06 +08:00; Reason: Task-003 微信服务层开发, 添加微信配置; Principle_Applied: 配置集中管理;}}
 */

import dotenv from 'dotenv';

dotenv.config();

// CORS源配置函数
function getCorsOrigin(): string | string[] | boolean | ((origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => void) {
  const nodeEnv = process.env['NODE_ENV'] || 'development';

  if (nodeEnv === 'development') {
    // 开发环境：允许多个本地端口和同源请求
    const allowedOrigins = [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:5176',
      'http://127.0.0.1:5173',
      'http://127.0.0.1:5174',
      'http://127.0.0.1:5175',
      'http://127.0.0.1:5176'
    ];

    // 如果环境变量指定了特定源，添加到允许列表
    if (process.env['CORS_ORIGIN']) {
      const customOrigins = process.env['CORS_ORIGIN'].split(',').map(origin => origin.trim());
      allowedOrigins.push(...customOrigins);
    }

    return (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
      // 允许同源请求（origin为undefined）
      if (!origin) {
        return callback(null, true);
      }

      // 检查是否在允许列表中
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }

      // 开发环境下，允许localhost和127.0.0.1的任何端口
      const localhostRegex = /^https?:\/\/(localhost|127\.0\.0\.1):\d+$/;
      if (localhostRegex.test(origin)) {
        return callback(null, true);
      }

      return callback(new Error('CORS策略不允许此源'), false);
    };
  } else {
    // 生产环境：严格按照环境变量配置
    return process.env['CORS_ORIGIN'] || false;
  }
}

interface AppConfig {
  app: {
    nodeEnv: string;
    port: number;
    apiPrefix: string;
  };
  database: {
    host: string;
    port: number;
    name: string;
    username: string;
    password: string;
    dialect: string;
    tablePrefix: string;
    pool: {
      max: number;
      min: number;
      acquire: number;
      idle: number;
    };
  };
  jwt: {
    secret: string;
    expiresIn: string;
    refreshSecret: string;
    refreshExpiresIn: string;
  };
  bcrypt: {
    rounds: number;
  };
  upload: {
    path: string;
    maxFileSize: number;
    allowedTypes: string[];
  };
  logging: {
    level: string;
    filePath: string;
  };
  rateLimit: {
    windowMs: number;
    maxRequests: number;
    loginWindowMs: number;
    loginMaxRequests: number;
  };
  cors: {
    origin: string | string[] | boolean | ((origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => void);
    credentials: boolean;
  };
  security: {
    helmetEnabled: boolean;
    compressionEnabled: boolean;
  };
  wechat: {
    appId: string;
    appSecret: string;
    redirectUri: string;
  };
}

export const config: AppConfig = {
  app: {
    nodeEnv: process.env['NODE_ENV'] || 'development',
    port: parseInt(process.env['PORT'] || '3002', 10),
    apiPrefix: process.env['API_PREFIX'] || '/api/v1',
  },
  database: {
    host: process.env['DB_HOST'] || 'localhost',
    port: parseInt(process.env['DB_PORT'] || '5432', 10),
    name: process.env['DB_NAME'] || 'embroidery_management',
    username: process.env['DB_USER'] || process.env['DB_USERNAME'] || 'postgres',
    password: process.env['DB_PASSWORD'] || '',
    dialect: process.env['DB_DIALECT'] || 'postgres',
    tablePrefix: process.env['DB_TABLE_PREFIX'] || 'em_',
    pool: {
      max: parseInt(process.env['DB_POOL_MAX'] || '20', 10),
      min: parseInt(process.env['DB_POOL_MIN'] || '5', 10),
      acquire: parseInt(process.env['DB_POOL_ACQUIRE'] || '30000', 10),
      idle: parseInt(process.env['DB_POOL_IDLE'] || '10000', 10),
    },
  },
  jwt: {
    secret: process.env['JWT_SECRET'] || 'your_super_secret_jwt_key_here',
    expiresIn: process.env['JWT_EXPIRES_IN'] || '24h',
    refreshSecret: process.env['JWT_REFRESH_SECRET'] || 'your_refresh_token_secret_here',
    refreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d',
  },
  bcrypt: {
    rounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),
  },
  upload: {
    path: process.env['UPLOAD_PATH'] || 'uploads',
    maxFileSize: parseInt(process.env['MAX_FILE_SIZE'] || '5242880', 10), // 5MB
    allowedTypes: (process.env['ALLOWED_FILE_TYPES'] || 'jpg,jpeg,png,gif').split(','),
  },
  logging: {
    level: process.env['LOG_LEVEL'] || 'info',
    filePath: process.env['LOG_FILE_PATH'] || 'logs',
  },
  rateLimit: {
    windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000', 10), // 15分钟
    maxRequests: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100', 10),
    loginWindowMs: parseInt(process.env['RATE_LIMIT_LOGIN_WINDOW_MS'] || '900000', 10), // 15分钟
    loginMaxRequests: parseInt(process.env['RATE_LIMIT_LOGIN_MAX_REQUESTS'] || '100', 10),
  },
  cors: {
    origin: getCorsOrigin(),
    credentials: process.env['CORS_CREDENTIALS'] === 'true',
  },
  security: {
    helmetEnabled: process.env['HELMET_ENABLED'] !== 'false',
    compressionEnabled: process.env['COMPRESSION_ENABLED'] !== 'false',
  },
  wechat: {
    appId: process.env['WECHAT_APP_ID'] || '',
    appSecret: process.env['WECHAT_APP_SECRET'] || '',
    redirectUri: process.env['WECHAT_REDIRECT_URI'] || 'http://localhost:3002/api/v1/auth/wechat/callback',
  },
};

// 验证必需的环境变量
const requiredEnvVars = [
  'JWT_SECRET',
  'DB_PASSWORD',
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`缺少必需的环境变量: ${missingEnvVars.join(', ')}`);
}
