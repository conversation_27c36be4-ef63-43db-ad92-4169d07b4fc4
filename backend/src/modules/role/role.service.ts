/**
 * 角色服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 11:37:00 +08:00; Reason: 创建角色管理服务用于用户管理中的角色分配; Principle_Applied: 业务逻辑分离;}}
 */

import { Role } from '@/shared/database/models/Role';
import { Permission } from '@/shared/database/models/Permission';
import { User } from '@/shared/database/models/User';
import { createApiError } from '@/shared/utils/error';
import { Op, Transaction } from 'sequelize';
import { sequelize } from '@/shared/database';

export interface RoleListItem {
  id: number;
  name: string;
  code: string;
  description: string;
  isSystem: boolean;
  enterpriseId: number | null;
  permissions?: {
    id: number;
    name: string;
    code: string;
    module: string;
  }[];
}

export interface CreateRoleData {
  name: string;
  code: string;
  description?: string;
  permissionIds?: number[];
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  permissionIds?: number[];
}

export interface PermissionListItem {
  id: number;
  name: string;
  code: string;
  module: string;
  description?: string;
}

export class RoleService {
  /**
   * 获取角色列表
   */
  public async getRoleList(userId: number, enterpriseId?: number): Promise<RoleListItem[]> {
    // 获取当前用户信息，包含角色信息
    const currentUser = await User.findByPk(userId, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'code', 'isSystem'],
          through: { attributes: [] },
        },
      ],
    });

    if (!currentUser) {
      throw createApiError('用户不存在', 404);
    }

    // 获取当前用户的角色代码
    const userRoleCodes = currentUser.roles?.map(role => role.code) || [];
    const isSuperAdmin = userRoleCodes.includes('SUPER_ADMIN');
    const isEnterpriseAdmin = userRoleCodes.includes('ENTERPRISE_ADMIN');

    let whereCondition: any = {};

    // 如果指定了企业ID，则只获取该企业的角色
    if (enterpriseId) {
      whereCondition.enterpriseId = enterpriseId;
    } else if (currentUser.enterpriseId) {
      // 如果用户属于某个企业，则只获取该企业的角色和系统角色
      whereCondition = {
        [Op.or]: [
          { enterpriseId: currentUser.enterpriseId },
          { isSystem: true }
        ]
      };
    } else {
      // 超级管理员可以看到所有角色
      // 不添加任何条件
    }

    const roles = await Role.findAll({
      where: whereCondition,
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'code', 'module'],
          through: { attributes: [] }, // 不包含中间表字段
        },
      ],
      order: [
        ['isSystem', 'DESC'], // 系统角色在前
        ['id', 'ASC'],
      ],
    });

    // 根据当前用户角色过滤可选择的角色
    const filteredRoles = roles.filter(role => {
      // 超级管理员可以分配所有角色
      if (isSuperAdmin) {
        return true;
      }

      // 企业管理员不能分配超级管理员和企业管理员角色
      if (isEnterpriseAdmin) {
        return role.code !== 'SUPER_ADMIN' && role.code !== 'ENTERPRISE_ADMIN';
      }

      // 普通用户不能分配任何系统角色
      return !role.isSystem;
    });

    return filteredRoles.map(role => ({
      id: role.id,
      name: role.name,
      code: role.code,
      description: role.description,
      isSystem: role.isSystem,
      enterpriseId: role.enterpriseId,
      permissions: role.permissions?.map(permission => ({
        id: permission.id,
        name: permission.name,
        code: permission.code,
        module: permission.module,
      })),
    }));
  }

  /**
   * 根据ID获取角色详情
   */
  public async getRoleById(userId: number, roleId: number): Promise<RoleListItem> {
    // 获取当前用户信息
    const currentUser = await User.findByPk(userId, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'code', 'isSystem'],
          through: { attributes: [] },
        },
      ],
    });

    if (!currentUser) {
      throw createApiError('用户不存在', 404);
    }

    // 检查用户权限
    const userRoles = currentUser.roles || [];
    const isSuperAdmin = userRoles.some(role => role.code === 'SUPER_ADMIN');
    const isEnterpriseAdmin = userRoles.some(role => role.code === 'ENTERPRISE_ADMIN');

    // 查找角色
    const role = await Role.findByPk(roleId, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'code', 'module'],
          through: { attributes: [] },
        },
      ],
    });

    if (!role) {
      throw createApiError('角色不存在', 404);
    }

    // 权限检查
    if (!isSuperAdmin) {
      if (role.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('无权访问该角色', 403);
      }

      if (isEnterpriseAdmin && (role.code === 'SUPER_ADMIN' || role.code === 'ENTERPRISE_ADMIN')) {
        throw createApiError('无权访问该角色', 403);
      }
    }

    return {
      id: role.id,
      name: role.name,
      code: role.code,
      description: role.description,
      isSystem: role.isSystem,
      enterpriseId: role.enterpriseId,
      permissions: role.permissions?.map(permission => ({
        id: permission.id,
        name: permission.name,
        code: permission.code,
        module: permission.module,
      })),
    };
  }

  /**
   * 创建角色
   */
  public async createRole(userId: number, data: CreateRoleData): Promise<RoleListItem> {
    // 获取当前用户信息
    const currentUser = await User.findByPk(userId, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'code', 'isSystem'],
          through: { attributes: [] },
        },
      ],
    });

    if (!currentUser) {
      throw createApiError('用户不存在', 404);
    }

    // 检查用户权限
    const userRoles = currentUser.roles || [];
    const isSuperAdmin = userRoles.some(role => role.code === 'SUPER_ADMIN');

    if (!isSuperAdmin && !currentUser.enterpriseId) {
      throw createApiError('无权创建角色', 403);
    }

    // 检查角色编码是否已存在
    const existingRole = await Role.findOne({
      where: {
        code: data.code,
        enterpriseId: isSuperAdmin ? null : currentUser.enterpriseId,
      },
    });

    if (existingRole) {
      throw createApiError('角色编码已存在', 400);
    }

    const transaction = await sequelize.transaction();

    try {
      // 创建角色
      const role = await Role.create({
        name: data.name,
        code: data.code,
        description: data.description,
        enterpriseId: isSuperAdmin ? null : currentUser.enterpriseId,
        isSystem: false, // 用户创建的角色都不是系统角色
      }, { transaction });

      // 分配权限
      if (data.permissionIds && data.permissionIds.length > 0) {
        const permissions = await Permission.findAll({
          where: {
            id: data.permissionIds,
          },
          transaction,
        });

        await role.setPermissions(permissions, { transaction });
      }

      await transaction.commit();

      // 重新查询角色详情
      return await this.getRoleById(userId, role.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 更新角色
   */
  public async updateRole(userId: number, roleId: number, data: UpdateRoleData): Promise<RoleListItem> {
    // 获取当前用户信息
    const currentUser = await User.findByPk(userId, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'code', 'isSystem'],
          through: { attributes: [] },
        },
      ],
    });

    if (!currentUser) {
      throw createApiError('用户不存在', 404);
    }

    // 检查用户权限
    const userRoles = currentUser.roles || [];
    const isSuperAdmin = userRoles.some(role => role.code === 'SUPER_ADMIN');
    const isEnterpriseAdmin = userRoles.some(role => role.code === 'ENTERPRISE_ADMIN');

    // 查找角色
    const role = await Role.findByPk(roleId);

    if (!role) {
      throw createApiError('角色不存在', 404);
    }

    // 权限检查
    if (role.isSystem) {
      throw createApiError('系统角色不能修改', 403);
    }

    if (!isSuperAdmin) {
      if (role.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('无权修改该角色', 403);
      }
    }

    const transaction = await sequelize.transaction();

    try {
      // 更新角色基本信息
      if (data.name !== undefined) {
        role.name = data.name;
      }
      if (data.description !== undefined) {
        role.description = data.description;
      }

      await role.save({ transaction });

      // 更新权限
      if (data.permissionIds !== undefined) {
        const permissions = await Permission.findAll({
          where: {
            id: data.permissionIds,
          },
          transaction,
        });

        await role.setPermissions(permissions, { transaction });
      }

      await transaction.commit();

      // 重新查询角色详情
      return await this.getRoleById(userId, role.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 删除角色
   */
  public async deleteRole(userId: number, roleId: number): Promise<void> {
    // 获取当前用户信息
    const currentUser = await User.findByPk(userId, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'code', 'isSystem'],
          through: { attributes: [] },
        },
      ],
    });

    if (!currentUser) {
      throw createApiError('用户不存在', 404);
    }

    // 检查用户权限
    const userRoles = currentUser.roles || [];
    const isSuperAdmin = userRoles.some(role => role.code === 'SUPER_ADMIN');

    // 查找角色
    const role = await Role.findByPk(roleId);

    if (!role) {
      throw createApiError('角色不存在', 404);
    }

    // 权限检查
    if (role.isSystem) {
      throw createApiError('系统角色不能删除', 403);
    }

    if (!isSuperAdmin) {
      if (role.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('无权删除该角色', 403);
      }
    }

    // 检查是否有用户使用该角色
    const userCount = await User.count({
      include: [
        {
          model: Role,
          as: 'roles',
          where: { id: roleId },
          through: { attributes: [] },
        },
      ],
    });

    if (userCount > 0) {
      throw createApiError(`该角色正在被 ${userCount} 个用户使用，无法删除`, 400);
    }

    // 删除角色
    await role.destroy();
  }

  /**
   * 更新角色权限
   */
  public async updateRolePermissions(userId: number, roleId: number, permissionIds: number[]): Promise<RoleListItem> {
    // 获取当前用户信息
    const currentUser = await User.findByPk(userId, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'code', 'isSystem'],
          through: { attributes: [] },
        },
      ],
    });

    if (!currentUser) {
      throw createApiError('用户不存在', 404);
    }

    // 检查用户权限
    const userRoles = currentUser.roles || [];
    const isSuperAdmin = userRoles.some(role => role.code === 'SUPER_ADMIN');

    // 查找角色
    const role = await Role.findByPk(roleId);

    if (!role) {
      throw createApiError('角色不存在', 404);
    }

    // 权限检查
    if (role.isSystem) {
      throw createApiError('系统角色权限不能修改', 403);
    }

    if (!isSuperAdmin) {
      if (role.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('无权修改该角色权限', 403);
      }
    }

    const transaction = await sequelize.transaction();

    try {
      // 查找权限
      const permissions = await Permission.findAll({
        where: {
          id: permissionIds,
        },
        transaction,
      });

      // 检查是否包含企业管理权限
      const hasEnterprisePermissions = permissions.some(permission => permission.module === 'enterprise');

      // 非超级管理员不能分配企业管理权限
      if (!isSuperAdmin && hasEnterprisePermissions) {
        throw createApiError('无权分配企业管理权限', 403);
      }

      // 更新角色权限
      await role.setPermissions(permissions, { transaction });

      await transaction.commit();

      // 重新查询角色详情
      return await this.getRoleById(userId, role.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 获取权限列表
   */
  public async getPermissionList(userId: number): Promise<PermissionListItem[]> {
    // 获取当前用户信息
    const currentUser = await User.findByPk(userId, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'code', 'isSystem'],
          through: { attributes: [] },
        },
      ],
    });

    if (!currentUser) {
      throw createApiError('用户不存在', 404);
    }

    // 检查用户权限
    const userRoles = currentUser.roles || [];
    const isSuperAdmin = userRoles.some(role => role.code === 'SUPER_ADMIN');

    // 构建查询条件
    const whereCondition: any = {};

    // 非超级管理员不能看到企业管理权限
    if (!isSuperAdmin) {
      whereCondition.module = {
        [Op.ne]: 'enterprise'
      };
    }

    const permissions = await Permission.findAll({
      where: whereCondition,
      attributes: ['id', 'name', 'code', 'module', 'description'],
      order: [
        ['module', 'ASC'],
        ['code', 'ASC'],
      ],
    });

    return permissions.map(permission => ({
      id: permission.id,
      name: permission.name,
      code: permission.code,
      module: permission.module,
      description: permission.description,
    }));
  }
}
