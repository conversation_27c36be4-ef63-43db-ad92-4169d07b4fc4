/**
 * 角色路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 11:37:00 +08:00; Reason: 创建角色管理路由用于用户管理中的角色分配; Principle_Applied: RESTful路由设计;}}
 */

import { Router } from 'express';
import { RoleController } from './role.controller';
import { authMiddleware, permissionMiddleware } from '@/shared/middleware/auth.middleware';

const router = Router();
const roleController = new RoleController();

// 所有角色路由都需要认证
router.use(authMiddleware);

/**
 * @route GET /api/v1/roles
 * @desc 获取角色列表
 * @access Private (需要 user:view 权限)
 */
router.get(
  '/',
  permissionMiddleware(['user:view']),
  roleController.getRoleListValidation,
  roleController.getRoleList
);

/**
 * @route GET /api/v1/roles/:id
 * @desc 获取角色详情
 * @access Private (需要 user:view 权限)
 */
router.get(
  '/:id',
  permissionMiddleware(['user:view']),
  roleController.getRoleByIdValidation,
  roleController.getRoleById
);

/**
 * @route POST /api/v1/roles
 * @desc 创建角色
 * @access Private (需要 user:create 权限)
 */
router.post(
  '/',
  permissionMiddleware(['user:create']),
  roleController.createRoleValidation,
  roleController.createRole
);

/**
 * @route PUT /api/v1/roles/:id
 * @desc 更新角色
 * @access Private (需要 user:update 权限)
 */
router.put(
  '/:id',
  permissionMiddleware(['user:update']),
  roleController.updateRoleValidation,
  roleController.updateRole
);

/**
 * @route DELETE /api/v1/roles/:id
 * @desc 删除角色
 * @access Private (需要 user:delete 权限)
 */
router.delete(
  '/:id',
  permissionMiddleware(['user:delete']),
  roleController.deleteRoleValidation,
  roleController.deleteRole
);

/**
 * @route PUT /api/v1/roles/:id/permissions
 * @desc 更新角色权限
 * @access Private (需要 user:update 权限)
 */
router.put(
  '/:id/permissions',
  permissionMiddleware(['user:update']),
  roleController.updateRolePermissionsValidation,
  roleController.updateRolePermissions
);

/**
 * @route GET /api/v1/permissions
 * @desc 获取权限列表
 * @access Private (需要 user:view 权限)
 */
router.get(
  '/permissions/list',
  permissionMiddleware(['user:view']),
  roleController.getPermissionListValidation,
  roleController.getPermissionList
);

export default router;
