/**
 * 角色控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 11:37:00 +08:00; Reason: 创建角色管理API用于用户管理中的角色分配; Principle_Applied: RESTful API设计;}}
 */

import { Request, Response, NextFunction } from 'express';
import { validationResult, query, param, body } from 'express-validator';
import { RoleService } from './role.service';
import { createApiError } from '@/shared/utils/error';
import { logger } from '@/shared/utils/logger';

export class RoleController {
  private roleService: RoleService;

  constructor() {
    this.roleService = new RoleService();
  }

  /**
   * 获取角色列表验证规则
   */
  public getRoleListValidation = [
    query('enterpriseId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('企业ID必须是大于0的整数'),
  ];

  /**
   * 获取角色列表
   */
  public getRoleList = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const enterpriseId = req.query.enterpriseId ? parseInt(req.query.enterpriseId as string) : undefined;
      const roles = await this.roleService.getRoleList(req.user.userId, enterpriseId);

      logger.info('获取角色列表成功', {
        userId: req.user.userId,
        enterpriseId,
        roleCount: roles.length,
      });

      res.json({
        code: 200,
        message: '获取角色列表成功',
        data: roles,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取角色详情验证规则
   */
  public getRoleByIdValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('角色ID必须是大于0的整数'),
  ];

  /**
   * 获取角色详情
   */
  public getRoleById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const roleId = parseInt(req.params.id);
      const role = await this.roleService.getRoleById(req.user.userId, roleId);

      logger.info('获取角色详情成功', {
        userId: req.user.userId,
        roleId,
      });

      res.json({
        code: 200,
        message: '获取角色详情成功',
        data: role,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 创建角色验证规则
   */
  public createRoleValidation = [
    body('name')
      .notEmpty()
      .withMessage('角色名称不能为空')
      .isLength({ min: 2, max: 50 })
      .withMessage('角色名称长度必须在2-50个字符之间'),
    body('code')
      .notEmpty()
      .withMessage('角色编码不能为空')
      .matches(/^[A-Z_][A-Z0-9_]*$/)
      .withMessage('角色编码只能包含大写字母、数字和下划线，且必须以字母或下划线开头'),
    body('description')
      .optional()
      .isString()
      .withMessage('角色描述必须是字符串'),
    body('permissionIds')
      .optional()
      .isArray()
      .withMessage('权限ID列表必须是数组')
      .custom((value) => {
        if (Array.isArray(value)) {
          return value.every(id => Number.isInteger(id) && id > 0);
        }
        return true;
      })
      .withMessage('权限ID必须是大于0的整数'),
  ];

  /**
   * 创建角色
   */
  public createRole = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const role = await this.roleService.createRole(req.user.userId, req.body);

      logger.info('创建角色成功', {
        userId: req.user.userId,
        roleId: role.id,
        roleName: role.name,
      });

      res.json({
        code: 200,
        message: '创建角色成功',
        data: role,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 更新角色验证规则
   */
  public updateRoleValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('角色ID必须是大于0的整数'),
    body('name')
      .optional()
      .notEmpty()
      .withMessage('角色名称不能为空')
      .isLength({ min: 2, max: 50 })
      .withMessage('角色名称长度必须在2-50个字符之间'),
    body('description')
      .optional()
      .isString()
      .withMessage('角色描述必须是字符串'),
    body('permissionIds')
      .optional()
      .isArray()
      .withMessage('权限ID列表必须是数组')
      .custom((value) => {
        if (Array.isArray(value)) {
          return value.every(id => Number.isInteger(id) && id > 0);
        }
        return true;
      })
      .withMessage('权限ID必须是大于0的整数'),
  ];

  /**
   * 更新角色
   */
  public updateRole = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const roleId = parseInt(req.params.id);
      const role = await this.roleService.updateRole(req.user.userId, roleId, req.body);

      logger.info('更新角色成功', {
        userId: req.user.userId,
        roleId,
      });

      res.json({
        code: 200,
        message: '更新角色成功',
        data: role,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 删除角色验证规则
   */
  public deleteRoleValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('角色ID必须是大于0的整数'),
  ];

  /**
   * 删除角色
   */
  public deleteRole = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const roleId = parseInt(req.params.id);
      await this.roleService.deleteRole(req.user.userId, roleId);

      logger.info('删除角色成功', {
        userId: req.user.userId,
        roleId,
      });

      res.json({
        code: 200,
        message: '删除角色成功',
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 更新角色权限验证规则
   */
  public updateRolePermissionsValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('角色ID必须是大于0的整数'),
    body('permissionIds')
      .isArray()
      .withMessage('权限ID列表必须是数组')
      .custom((value) => {
        if (Array.isArray(value)) {
          return value.every(id => Number.isInteger(id) && id > 0);
        }
        return false;
      })
      .withMessage('权限ID必须是大于0的整数'),
  ];

  /**
   * 更新角色权限
   */
  public updateRolePermissions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const roleId = parseInt(req.params.id);
      const { permissionIds } = req.body;
      const role = await this.roleService.updateRolePermissions(req.user.userId, roleId, permissionIds);

      logger.info('更新角色权限成功', {
        userId: req.user.userId,
        roleId,
        permissionCount: permissionIds.length,
      });

      res.json({
        code: 200,
        message: '更新角色权限成功',
        data: role,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取权限列表验证规则
   */
  public getPermissionListValidation = [];

  /**
   * 获取权限列表
   */
  public getPermissionList = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const permissions = await this.roleService.getPermissionList(req.user.userId);

      logger.info('获取权限列表成功', {
        userId: req.user.userId,
        permissionCount: permissions.length,
      });

      res.json({
        code: 200,
        message: '获取权限列表成功',
        data: permissions,
      });
    } catch (error) {
      next(error);
    }
  };
}
