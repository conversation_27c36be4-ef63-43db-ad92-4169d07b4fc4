/**
 * 企业控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 14:30:00 +08:00; Reason: Task-006 企业管理模块, 创建企业控制器; Principle_Applied: MVC架构设计;}}
 */

import { Request, Response, NextFunction } from 'express';
import { query, param, body, validationResult } from 'express-validator';
import { EnterpriseService } from './enterprise.service';
import { createApiError } from '@/shared/utils/error';

export class EnterpriseController {
  private enterpriseService: EnterpriseService;

  constructor() {
    this.enterpriseService = new EnterpriseService();
  }

  /**
   * 获取企业列表验证规则
   */
  public getEnterpriseListValidation = [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须是1-100之间的整数'),
    query('keyword')
      .optional()
      .isString()
      .trim()
      .withMessage('关键词必须是字符串'),
    query('status')
      .optional()
      .isIn([0, 1])
      .withMessage('状态值必须是0或1'),
  ];

  /**
   * 获取企业列表
   */
  public getEnterpriseList = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const query = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 20,
        keyword: req.query.keyword as string,
        status: req.query.status ? parseInt(req.query.status as string) : undefined,
      };

      const result = await this.enterpriseService.getEnterpriseList(query, req.user.userId);

      res.json({
        code: 200,
        message: '获取企业列表成功',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取企业详情验证规则
   */
  public getEnterpriseByIdValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('企业ID必须是大于0的整数'),
  ];

  /**
   * 获取企业详情
   */
  public getEnterpriseById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      const enterprise = await this.enterpriseService.getEnterpriseById(id, req.user.userId);

      res.json({
        code: 200,
        message: '获取企业详情成功',
        data: enterprise,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 创建企业验证规则
   */
  public createEnterpriseValidation = [
    body('name')
      .notEmpty()
      .withMessage('企业名称不能为空')
      .isLength({ min: 2, max: 100 })
      .withMessage('企业名称长度必须在2-100个字符之间'),
    body('code')
      .notEmpty()
      .withMessage('企业编码不能为空')
      .matches(/^[A-Z0-9_]+$/)
      .withMessage('企业编码只能包含大写字母、数字和下划线')
      .isLength({ min: 2, max: 50 })
      .withMessage('企业编码长度必须在2-50个字符之间'),
    body('logoUrl')
      .optional()
      .isURL()
      .withMessage('Logo URL格式不正确'),
    body('description')
      .optional()
      .isString()
      .withMessage('企业描述必须是字符串'),
    body('status')
      .optional()
      .isIn([0, 1])
      .withMessage('状态值必须是0或1'),
    // 管理员账号信息验证
    body('adminUsername')
      .notEmpty()
      .withMessage('管理员用户名不能为空')
      .isLength({ min: 3, max: 50 })
      .withMessage('管理员用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('管理员用户名只能包含字母、数字和下划线'),
    body('adminPassword')
      .notEmpty()
      .withMessage('管理员密码不能为空')
      .isLength({ min: 6, max: 50 })
      .withMessage('管理员密码长度必须在6-50个字符之间'),
    body('adminRealName')
      .notEmpty()
      .withMessage('管理员真实姓名不能为空')
      .isLength({ min: 2, max: 50 })
      .withMessage('管理员真实姓名长度必须在2-50个字符之间'),
    body('adminEmail')
      .optional()
      .isEmail()
      .withMessage('管理员邮箱格式不正确'),
    body('adminPhone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('管理员手机号格式不正确'),
  ];

  /**
   * 创建企业
   */
  public createEnterprise = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const enterprise = await this.enterpriseService.createEnterprise(req.body, req.user.userId);

      res.status(201).json({
        code: 201,
        message: '创建企业成功',
        data: enterprise,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 更新企业验证规则
   */
  public updateEnterpriseValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('企业ID必须是大于0的整数'),
    body('name')
      .optional()
      .notEmpty()
      .withMessage('企业名称不能为空')
      .isLength({ min: 2, max: 100 })
      .withMessage('企业名称长度必须在2-100个字符之间'),
    body('code')
      .optional()
      .notEmpty()
      .withMessage('企业编码不能为空')
      .matches(/^[A-Z0-9_]+$/)
      .withMessage('企业编码只能包含大写字母、数字和下划线')
      .isLength({ min: 2, max: 50 })
      .withMessage('企业编码长度必须在2-50个字符之间'),
    body('logoUrl')
      .optional()
      .isURL()
      .withMessage('Logo URL格式不正确'),
    body('description')
      .optional()
      .isString()
      .withMessage('企业描述必须是字符串'),
    body('status')
      .optional()
      .isIn([0, 1])
      .withMessage('状态值必须是0或1'),
  ];

  /**
   * 更新企业
   */
  public updateEnterprise = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      const enterprise = await this.enterpriseService.updateEnterprise(id, req.body, req.user.userId);

      res.json({
        code: 200,
        message: '更新企业成功',
        data: enterprise,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 删除企业验证规则
   */
  public deleteEnterpriseValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('企业ID必须是大于0的整数'),
  ];

  /**
   * 删除企业
   */
  public deleteEnterprise = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      await this.enterpriseService.deleteEnterprise(id, req.user.userId);

      res.json({
        code: 200,
        message: '删除企业成功',
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 重置企业管理员密码验证规则
   */
  public resetAdminPasswordValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('企业ID必须是大于0的整数'),
    body('newPassword')
      .notEmpty()
      .withMessage('新密码不能为空')
      .isLength({ min: 6, max: 50 })
      .withMessage('新密码长度必须在6-50个字符之间'),
  ];

  /**
   * 重置企业管理员密码
   */
  public resetAdminPassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const enterpriseId = parseInt(req.params.id);
      const { newPassword } = req.body;

      await this.enterpriseService.resetEnterpriseAdminPassword(enterpriseId, newPassword, req.user.userId);

      res.json({
        code: 200,
        message: '重置企业管理员密码成功',
      });
    } catch (error) {
      next(error);
    }
  };
}
