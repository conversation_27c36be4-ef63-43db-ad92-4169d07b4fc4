/**
 * 企业路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 14:30:00 +08:00; Reason: Task-006 企业管理模块, 创建企业路由; Principle_Applied: RESTful API设计;}}
 */

import { Router } from 'express';
import { EnterpriseController } from './enterprise.controller';
import { authMiddleware, permissionMiddleware, roleMiddleware } from '@/shared/middleware/auth.middleware';

const router = Router();
const enterpriseController = new EnterpriseController();

/**
 * @route   GET /enterprises
 * @desc    获取企业列表
 * @access  Private (需要 enterprise:view 权限)
 */
router.get(
  '/',
  authMiddleware,
  permissionMiddleware(['enterprise:view']),
  enterpriseController.getEnterpriseListValidation,
  enterpriseController.getEnterpriseList
);

/**
 * @route   GET /enterprises/:id
 * @desc    获取企业详情
 * @access  Private (需要 enterprise:view 权限)
 */
router.get(
  '/:id',
  authMiddleware,
  permissionMiddleware(['enterprise:view']),
  enterpriseController.getEnterpriseByIdValidation,
  enterpriseController.getEnterpriseById
);

/**
 * @route   POST /enterprises
 * @desc    创建企业
 * @access  Private (需要 enterprise:create 权限)
 */
router.post(
  '/',
  authMiddleware,
  permissionMiddleware(['enterprise:create']),
  enterpriseController.createEnterpriseValidation,
  enterpriseController.createEnterprise
);

/**
 * @route   PUT /enterprises/:id
 * @desc    更新企业信息
 * @access  Private (需要 enterprise:update 权限)
 */
router.put(
  '/:id',
  authMiddleware,
  permissionMiddleware(['enterprise:update']),
  enterpriseController.updateEnterpriseValidation,
  enterpriseController.updateEnterprise
);

/**
 * @route   DELETE /enterprises/:id
 * @desc    删除企业
 * @access  Private (需要 enterprise:delete 权限)
 */
router.delete(
  '/:id',
  authMiddleware,
  permissionMiddleware(['enterprise:delete']),
  enterpriseController.deleteEnterpriseValidation,
  enterpriseController.deleteEnterprise
);

/**
 * @route   POST /enterprises/:id/reset-admin-password
 * @desc    重置企业管理员密码
 * @access  Private (需要 enterprise:update 权限)
 */
router.post(
  '/:id/reset-admin-password',
  authMiddleware,
  permissionMiddleware(['enterprise:update']),
  enterpriseController.resetAdminPasswordValidation,
  enterpriseController.resetAdminPassword
);

export { router as enterpriseRoutes };
