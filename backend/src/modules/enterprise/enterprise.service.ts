/**
 * 企业服务层
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-29 14:30:00 +08:00; Reason: Task-006 企业管理模块, 创建企业服务层; Principle_Applied: 服务层架构设计;}}
 */

import { Op, Transaction } from 'sequelize';
import bcrypt from 'bcrypt';
import { sequelize } from '@/shared/database';
import { Enterprise, User, Department, Role, Permission, UserRole } from '@/shared/database/models';
import { createApiError } from '@/shared/utils/error';
import { logger } from '@/shared/utils/logger';
import { formatDateTime } from '@/shared/utils/dateTime';

// 企业列表查询参数
export interface EnterpriseListQuery {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: number;
}

// 企业列表响应
export interface EnterpriseListResponse {
  list: any[];
  total: number;
  page: number;
  pageSize: number;
}

// 创建企业请求
export interface CreateEnterpriseRequest {
  name: string;
  code: string;
  logoUrl?: string;
  description?: string;
  status?: number;
  // 企业管理员账号信息
  adminUsername: string;
  adminPassword: string;
  adminRealName: string;
  adminEmail?: string;
  adminPhone?: string;
}

// 更新企业请求
export interface UpdateEnterpriseRequest {
  name?: string;
  code?: string;
  logoUrl?: string;
  description?: string;
  status?: number;
}

export class EnterpriseService {
  /**
   * 获取企业列表（仅超级管理员）
   */
  async getEnterpriseList(query: EnterpriseListQuery, currentUserId: number): Promise<EnterpriseListResponse> {
    try {
      const {
        page = 1,
        pageSize = 20,
        keyword,
        status
      } = query;

      // 验证当前用户是否为超级管理员
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      if (!isSuperAdmin) {
        throw createApiError('权限不足，仅超级管理员可查看企业列表', 403, 'PERMISSION_DENIED');
      }

      // 构建查询条件
      const whereConditions: any = {};

      // 关键词搜索
      if (keyword) {
        whereConditions[Op.or] = [
          { name: { [Op.iLike]: `%${keyword}%` } },
          { code: { [Op.iLike]: `%${keyword}%` } },
          { description: { [Op.iLike]: `%${keyword}%` } },
        ];
      }

      // 状态筛选
      if (status !== undefined) {
        whereConditions.status = status;
      }

      // 分页参数
      const offset = (page - 1) * pageSize;
      const limit = pageSize;

      // 查询企业列表
      const { count, rows: enterprises } = await Enterprise.findAndCountAll({
        where: whereConditions,
        order: [['createdAt', 'DESC']],
        offset,
        limit,
      });

      // 获取每个企业的统计信息和管理员信息
      const enterpriseIds = enterprises.map(e => e.id);
      const [userCounts, departmentCounts, adminUsers] = await Promise.all([
        User.findAll({
          where: { enterpriseId: { [Op.in]: enterpriseIds } },
          attributes: [
            'enterpriseId',
            [sequelize.fn('COUNT', sequelize.col('id')), 'count']
          ],
          group: ['enterpriseId'],
          raw: true,
        }),
        Department.findAll({
          where: { enterpriseId: { [Op.in]: enterpriseIds } },
          attributes: [
            'enterpriseId',
            [sequelize.fn('COUNT', sequelize.col('id')), 'count']
          ],
          group: ['enterpriseId'],
          raw: true,
        }),
        // 查询企业管理员
        User.findAll({
          where: { enterpriseId: { [Op.in]: enterpriseIds } },
          include: [
            {
              model: Role,
              as: 'roles',
              where: { code: 'ENTERPRISE_ADMIN' },
              attributes: [],
              through: { attributes: [] }
            }
          ],
          attributes: ['id', 'username', 'realName', 'email', 'phone', 'enterpriseId'],
        }),
      ]);

      // 构建统计映射和管理员映射
      const userCountMap = new Map();
      const departmentCountMap = new Map();
      const adminMap = new Map();

      userCounts.forEach((item: any) => {
        userCountMap.set(item.enterpriseId, parseInt(item.count) || 0);
      });

      departmentCounts.forEach((item: any) => {
        departmentCountMap.set(item.enterpriseId, parseInt(item.count) || 0);
      });

      adminUsers.forEach((admin: any) => {
        adminMap.set(admin.enterpriseId, {
          id: admin.id,
          username: admin.username,
          realName: admin.realName,
          email: admin.email,
          phone: admin.phone,
        });
      });

      // 格式化响应数据
      const list = enterprises.map(enterprise => ({
        id: enterprise.id,
        name: enterprise.name,
        code: enterprise.code,
        logoUrl: enterprise.logoUrl,
        description: enterprise.description,
        status: enterprise.status,
        createdAt: formatDateTime(enterprise.createdAt),
        updatedAt: formatDateTime(enterprise.updatedAt),
        statistics: {
          userCount: userCountMap.get(enterprise.id) || 0,
          departmentCount: departmentCountMap.get(enterprise.id) || 0,
        },
        admin: adminMap.get(enterprise.id) || null,
      }));

      logger.info('获取企业列表成功', {
        userId: currentUserId,
        total: count,
        page,
        pageSize,
      });

      return {
        list,
        total: count,
        page,
        pageSize,
      };
    } catch (error) {
      logger.error('获取企业列表失败', error);
      throw error;
    }
  }

  /**
   * 获取企业详情
   */
  async getEnterpriseById(id: number, currentUserId: number): Promise<any> {
    try {
      // 验证当前用户权限
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      
      // 非超级管理员只能查看自己的企业
      if (!isSuperAdmin && currentUser.enterpriseId !== id) {
        throw createApiError('权限不足，只能查看自己所属的企业', 403, 'PERMISSION_DENIED');
      }

      const enterprise = await Enterprise.findByPk(id);
      
      if (!enterprise) {
        throw createApiError('企业不存在', 404, 'ENTERPRISE_NOT_FOUND');
      }

      // 获取企业统计信息
      const [userCount, departmentCount] = await Promise.all([
        User.count({ where: { enterpriseId: id } }),
        Department.count({ where: { enterpriseId: id } }),
      ]);

      logger.info('获取企业详情成功', { 
        userId: currentUserId, 
        enterpriseId: id 
      });

      return {
        id: enterprise.id,
        name: enterprise.name,
        code: enterprise.code,
        logoUrl: enterprise.logoUrl,
        description: enterprise.description,
        status: enterprise.status,
        createdAt: formatDateTime(enterprise.createdAt),
        updatedAt: formatDateTime(enterprise.updatedAt),
        statistics: {
          userCount,
          departmentCount,
        },
      };
    } catch (error) {
      logger.error('获取企业详情失败', error);
      throw error;
    }
  }

  /**
   * 创建企业（仅超级管理员）
   */
  async createEnterprise(data: CreateEnterpriseRequest, currentUserId: number): Promise<any> {
    const transaction: Transaction = await sequelize.transaction();

    try {
      // 验证当前用户是否为超级管理员
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      if (!isSuperAdmin) {
        throw createApiError('权限不足，仅超级管理员可创建企业', 403, 'PERMISSION_DENIED');
      }

      // 检查企业编码是否已存在
      const existingEnterprise = await Enterprise.findOne({
        where: { code: data.code },
        transaction
      });

      if (existingEnterprise) {
        throw createApiError('企业编码已存在', 400, 'ENTERPRISE_CODE_EXISTS');
      }

      // 检查管理员用户名是否已存在
      const existingUser = await User.findOne({
        where: { username: data.adminUsername },
        transaction
      });

      if (existingUser) {
        throw createApiError('管理员用户名已存在', 400, 'USERNAME_EXISTS');
      }

      // 1. 创建企业
      const enterprise = await Enterprise.create({
        name: data.name,
        code: data.code,
        logoUrl: data.logoUrl,
        description: data.description,
        status: data.status ?? 1,
      }, { transaction });

      // 2. 创建企业默认部门（总部）
      const headquartersDepartment = await Department.create({
        name: '总部',
        description: '企业总部',
        enterpriseId: enterprise.id,
        parentId: null,
        level: 1,
        path: '0', // 临时路径，创建后会更新为实际ID
        sortOrder: 1,
      }, { transaction });

      // 更新部门路径为实际ID
      await headquartersDepartment.update({
        path: headquartersDepartment.id.toString()
      }, { transaction });

      // 3. 获取企业管理员需要的权限（包含enterprise:update权限）
      const adminPermissions = await Permission.findAll({
        where: {
          [Op.or]: [
            {
              module: {
                [Op.ne]: 'enterprise' // 除企业管理外的所有权限
              }
            },
            {
              code: 'enterprise:update' // 添加企业编辑权限
            }
          ]
        },
        transaction
      });

      // 4. 创建企业管理员角色
      const adminRole = await Role.create({
        name: '企业管理员',
        code: 'ENTERPRISE_ADMIN',
        description: '企业管理员，拥有企业内所有权限',
        enterpriseId: enterprise.id,
        isSystem: false, // 企业角色，不是系统角色
      }, { transaction });

      // 5. 为企业管理员角色分配权限（除企业管理外的所有权限）
      await adminRole.setPermissions(adminPermissions, { transaction });

      // 6. 获取业务权限（用于业务角色）
      const businessPermissions = await Permission.findAll({
        where: {
          module: {
            [Op.in]: ['order', 'production', 'inventory', 'salary_rate', 'salary_list', 'digital'] // 业务模块权限
          }
        },
        transaction
      });

      // 7. 创建业务角色
      const businessRoles = [
        { name: '老板', code: 'BOSS', description: '老板，拥有企业内所有权限', modules: 'all' }, // 特殊标记，表示所有权限
        { name: '销售', code: 'SALES', description: '销售人员，负责订单管理和数字空间', modules: ['order', 'digital'] },
        { name: '车间主任', code: 'WORKSHOP_MANAGER', description: '车间主任，负责生产管理和数字空间', modules: ['production', 'digital'] },
        { name: '制版师', code: 'PATTERN_MAKER', description: '制版师，负责数字空间', modules: ['digital'] },
        { name: '财务', code: 'FINANCE', description: '财务人员，负责工资管理', modules: ['salary_rate', 'salary_list'] },
      ];

      for (const roleData of businessRoles) {
        const role = await Role.create({
          name: roleData.name,
          code: roleData.code,
          description: roleData.description,
          enterpriseId: enterprise.id,
          isSystem: false,
        }, { transaction });

        // 为业务角色分配对应模块的权限
        let rolePermissions;
        if (roleData.modules === 'all') {
          // 老板角色：分配除企业管理外的所有权限（与企业管理员相同）
          rolePermissions = adminPermissions;
        } else {
          // 其他角色：分配对应模块的权限
          rolePermissions = businessPermissions.filter(permission =>
            (roleData.modules as string[]).includes(permission.module)
          );
        }
        await role.setPermissions(rolePermissions, { transaction });
      }

      // 8. 创建企业管理员账号
      const adminUser = await User.create({
        username: data.adminUsername,
        passwordHash: data.adminPassword, // User模型的beforeCreate钩子会自动加密
        realName: data.adminRealName,
        email: data.adminEmail,
        phone: data.adminPhone,
        enterpriseId: enterprise.id,
        departmentId: headquartersDepartment.id,
        status: 1,
      }, { transaction });

      // 9. 为管理员分配角色
      await UserRole.create({
        userId: adminUser.id,
        roleId: adminRole.id,
      }, { transaction });

      await transaction.commit();

      logger.info('创建企业成功', {
        userId: currentUserId,
        enterpriseId: enterprise.id,
        enterpriseName: enterprise.name,
        adminUserId: adminUser.id,
        adminUsername: adminUser.username,
      });

      return {
        id: enterprise.id,
        name: enterprise.name,
        code: enterprise.code,
        logoUrl: enterprise.logoUrl,
        description: enterprise.description,
        status: enterprise.status,
        createdAt: formatDateTime(enterprise.createdAt),
        updatedAt: formatDateTime(enterprise.updatedAt),
        adminUser: {
          id: adminUser.id,
          username: adminUser.username,
          realName: adminUser.realName,
          email: adminUser.email,
          phone: adminUser.phone,
        },
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('创建企业失败', error);
      throw error;
    }
  }

  /**
   * 更新企业信息
   */
  async updateEnterprise(id: number, data: UpdateEnterpriseRequest, currentUserId: number): Promise<any> {
    try {
      // 验证当前用户权限
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      const isEnterpriseAdmin = currentUser.roles?.some(role => role.code === 'ENTERPRISE_ADMIN');
      const isBoss = currentUser.roles?.some(role => role.code === 'BOSS');

      // 权限检查：超级管理员可以修改任何企业，企业管理员和老板只能修改自己的企业
      if (!isSuperAdmin && (!(isEnterpriseAdmin || isBoss) || currentUser.enterpriseId !== id)) {
        throw createApiError('权限不足，只能修改自己所属的企业', 403, 'PERMISSION_DENIED');
      }

      const enterprise = await Enterprise.findByPk(id);

      if (!enterprise) {
        throw createApiError('企业不存在', 404, 'ENTERPRISE_NOT_FOUND');
      }

      // 如果要更新企业编码，检查是否已存在
      if (data.code && data.code !== enterprise.code) {
        const existingEnterprise = await Enterprise.findOne({
          where: {
            code: data.code,
            id: { [Op.ne]: id }
          }
        });

        if (existingEnterprise) {
          throw createApiError('企业编码已存在', 400, 'ENTERPRISE_CODE_EXISTS');
        }
      }

      // 更新企业信息
      const updateData: any = {};
      if (data.name !== undefined) updateData.name = data.name;
      if (data.code !== undefined) updateData.code = data.code;
      if (data.logoUrl !== undefined) updateData.logoUrl = data.logoUrl;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.status !== undefined) updateData.status = data.status;

      await enterprise.update(updateData);

      logger.info('更新企业信息成功', {
        userId: currentUserId,
        enterpriseId: id,
        updateData,
      });

      return {
        id: enterprise.id,
        name: enterprise.name,
        code: enterprise.code,
        logoUrl: enterprise.logoUrl,
        description: enterprise.description,
        status: enterprise.status,
        createdAt: formatDateTime(enterprise.createdAt),
        updatedAt: formatDateTime(enterprise.updatedAt),
      };
    } catch (error) {
      logger.error('更新企业信息失败', error);
      throw error;
    }
  }

  /**
   * 删除企业（仅超级管理员）- 级联删除企业下的所有数据
   */
  async deleteEnterprise(id: number, currentUserId: number): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      // 验证当前用户是否为超级管理员
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      if (!isSuperAdmin) {
        throw createApiError('权限不足，仅超级管理员可删除企业', 403, 'PERMISSION_DENIED');
      }

      const enterprise = await Enterprise.findByPk(id);

      if (!enterprise) {
        throw createApiError('企业不存在', 404, 'ENTERPRISE_NOT_FOUND');
      }

      // 获取统计信息用于日志
      const userCount = await User.count({ where: { enterpriseId: id } });
      const departmentCount = await Department.count({ where: { enterpriseId: id } });
      const roleCount = await Role.count({ where: { enterpriseId: id } });

      // 1. 删除企业下的所有用户角色关联
      const enterpriseUsers = await User.findAll({
        where: { enterpriseId: id },
        attributes: ['id'],
        transaction
      });

      if (enterpriseUsers.length > 0) {
        const userIds = enterpriseUsers.map(user => user.id);
        await UserRole.destroy({
          where: { userId: { [Op.in]: userIds } },
          transaction
        });
      }

      // 2. 删除企业下的所有用户
      await User.destroy({
        where: { enterpriseId: id },
        transaction
      });

      // 3. 删除企业下的所有部门
      await Department.destroy({
        where: { enterpriseId: id },
        transaction
      });

      // 4. 删除企业下的所有角色（包括角色权限关联，通过模型的级联删除处理）
      await Role.destroy({
        where: { enterpriseId: id },
        transaction
      });

      // 5. 最后删除企业
      await enterprise.destroy({ transaction });

      await transaction.commit();

      logger.info('删除企业成功（级联删除）', {
        userId: currentUserId,
        enterpriseId: id,
        enterpriseName: enterprise.name,
        deletedCounts: {
          users: userCount,
          departments: departmentCount,
          roles: roleCount,
        },
      });
    } catch (error) {
      await transaction.rollback();
      logger.error('删除企业失败', error);
      throw error;
    }
  }

  /**
   * 重置企业管理员密码
   */
  async resetEnterpriseAdminPassword(enterpriseId: number, newPassword: string, currentUserId: number): Promise<void> {
    try {
      // 验证当前用户权限
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      if (!isSuperAdmin) {
        throw createApiError('权限不足，仅超级管理员可重置企业管理员密码', 403, 'PERMISSION_DENIED');
      }

      // 查找企业
      const enterprise = await Enterprise.findByPk(enterpriseId);
      if (!enterprise) {
        throw createApiError('企业不存在', 404, 'ENTERPRISE_NOT_FOUND');
      }

      // 查找企业管理员
      const adminUser = await User.findOne({
        where: { enterpriseId },
        include: [
          {
            model: Role,
            as: 'roles',
            where: { code: 'ENTERPRISE_ADMIN' },
            through: { attributes: [] }
          }
        ]
      });

      if (!adminUser) {
        throw createApiError('未找到企业管理员', 404, 'ADMIN_NOT_FOUND');
      }

      // 更新密码（User模型的beforeUpdate钩子会自动加密）
      await adminUser.update({
        passwordHash: newPassword
      });

      logger.info('重置企业管理员密码成功', {
        userId: currentUserId,
        enterpriseId,
        adminUserId: adminUser.id,
        adminUsername: adminUser.username,
      });

    } catch (error) {
      logger.error('重置企业管理员密码失败', error);
      throw error;
    }
  }
}
