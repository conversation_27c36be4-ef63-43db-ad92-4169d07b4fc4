/**
 * 标签服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 创建标签管理服务, 处理标签业务逻辑; Principle_Applied: 服务层设计;}}
 */

import { Op, Transaction } from 'sequelize';
import { sequelize } from '../../shared/database';
import { Tag, TagType, TagStatus, TagCreationAttributes } from '../../shared/database/models/Tag';
import { createApiError } from '../../shared/middleware/error.middleware';

export interface TagListQuery {
  page?: number;
  pageSize?: number;
  type?: TagType;
  status?: TagStatus;
  search?: string;
  pid?: number;
}

export interface TagListResponse {
  tags: Tag[];
  total: number;
  page: number;
  pageSize: number;
}

export interface TagTreeNode extends Tag {
  children?: TagTreeNode[];
}

export interface CreateTagRequest {
  name: string;
  type: TagType;
  pid?: number;
  status?: TagStatus;
}

export interface UpdateTagRequest {
  name?: string;
  status?: TagStatus;
  pid?: number;
}

/**
 * 标签服务类
 */
export class TagService {
  /**
   * 获取标签列表
   */
  static async getTagList(enterpriseId: number, query: TagListQuery): Promise<TagListResponse> {
    const {
      page = 1,
      pageSize = 20,
      type,
      status,
      search,
      pid
    } = query;

    const offset = (page - 1) * pageSize;
    const whereConditions: any = {
      enterpriseId
    };

    // 类型筛选
    if (type !== undefined) {
      whereConditions.type = type;
    }

    // 状态筛选
    if (status !== undefined) {
      whereConditions.status = status;
    }

    // 父级筛选
    if (pid !== undefined) {
      whereConditions.pid = pid;
    }

    // 名称搜索
    if (search) {
      whereConditions.name = {
        [Op.like]: `%${search}%`
      };
    }

    const { count, rows } = await Tag.findAndCountAll({
      where: whereConditions,
      limit: pageSize,
      offset,
      order: [['type', 'ASC'], ['level', 'ASC'], ['path', 'ASC'], ['id', 'ASC']],
      include: [
        {
          model: Tag,
          as: 'parent',
          required: false,
          attributes: ['id', 'name']
        }
      ]
    });

    return {
      tags: rows,
      total: count,
      page,
      pageSize
    };
  }

  /**
   * 获取标签树形结构
   */
  static async getTagTree(
    enterpriseId: number,
    type: TagType,
    status?: TagStatus,
    search?: string
  ): Promise<TagTreeNode[]> {
    const whereCondition: any = {
      enterpriseId,
      type
    };

    // 添加状态筛选
    if (status !== undefined) {
      whereCondition.status = status;
    }

    // 添加搜索条件
    if (search) {
      whereCondition.name = {
        [Op.like]: `%${search}%`
      };
    }

    const tags = await Tag.findAll({
      where: whereCondition,
      order: [['level', 'ASC'], ['path', 'ASC']]
    });

    return this.buildTree(tags);
  }

  /**
   * 构建树形结构
   */
  private static buildTree(tags: Tag[]): TagTreeNode[] {
    const tagMap = new Map<number, TagTreeNode>();
    const rootTags: TagTreeNode[] = [];

    // 创建节点映射
    tags.forEach(tag => {
      const tagData = tag.toJSON() as any;
      tagMap.set(tag.id, { ...tagData, children: [] });
    });

    // 构建树形结构
    tags.forEach(tag => {
      const node = tagMap.get(tag.id)!;
      
      if (tag.pid && tagMap.has(tag.pid)) {
        const parent = tagMap.get(tag.pid)!;
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(node);
      } else {
        rootTags.push(node);
      }
    });

    return rootTags;
  }

  /**
   * 获取标签详情
   */
  static async getTagById(enterpriseId: number, id: number): Promise<Tag> {
    const tag = await Tag.findOne({
      where: {
        id,
        enterpriseId
      },
      include: [
        {
          model: Tag,
          as: 'parent',
          required: false,
          attributes: ['id', 'name']
        }
      ]
    });

    if (!tag) {
      throw createApiError('标签不存在', 404);
    }

    return tag;
  }

  /**
   * 创建标签
   */
  static async createTag(enterpriseId: number, data: CreateTagRequest): Promise<Tag> {
    const transaction = await sequelize.transaction();

    try {
      // 验证名称唯一性
      await this.validateTagName(enterpriseId, data.name, data.type, undefined, transaction);

      // 处理层级和路径
      let level = 1;
      let path = '';
      
      if (data.pid && data.type === TagType.WORKSHOP_LINE) {
        const parent = await Tag.findOne({
          where: {
            id: data.pid,
            enterpriseId,
            type: data.type
          },
          transaction
        });

        if (!parent) {
          throw createApiError('父级标签不存在', 400);
        }

        level = parent.level + 1;
      } else if (data.type !== TagType.WORKSHOP_LINE) {
        // 非车间产线类型不支持层级
        data.pid = undefined;
        level = 1;
      }

      const tagData: TagCreationAttributes = {
        enterpriseId,
        name: data.name,
        type: data.type,
        pid: data.pid || null,
        level,
        path: '', // 先创建，后更新路径
        status: data.status || TagStatus.ENABLED
      };

      const tag = await Tag.create(tagData, { transaction });

      // 构建并更新路径
      if (data.pid && data.type === TagType.WORKSHOP_LINE) {
        const parent = await Tag.findByPk(data.pid, { transaction });
        path = `${parent!.path}/${tag.id}`;
      } else {
        path = tag.id.toString();
      }

      await tag.update({ path }, { transaction });

      await transaction.commit();

      return await this.getTagById(enterpriseId, tag.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 更新标签
   */
  static async updateTag(enterpriseId: number, id: number, data: UpdateTagRequest): Promise<Tag> {
    const transaction = await sequelize.transaction();

    try {
      const tag = await Tag.findOne({
        where: {
          id,
          enterpriseId
        },
        transaction
      });

      if (!tag) {
        throw createApiError('标签不存在', 404);
      }

      // 验证名称唯一性
      if (data.name && data.name !== tag.name) {
        await this.validateTagName(enterpriseId, data.name, tag.type, id, transaction);
      }

      // 处理父级变更（仅车间产线支持）
      if (data.pid !== undefined && tag.type === TagType.WORKSHOP_LINE) {
        if (data.pid === tag.id) {
          throw createApiError('不能将自己设为父级', 400);
        }

        if (data.pid) {
          // 检查是否会形成循环引用
          const wouldCreateCycle = await this.wouldCreateCycle(tag.id, data.pid, transaction);
          if (wouldCreateCycle) {
            throw createApiError('不能将后代节点设为父级', 400);
          }

          const parent = await Tag.findOne({
            where: {
              id: data.pid,
              enterpriseId,
              type: tag.type
            },
            transaction
          });

          if (!parent) {
            throw createApiError('父级标签不存在', 400);
          }
        }

        // 更新层级和路径
        await this.updateTagHierarchy(tag, data.pid, transaction);
      }

      // 更新其他字段
      const updateData: any = {};
      if (data.name) updateData.name = data.name;
      if (data.status !== undefined) {
        updateData.status = data.status;

        // 如果是车间产线类型，需要级联更新子级状态
        if (tag.type === TagType.WORKSHOP_LINE) {
          if (data.status === TagStatus.DISABLED) {
            // 禁用操作：级联禁用所有子级
            await this.cascadeUpdateStatus(tag.id, TagStatus.DISABLED, transaction);
          } else if (data.status === TagStatus.ENABLED) {
            // 启用操作：级联启用所有子级
            await this.cascadeUpdateStatus(tag.id, TagStatus.ENABLED, transaction);
          }
        }
      }

      await tag.update(updateData, { transaction });

      await transaction.commit();

      return await this.getTagById(enterpriseId, id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 级联更新状态
   */
  private static async cascadeUpdateStatus(
    parentId: number,
    status: TagStatus,
    transaction: Transaction
  ): Promise<void> {
    // 获取所有子级标签
    const children = await Tag.findAll({
      where: {
        pid: parentId
      },
      transaction
    });

    for (const child of children) {
      // 更新子级状态
      await child.update({ status }, { transaction });

      // 递归更新子级的子级
      await this.cascadeUpdateStatus(child.id, status, transaction);
    }
  }

  /**
   * 级联删除子级
   */
  private static async cascadeDeleteChildren(
    parentId: number,
    transaction: Transaction
  ): Promise<void> {
    // 获取所有子级标签
    const children = await Tag.findAll({
      where: {
        pid: parentId
      },
      transaction
    });

    for (const child of children) {
      // 递归删除子级的子级
      await this.cascadeDeleteChildren(child.id, transaction);

      // 删除子级
      await child.destroy({ transaction });
    }
  }

  /**
   * 删除标签
   */
  static async deleteTag(enterpriseId: number, id: number): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      const tag = await Tag.findOne({
        where: {
          id,
          enterpriseId
        },
        transaction
      });

      if (!tag) {
        throw createApiError('标签不存在', 404);
      }

      // 级联删除子标签（仅车间产线类型）
      if (tag.type === TagType.WORKSHOP_LINE) {
        await this.cascadeDeleteChildren(id, transaction);
      }

      await tag.destroy({ transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 验证标签名称唯一性
   */
  private static async validateTagName(
    enterpriseId: number,
    name: string,
    type: TagType,
    excludeId?: number,
    transaction?: Transaction
  ): Promise<void> {
    const whereConditions: any = {
      enterpriseId,
      name,
      type
    };

    if (excludeId) {
      whereConditions.id = { [Op.ne]: excludeId };
    }

    const existingTag = await Tag.findOne({
      where: whereConditions,
      transaction
    });

    if (existingTag) {
      throw createApiError('标签名称已存在', 400);
    }
  }

  /**
   * 检查是否会形成循环引用
   */
  private static async wouldCreateCycle(
    tagId: number,
    newParentId: number,
    transaction?: Transaction
  ): Promise<boolean> {
    let currentId = newParentId;

    while (currentId) {
      if (currentId === tagId) {
        return true;
      }

      const parent = await Tag.findByPk(currentId, {
        attributes: ['pid'],
        transaction
      });

      if (!parent || !parent.pid) {
        break;
      }

      currentId = parent.pid;
    }

    return false;
  }

  /**
   * 更新标签层级和路径
   */
  private static async updateTagHierarchy(
    tag: Tag,
    newParentId: number | null,
    transaction: Transaction
  ): Promise<void> {
    let newLevel = 1;
    let newPath = tag.id.toString();

    if (newParentId) {
      const parent = await Tag.findByPk(newParentId, { transaction });
      if (parent) {
        newLevel = parent.level + 1;
        newPath = `${parent.path}/${tag.id}`;
      }
    }

    // 更新当前标签
    await tag.update({
      pid: newParentId,
      level: newLevel,
      path: newPath
    }, { transaction });

    // 更新所有子标签的层级和路径
    const children = await Tag.findAll({
      where: {
        path: {
          [Op.like]: `${tag.path}/%`
        }
      },
      transaction
    });

    for (const child of children) {
      const oldPath = child.path;
      const newChildPath = oldPath.replace(tag.path, newPath);
      const pathParts = newChildPath.split('/');
      const newChildLevel = pathParts.length;

      await child.update({
        path: newChildPath,
        level: newChildLevel
      }, { transaction });
    }
  }
}

export default TagService;
