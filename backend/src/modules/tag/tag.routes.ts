/**
 * 标签路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 创建标签管理路由, 定义RESTful API; Principle_Applied: 路由设计;}}
 */

import { Router } from 'express';
import { TagController } from './tag.controller';
import { authMiddleware } from '../../shared/middleware/auth.middleware';
import asyncHandler from '../../shared/middleware/async.middleware';

const router = Router();

// 应用认证中间件
router.use(authMiddleware);

/**
 * @route GET /api/v1/tags
 * @desc 获取标签列表
 * @access Private
 * @query {number} page - 页码
 * @query {number} pageSize - 每页数量
 * @query {number} type - 标签类型
 * @query {number} status - 标签状态
 * @query {string} search - 搜索关键词
 * @query {number} pid - 父级ID
 */
router.get('/', asyncHandler(TagController.getTagList));

/**
 * @route GET /api/v1/tags/types
 * @desc 获取标签类型列表
 * @access Private
 */
router.get('/types', asyncHandler(TagController.getTagTypes));

/**
 * @route GET /api/v1/tags/tree/:type
 * @desc 获取指定类型的标签树形结构
 * @access Private
 * @param {number} type - 标签类型
 * @query {number} [status] - 标签状态筛选
 * @query {string} [search] - 搜索关键词
 */
router.get('/tree/:type', asyncHandler(TagController.getTagTree));

/**
 * @route GET /api/v1/tags/:id
 * @desc 获取标签详情
 * @access Private
 * @param {number} id - 标签ID
 */
router.get('/:id', asyncHandler(TagController.getTagById));

/**
 * @route POST /api/v1/tags
 * @desc 创建标签
 * @access Private
 * @body {string} name - 标签名称
 * @body {number} type - 标签类型
 * @body {number} [pid] - 父级ID（仅车间产线支持）
 * @body {number} [status] - 标签状态
 */
router.post('/', asyncHandler(TagController.createTag));

/**
 * @route PUT /api/v1/tags/:id
 * @desc 更新标签
 * @access Private
 * @param {number} id - 标签ID
 * @body {string} [name] - 标签名称
 * @body {number} [status] - 标签状态
 * @body {number} [pid] - 父级ID（仅车间产线支持）
 */
router.put('/:id', asyncHandler(TagController.updateTag));

/**
 * @route PUT /api/v1/tags/:id/status
 * @desc 更新标签状态
 * @access Private
 * @param {number} id - 标签ID
 * @body {number} status - 标签状态
 */
router.put('/:id/status', asyncHandler(TagController.updateTagStatus));

/**
 * @route DELETE /api/v1/tags/:id
 * @desc 删除标签
 * @access Private
 * @param {number} id - 标签ID
 */
router.delete('/:id', asyncHandler(TagController.deleteTag));

export default router;
