/**
 * 标签控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 创建标签管理控制器, 处理HTTP请求; Principle_Applied: 控制器设计;}}
 */

import { Request, Response } from 'express';
import { TagService, TagListQuery, CreateTagRequest, UpdateTagRequest } from './tag.service';
import { TagType, TagStatus } from '../../shared/database/models/Tag';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

/**
 * 标签控制器类
 */
export class TagController {
  /**
   * 获取标签列表
   */
  static async getTagList(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user?.enterpriseId;
      if (!enterpriseId) {
        throw createApiError('企业信息不存在', 400);
      }

      const query: TagListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 20,
        type: req.query.type ? parseInt(req.query.type as string) as TagType : undefined,
        status: req.query.status ? parseInt(req.query.status as string) as TagStatus : undefined,
        search: req.query.search as string,
        pid: req.query.pid ? parseInt(req.query.pid as string) : undefined
      };

      const result = await TagService.getTagList(enterpriseId, query);

      res.json({
        code: 200,
        message: '获取标签列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取标签列表失败', error);
      throw error;
    }
  }

  /**
   * 获取标签树形结构
   */
  static async getTagTree(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user?.enterpriseId;
      if (!enterpriseId) {
        throw createApiError('企业信息不存在', 400);
      }

      const type = parseInt(req.params.type) as TagType;
      if (!Object.values(TagType).includes(type)) {
        throw createApiError('无效的标签类型', 400);
      }

      // 获取查询参数
      const status = req.query.status ? parseInt(req.query.status as string) as TagStatus : undefined;
      const search = req.query.search as string;

      // 验证状态参数
      if (status !== undefined && !Object.values(TagStatus).includes(status)) {
        throw createApiError('无效的标签状态', 400);
      }

      const tree = await TagService.getTagTree(enterpriseId, type, status, search);

      res.json({
        code: 200,
        message: '获取标签树形结构成功',
        data: tree
      });
    } catch (error) {
      logger.error('获取标签树形结构失败', error);
      throw error;
    }
  }

  /**
   * 获取标签详情
   */
  static async getTagById(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user?.enterpriseId;
      if (!enterpriseId) {
        throw createApiError('企业信息不存在', 400);
      }

      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        throw createApiError('无效的标签ID', 400);
      }

      const tag = await TagService.getTagById(enterpriseId, id);

      res.json({
        code: 200,
        message: '获取标签详情成功',
        data: tag
      });
    } catch (error) {
      logger.error('获取标签详情失败', error);
      throw error;
    }
  }

  /**
   * 创建标签
   */
  static async createTag(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user?.enterpriseId;
      if (!enterpriseId) {
        throw createApiError('企业信息不存在', 400);
      }

      const data: CreateTagRequest = req.body;

      // 验证必填字段
      if (!data.name || !data.name.trim()) {
        throw createApiError('标签名称不能为空', 400);
      }

      if (!Object.values(TagType).includes(data.type)) {
        throw createApiError('无效的标签类型', 400);
      }

      // 验证状态
      if (data.status !== undefined && !Object.values(TagStatus).includes(data.status)) {
        throw createApiError('无效的标签状态', 400);
      }

      // 验证父级ID（仅车间产线支持）
      if (data.pid && data.type !== TagType.WORKSHOP_LINE) {
        throw createApiError('该类型标签不支持层级结构', 400);
      }

      const tag = await TagService.createTag(enterpriseId, data);

      res.status(201).json({
        code: 201,
        message: '创建标签成功',
        data: tag
      });
    } catch (error) {
      logger.error('创建标签失败', error);
      throw error;
    }
  }

  /**
   * 更新标签
   */
  static async updateTag(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user?.enterpriseId;
      if (!enterpriseId) {
        throw createApiError('企业信息不存在', 400);
      }

      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        throw createApiError('无效的标签ID', 400);
      }

      const data: UpdateTagRequest = req.body;

      // 验证状态
      if (data.status !== undefined && !Object.values(TagStatus).includes(data.status)) {
        throw createApiError('无效的标签状态', 400);
      }

      // 验证名称
      if (data.name !== undefined && (!data.name || !data.name.trim())) {
        throw createApiError('标签名称不能为空', 400);
      }

      const tag = await TagService.updateTag(enterpriseId, id, data);

      res.json({
        code: 200,
        message: '更新标签成功',
        data: tag
      });
    } catch (error) {
      logger.error('更新标签失败', error);
      throw error;
    }
  }

  /**
   * 删除标签
   */
  static async deleteTag(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user?.enterpriseId;
      if (!enterpriseId) {
        throw createApiError('企业信息不存在', 400);
      }

      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        throw createApiError('无效的标签ID', 400);
      }

      await TagService.deleteTag(enterpriseId, id);

      res.json({
        code: 200,
        message: '删除标签成功'
      });
    } catch (error) {
      logger.error('删除标签失败', error);
      throw error;
    }
  }

  /**
   * 更新标签状态
   */
  static async updateTagStatus(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user?.enterpriseId;
      if (!enterpriseId) {
        throw createApiError('企业信息不存在', 400);
      }

      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        throw createApiError('无效的标签ID', 400);
      }

      const { status } = req.body;
      if (!Object.values(TagStatus).includes(status)) {
        throw createApiError('无效的标签状态', 400);
      }

      const tag = await TagService.updateTag(enterpriseId, id, { status });

      res.json({
        code: 200,
        message: '更新标签状态成功',
        data: tag
      });
    } catch (error) {
      logger.error('更新标签状态失败', error);
      throw error;
    }
  }

  /**
   * 获取标签类型列表
   */
  static async getTagTypes(req: Request, res: Response): Promise<void> {
    try {
      const types = [
        { value: TagType.WORKSHOP_LINE, label: '车间产线', supportHierarchy: true },
        { value: TagType.MACHINE_GROUP, label: '机器分组', supportHierarchy: false },
        { value: TagType.PATTERN_GROUP, label: '花样分组', supportHierarchy: false },
        { value: TagType.PATTERN_UNIT, label: '花样计量单位', supportHierarchy: false }
      ];

      res.json({
        code: 200,
        message: '获取标签类型成功',
        data: types
      });
    } catch (error) {
      logger.error('获取标签类型失败', error);
      throw error;
    }
  }
}

export default TagController;
