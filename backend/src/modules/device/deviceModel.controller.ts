/**
 * 设备类型控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建设备类型HTTP接口控制器; Principle_Applied: 控制器模式;}}
 */

import { Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { DeviceModelService, DeviceModelListQuery, CreateDeviceModelRequest, UpdateDeviceModelRequest } from './deviceModel.service';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

const deviceModelService = new DeviceModelService();

export class DeviceModelController {
  /**
   * 获取设备类型列表
   */
  async getDeviceModelList(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const query: DeviceModelListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 10,
        search: req.query.search as string,
        enterpriseId: req.user!.enterpriseId
      };

      const result = await deviceModelService.getDeviceModelList(query);

      res.json({
        code: 200,
        message: '获取设备类型列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取设备类型列表失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取设备类型详情
   */
  async getDeviceModelById(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceModelId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      const deviceModel = await deviceModelService.getDeviceModelById(deviceModelId, enterpriseId);

      res.json({
        code: 200,
        message: '获取设备类型详情成功',
        data: deviceModel
      });
    } catch (error) {
      logger.error('获取设备类型详情失败', {
        deviceModelId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建设备类型
   */
  async createDeviceModel(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: CreateDeviceModelRequest = {
        ...req.body,
        enterpriseId: req.user!.enterpriseId
      };

      const deviceModel = await deviceModelService.createDeviceModel(data);

      res.status(201).json({
        code: 201,
        message: '创建设备类型成功',
        data: deviceModel
      });
    } catch (error) {
      logger.error('创建设备类型失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        deviceModelName: req.body.name,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新设备类型
   */
  async updateDeviceModel(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceModelId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const data: UpdateDeviceModelRequest = req.body;

      const deviceModel = await deviceModelService.updateDeviceModel(deviceModelId, enterpriseId, data);

      res.json({
        code: 200,
        message: '更新设备类型成功',
        data: deviceModel
      });
    } catch (error) {
      logger.error('更新设备类型失败', {
        deviceModelId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除设备类型
   */
  async deleteDeviceModel(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceModelId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      await deviceModelService.deleteDeviceModel(deviceModelId, enterpriseId);

      res.json({
        code: 200,
        message: '删除设备类型成功'
      });
    } catch (error) {
      logger.error('删除设备类型失败', {
        deviceModelId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新设备类型状态
   */
  async updateDeviceModelStatus(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceModelId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const { status } = req.body;

      const deviceModel = await deviceModelService.updateDeviceModelStatus(deviceModelId, enterpriseId, status);

      res.json({
        code: 200,
        message: '更新设备类型状态成功',
        data: deviceModel
      });
    } catch (error) {
      logger.error('更新设备类型状态失败', {
        deviceModelId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        status: req.body.status,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取设备类型选项列表
   */
  async getDeviceModelOptions(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;
      const options = await deviceModelService.getDeviceModelOptions(enterpriseId);

      res.json({
        code: 200,
        message: '获取设备类型选项列表成功',
        data: options
      });
    } catch (error) {
      logger.error('获取设备类型选项列表失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}

// 验证规则
export const deviceModelValidationRules = {
  // 获取设备类型列表验证
  getDeviceModelList: [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词长度不能超过100个字符'),
  ],

  // 获取设备类型详情验证
  getDeviceModelById: [
    param('id').isInt({ min: 1 }).withMessage('设备类型ID必须是大于0的整数'),
  ],

  // 创建设备类型验证
  createDeviceModel: [
    body('code').notEmpty().withMessage('型号编码不能为空').isLength({ max: 50 }).withMessage('型号编码长度不能超过50个字符'),
    body('name').notEmpty().withMessage('设备类型名称不能为空').isLength({ max: 100 }).withMessage('设备类型名称长度不能超过100个字符'),
    body('parameter').optional().isLength({ max: 5000 }).withMessage('配置参数长度不能超过5000个字符'),
  ],

  // 更新设备类型验证
  updateDeviceModel: [
    param('id').isInt({ min: 1 }).withMessage('设备类型ID必须是大于0的整数'),
    body('code').optional().notEmpty().withMessage('型号编码不能为空').isLength({ max: 50 }).withMessage('型号编码长度不能超过50个字符'),
    body('name').optional().notEmpty().withMessage('设备类型名称不能为空').isLength({ max: 100 }).withMessage('设备类型名称长度不能超过100个字符'),
    body('parameter').optional().isLength({ max: 5000 }).withMessage('配置参数长度不能超过5000个字符'),
  ],

  // 删除设备类型验证
  deleteDeviceModel: [
    param('id').isInt({ min: 1 }).withMessage('设备类型ID必须是大于0的整数'),
  ],

  // 更新设备类型状态验证
  updateDeviceModelStatus: [
    param('id').isInt({ min: 1 }).withMessage('设备类型ID必须是大于0的整数'),
    body('status').isInt({ min: 0, max: 1 }).withMessage('状态值必须是0（禁用）或1（启用）'),
  ],
};
