/**
 * 设备服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 14:30:51 +08:00; Reason: 设备管理模块开发, 创建设备业务逻辑服务; Principle_Applied: 业务逻辑分离;}}
 */

import { Op, WhereOptions } from 'sequelize';
import { Device } from '../../shared/database/models/Device';
import { DeviceModel } from '../../shared/database/models/DeviceModel';
import { Tag, TagType } from '../../shared/database/models/Tag';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

// 设备列表查询参数
export interface DeviceListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  deviceModelId?: number;
  productionLineId?: number;
  groupId?: number;
  vendor?: string;
  wifiState?: string;
  enterpriseId: number;
}

// 设备列表响应
export interface DeviceListResponse {
  devices: Device[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 搜索选项接口
export interface DeviceSearchOptions {
  deviceModels: Array<{ id: number; name: string }>;
  productionLines: Array<{ id: number; name: string }>;
  groups: Array<{ id: number; name: string }>;
}

// 创建设备请求
export interface CreateDeviceRequest {
  name: string;
  code?: string;
  sn?: string;
  mac?: string;
  ip?: string;
  remark?: string;
  controlModel?: string;
  deviceModel?: string;
  vendor?: string;
  headSpace?: number;
  headNum?: number;
  headNeedleNum?: number;
  formularHeadSpace?: number;
  formularHeadNum?: number;
  formularLength?: number;
  displaySoftware?: string;
  controlSoftware?: string;
  productionLineId?: number;
  groupId?: number;
  registerWay?: string;
  enterpriseId: number;
}

// 更新设备请求
export interface UpdateDeviceRequest {
  name?: string;
  code?: string;
  sn?: string;
  mac?: string;
  ip?: string;
  remark?: string;
  controlModel?: string;
  deviceModel?: string;
  vendor?: string;
  headSpace?: number;
  headNum?: number;
  headNeedleNum?: number;
  formularHeadSpace?: number;
  formularHeadNum?: number;
  formularLength?: number;
  displaySoftware?: string;
  controlSoftware?: string;
  productionLineId?: string;
  registerWay?: string;
}

// WiFi配置更新请求
export interface UpdateWifiConfigRequest {
  wifiBitRate?: number;
  wifiFreq?: number;
  wifiIp?: string;
  wifiKeyMgmt?: string;
  wifiMac?: string;
  wifiSsid?: string;
  wifiState?: string;
  wifiLinkQuality?: string;
  wifiSignalLevel?: string;
  gatewayMac?: string;
}

export class DeviceService {
  /**
   * 获取设备列表
   */
  async getDeviceList(query: DeviceListQuery): Promise<DeviceListResponse> {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
        deviceModelId,
        productionLineId,
        groupId,
        vendor,
        wifiState,
        enterpriseId
      } = query;

      // 构建查询条件
      const whereConditions: WhereOptions = {
        enterpriseId
      };

      // 搜索条件
      if (search) {
        whereConditions[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { code: { [Op.iLike]: `%${search}%` } },
          { sn: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // 筛选条件
      if (deviceModelId) {
        whereConditions.deviceModelId = deviceModelId;
      }
      if (productionLineId) {
        whereConditions.productionLineId = productionLineId;
      }
      if (groupId) {
        whereConditions.groupId = groupId;
      }
      if (vendor) {
        whereConditions.vendor = vendor;
      }
      if (wifiState) {
        whereConditions.wifiState = wifiState;
      }

      // 分页参数
      const offset = (page - 1) * pageSize;
      const limit = pageSize;

      // 查询设备列表
      const { rows: devices, count: total } = await Device.findAndCountAll({
        where: whereConditions,
        offset,
        limit,
        order: [['createdAt', 'DESC']],
        include: [
          {
            association: 'deviceModelInfo',
            attributes: ['id', 'code', 'name'],
            required: false,
          },
          {
            association: 'productionLine',
            attributes: ['id', 'name'],
            required: false,
          },
          {
            association: 'group',
            attributes: ['id', 'name'],
            required: false,
          }
        ]
      });

      const totalPages = Math.ceil(total / pageSize);

      logger.info('获取设备列表成功', {
        enterpriseId,
        total,
        page,
        pageSize
      });

      return {
        devices,
        total,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      logger.error('获取设备列表失败', {
        enterpriseId: query.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据ID获取设备详情
   */
  async getDeviceById(id: number, enterpriseId: number): Promise<Device> {
    try {
      const device = await Device.findOne({
        where: { id, enterpriseId },
        include: [
          {
            association: 'deviceModelInfo',
            attributes: ['id', 'code', 'name', 'parameter'],
            required: false,
          }
        ]
      });

      if (!device) {
        throw createApiError('设备不存在', 404, 'DEVICE_NOT_FOUND');
      }

      logger.info('获取设备详情成功', { deviceId: id, enterpriseId });

      return device;
    } catch (error) {
      logger.error('获取设备详情失败', {
        deviceId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建设备
   */
  async createDevice(data: CreateDeviceRequest): Promise<Device> {
    try {
      // 检查设备编号是否重复
      if (data.code) {
        const existingDevice = await Device.findOne({
          where: {
            code: data.code,
            enterpriseId: data.enterpriseId
          }
        });

        if (existingDevice) {
          throw createApiError('设备编号已存在', 400, 'DEVICE_CODE_EXISTS');
        }
      }

      // 检查SN是否重复
      if (data.sn) {
        const existingDevice = await Device.findOne({
          where: {
            sn: data.sn,
            enterpriseId: data.enterpriseId
          }
        });

        if (existingDevice) {
          throw createApiError('设备SN已存在', 400, 'DEVICE_SN_EXISTS');
        }
      }

      // 创建设备
      const device = await Device.create(data);

      logger.info('创建设备成功', {
        deviceId: device.id,
        deviceName: device.name,
        enterpriseId: data.enterpriseId
      });

      return device;
    } catch (error) {
      logger.error('创建设备失败', {
        deviceName: data.name,
        enterpriseId: data.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新设备
   */
  async updateDevice(id: number, data: UpdateDeviceRequest, enterpriseId: number): Promise<Device> {
    try {
      const device = await this.getDeviceById(id, enterpriseId);

      // 检查设备编号是否重复（排除当前设备）
      if (data.code && data.code !== device.code) {
        const existingDevice = await Device.findOne({
          where: {
            code: data.code,
            enterpriseId,
            id: { [Op.ne]: id }
          }
        });

        if (existingDevice) {
          throw createApiError('设备编号已存在', 400, 'DEVICE_CODE_EXISTS');
        }
      }

      // 检查SN是否重复（排除当前设备）
      if (data.sn && data.sn !== device.sn) {
        const existingDevice = await Device.findOne({
          where: {
            sn: data.sn,
            enterpriseId,
            id: { [Op.ne]: id }
          }
        });

        if (existingDevice) {
          throw createApiError('设备SN已存在', 400, 'DEVICE_SN_EXISTS');
        }
      }

      // 更新设备
      await device.update(data);

      logger.info('更新设备成功', {
        deviceId: id,
        deviceName: device.name,
        enterpriseId
      });

      return device;
    } catch (error) {
      logger.error('更新设备失败', {
        deviceId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除设备
   */
  async deleteDevice(id: number, enterpriseId: number): Promise<void> {
    try {
      const device = await this.getDeviceById(id, enterpriseId);

      await device.destroy();

      logger.info('删除设备成功', {
        deviceId: id,
        deviceName: device.name,
        enterpriseId
      });
    } catch (error) {
      logger.error('删除设备失败', {
        deviceId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新设备WiFi配置
   */
  async updateWifiConfig(id: number, data: UpdateWifiConfigRequest, enterpriseId: number): Promise<Device> {
    try {
      const device = await this.getDeviceById(id, enterpriseId);

      await device.update(data);

      logger.info('更新设备WiFi配置成功', {
        deviceId: id,
        deviceName: device.name,
        enterpriseId
      });

      return device;
    } catch (error) {
      logger.error('更新设备WiFi配置失败', {
        deviceId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取设备统计信息
   */
  async getDeviceStats(enterpriseId: number): Promise<{
    total: number;
    online: number;
    offline: number;
    byModel: Array<{ model: string; count: number }>;
    byVendor: Array<{ vendor: string; count: number }>;
  }> {
    try {
      // 总设备数
      const total = await Device.count({
        where: { enterpriseId }
      });

      // 在线设备数（WiFi状态为connected）
      const online = await Device.count({
        where: {
          enterpriseId,
          wifiState: 'connected'
        }
      });

      const offline = total - online;

      // 按机型统计
      const byModelRaw = await Device.findAll({
        where: { enterpriseId },
        attributes: [
          'deviceModel',
          [Device.sequelize!.fn('COUNT', '*'), 'count']
        ],
        group: ['deviceModel'],
        raw: true
      }) as any[];

      const byModel = byModelRaw.map(item => ({
        model: item.deviceModel || '未知',
        count: parseInt(item.count)
      }));

      // 按厂商统计
      const byVendorRaw = await Device.findAll({
        where: { enterpriseId },
        attributes: [
          'vendor',
          [Device.sequelize!.fn('COUNT', '*'), 'count']
        ],
        group: ['vendor'],
        raw: true
      }) as any[];

      const byVendor = byVendorRaw.map(item => ({
        vendor: item.vendor || '未知',
        count: parseInt(item.count)
      }));

      logger.info('获取设备统计信息成功', { enterpriseId, total, online, offline });

      return {
        total,
        online,
        offline,
        byModel,
        byVendor
      };
    } catch (error) {
      logger.error('获取设备统计信息失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取设备搜索选项数据
   */
  async getSearchOptions(enterpriseId: number): Promise<DeviceSearchOptions> {
    try {
      logger.info('开始获取设备搜索选项', { enterpriseId });

      // 先尝试查询设备类型选项（只返回启用状态的设备类型）
      let deviceModels: any[] = [];
      try {
        deviceModels = await DeviceModel.findAll({
          where: {
            enterpriseId,
            status: 1 // 只查询启用状态的设备类型
          },
          attributes: ['id', 'name'],
          order: [['name', 'ASC']]
        });
        logger.info('设备类型查询成功', { count: deviceModels.length });
      } catch (error) {
        logger.error('设备类型查询失败', { error: error instanceof Error ? error.message : '未知错误' });
      }

      // 尝试查询车间产线选项（树形结构，只返回启用状态的产线）
      let productionLines: any[] = [];
      try {
        const allProductionLines = await Tag.findAll({
          where: {
            enterpriseId,
            type: TagType.WORKSHOP_LINE,
            status: 1 // 只查询启用状态的产线
          },
          attributes: ['id', 'name', 'pid', 'level'],
          order: [['level', 'ASC'], ['name', 'ASC']]
        });

        // 构建树形结构
        productionLines = this.buildTagTree(allProductionLines);
        logger.info('车间产线查询成功', { count: allProductionLines.length, treeCount: productionLines.length });
      } catch (error) {
        logger.error('车间产线查询失败', { error: error instanceof Error ? error.message : '未知错误' });
      }

      // 尝试查询机器分组选项（只返回启用状态的分组）
      let groups: any[] = [];
      try {
        groups = await Tag.findAll({
          where: {
            enterpriseId,
            type: TagType.MACHINE_GROUP,
            status: 1 // 只查询启用状态的分组
          },
          attributes: ['id', 'name'],
          order: [['name', 'ASC']]
        });
        logger.info('机器分组查询成功', { count: groups.length });
      } catch (error) {
        logger.error('机器分组查询失败', { error: error instanceof Error ? error.message : '未知错误' });
      }

      const result = {
        deviceModels: deviceModels.map(item => ({
          id: item.id,
          name: item.name
        })),
        productionLines: productionLines,
        groups: groups.map(item => ({
          id: item.id,
          name: item.name
        }))
      };

      logger.info('获取设备搜索选项成功', {
        enterpriseId,
        deviceModelsCount: result.deviceModels.length,
        productionLinesCount: result.productionLines.length,
        groupsCount: result.groups.length
      });

      return result;
    } catch (error) {
      logger.error('获取设备搜索选项失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * 构建标签树形结构
   */
  private buildTagTree(tags: any[]): any[] {
    const tagMap = new Map<number, any>();
    const rootTags: any[] = [];

    // 先创建所有节点的映射
    tags.forEach(tag => {
      tagMap.set(tag.id, {
        id: tag.id,
        name: tag.name,
        value: tag.id,
        title: tag.name,
        key: tag.id.toString(),
        pid: tag.pid,
        level: tag.level,
        children: []
      });
    });

    // 构建树形结构
    tags.forEach(tag => {
      const node = tagMap.get(tag.id);
      if (tag.pid === null || tag.pid === 0) {
        // 根节点
        rootTags.push(node);
      } else {
        // 子节点
        const parent = tagMap.get(tag.pid);
        if (parent) {
          parent.children.push(node);
        }
      }
    });

    return rootTags;
  }
}
