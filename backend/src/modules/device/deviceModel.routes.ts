/**
 * 设备类型路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建设备类型HTTP路由配置; Principle_Applied: 路由分离;}}
 */

import { Router } from 'express';
import { DeviceModelController, deviceModelValidationRules } from './deviceModel.controller';
import { authMiddleware, permissionMiddleware } from '../../shared/middleware/auth.middleware';
import { asyncHandler } from '../../shared/middleware/async.middleware';

const router = Router();
const deviceModelController = new DeviceModelController();

/**
 * @swagger
 * components:
 *   schemas:
 *     DeviceModel:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 设备类型ID
 *         enterpriseId:
 *           type: integer
 *           description: 企业ID
 *         code:
 *           type: string
 *           description: 型号编码
 *         name:
 *           type: string
 *           description: 名称
 *         parameter:
 *           type: string
 *           description: 型号对应的配置
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *       required:
 *         - id
 *         - enterpriseId
 *         - code
 *         - name
 *         - createdAt
 *         - updatedAt
 */

/**
 * @swagger
 * /api/v1/device-models:
 *   get:
 *     summary: 获取设备类型列表
 *     tags: [DeviceModel]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词（名称、编码）
 *     responses:
 *       200:
 *         description: 获取设备类型列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取设备类型列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     deviceModels:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DeviceModel'
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 */
router.get('/', authMiddleware, permissionMiddleware(['equipment:view']), deviceModelValidationRules.getDeviceModelList, asyncHandler(deviceModelController.getDeviceModelList));

/**
 * @swagger
 * /api/v1/device-models/options:
 *   get:
 *     summary: 获取设备类型选项列表
 *     tags: [DeviceModel]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取设备类型选项列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取设备类型选项列表成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       code:
 *                         type: string
 *                       name:
 *                         type: string
 */
router.get('/options', authMiddleware, permissionMiddleware(['equipment:view']), asyncHandler(deviceModelController.getDeviceModelOptions));

/**
 * @swagger
 * /api/v1/device-models/{id}:
 *   get:
 *     summary: 获取设备类型详情
 *     tags: [DeviceModel]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 设备类型ID
 *     responses:
 *       200:
 *         description: 获取设备类型详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取设备类型详情成功
 *                 data:
 *                   $ref: '#/components/schemas/DeviceModel'
 */
router.get('/:id', authMiddleware, permissionMiddleware(['equipment:view']), deviceModelValidationRules.getDeviceModelById, asyncHandler(deviceModelController.getDeviceModelById));

/**
 * @swagger
 * /api/v1/device-models:
 *   post:
 *     summary: 创建设备类型
 *     tags: [DeviceModel]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - name
 *             properties:
 *               code:
 *                 type: string
 *                 description: 型号编码
 *               name:
 *                 type: string
 *                 description: 名称
 *               parameter:
 *                 type: string
 *                 description: 型号对应的配置
 *     responses:
 *       201:
 *         description: 创建设备类型成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 创建设备类型成功
 *                 data:
 *                   $ref: '#/components/schemas/DeviceModel'
 */
router.post('/', authMiddleware, permissionMiddleware(['equipment:create']), deviceModelValidationRules.createDeviceModel, asyncHandler(deviceModelController.createDeviceModel));

/**
 * @swagger
 * /api/v1/device-models/{id}:
 *   put:
 *     summary: 更新设备类型
 *     tags: [DeviceModel]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 设备类型ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: 型号编码
 *               name:
 *                 type: string
 *                 description: 名称
 *               parameter:
 *                 type: string
 *                 description: 型号对应的配置
 *     responses:
 *       200:
 *         description: 更新设备类型成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新设备类型成功
 *                 data:
 *                   $ref: '#/components/schemas/DeviceModel'
 */
router.put('/:id', authMiddleware, permissionMiddleware(['equipment:update']), deviceModelValidationRules.updateDeviceModel, asyncHandler(deviceModelController.updateDeviceModel));

/**
 * @swagger
 * /api/v1/device-models/{id}:
 *   delete:
 *     summary: 删除设备类型
 *     tags: [DeviceModel]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 设备类型ID
 *     responses:
 *       200:
 *         description: 删除设备类型成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除设备类型成功
 */
router.delete('/:id', authMiddleware, permissionMiddleware(['equipment:delete']), deviceModelValidationRules.deleteDeviceModel, asyncHandler(deviceModelController.deleteDeviceModel));

export default router;
