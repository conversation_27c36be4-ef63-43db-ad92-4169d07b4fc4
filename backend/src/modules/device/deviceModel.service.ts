/**
 * 设备类型服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-24 11:29:35 +08:00; Reason: 新增设备类型表, 创建设备类型业务逻辑服务; Principle_Applied: 业务逻辑分离;}}
 */

import { Op, WhereOptions } from 'sequelize';
import { DeviceModel } from '../../shared/database/models/DeviceModel';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

// 设备类型列表查询参数
export interface DeviceModelListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  enterpriseId: number;
}

// 设备类型列表响应
export interface DeviceModelListResponse {
  deviceModels: DeviceModel[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 创建设备类型请求
export interface CreateDeviceModelRequest {
  enterpriseId: number;
  code: string;
  name: string;
  parameter?: string;
}

// 更新设备类型请求
export interface UpdateDeviceModelRequest {
  code?: string;
  name?: string;
  parameter?: string;
}

export class DeviceModelService {
  /**
   * 获取设备类型列表
   */
  async getDeviceModelList(query: DeviceModelListQuery): Promise<DeviceModelListResponse> {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
        enterpriseId
      } = query;

      // 构建查询条件
      const whereConditions: WhereOptions = {
        enterpriseId
      };

      // 搜索条件
      if (search) {
        whereConditions[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { code: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // 计算偏移量
      const offset = (page - 1) * pageSize;

      // 查询设备类型列表
      const { rows: deviceModels, count: total } = await DeviceModel.findAndCountAll({
        where: whereConditions,
        limit: pageSize,
        offset,
        order: [['createdAt', 'DESC']],
        include: [
          {
            association: 'devices',
            attributes: ['id'],
            required: false,
          }
        ]
      });

      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);

      logger.info('获取设备类型列表成功', {
        enterpriseId,
        total,
        page,
        pageSize
      });

      return {
        deviceModels,
        total,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      logger.error('获取设备类型列表失败', {
        enterpriseId: query.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据ID获取设备类型详情
   */
  async getDeviceModelById(id: number, enterpriseId: number): Promise<DeviceModel> {
    try {
      const deviceModel = await DeviceModel.findOne({
        where: {
          id,
          enterpriseId
        },
        include: [
          {
            association: 'devices',
            attributes: ['id', 'name', 'code'],
            required: false,
          }
        ]
      });

      if (!deviceModel) {
        throw createApiError('设备类型不存在', 404, 'DEVICE_MODEL_NOT_FOUND');
      }

      logger.info('获取设备类型详情成功', {
        deviceModelId: id,
        enterpriseId
      });

      return deviceModel;
    } catch (error) {
      logger.error('获取设备类型详情失败', {
        deviceModelId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建设备类型
   */
  async createDeviceModel(data: CreateDeviceModelRequest): Promise<DeviceModel> {
    try {
      // 检查型号编码是否重复
      const existingDeviceModel = await DeviceModel.findOne({
        where: {
          code: data.code,
          enterpriseId: data.enterpriseId
        }
      });

      if (existingDeviceModel) {
        throw createApiError('型号编码已存在', 400, 'DEVICE_MODEL_CODE_EXISTS');
      }

      // 创建设备类型
      const deviceModel = await DeviceModel.create(data);

      logger.info('创建设备类型成功', {
        deviceModelId: deviceModel.id,
        deviceModelName: deviceModel.name,
        enterpriseId: data.enterpriseId
      });

      return deviceModel;
    } catch (error) {
      logger.error('创建设备类型失败', {
        deviceModelName: data.name,
        enterpriseId: data.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新设备类型
   */
  async updateDeviceModel(id: number, enterpriseId: number, data: UpdateDeviceModelRequest): Promise<DeviceModel> {
    try {
      // 查找设备类型
      const deviceModel = await DeviceModel.findOne({
        where: {
          id,
          enterpriseId
        }
      });

      if (!deviceModel) {
        throw createApiError('设备类型不存在', 404, 'DEVICE_MODEL_NOT_FOUND');
      }

      // 如果更新型号编码，检查是否重复
      if (data.code && data.code !== deviceModel.code) {
        const existingDeviceModel = await DeviceModel.findOne({
          where: {
            code: data.code,
            enterpriseId,
            id: { [Op.ne]: id }
          }
        });

        if (existingDeviceModel) {
          throw createApiError('型号编码已存在', 400, 'DEVICE_MODEL_CODE_EXISTS');
        }
      }

      // 更新设备类型
      await deviceModel.update(data);

      logger.info('更新设备类型成功', {
        deviceModelId: id,
        enterpriseId
      });

      return deviceModel;
    } catch (error) {
      logger.error('更新设备类型失败', {
        deviceModelId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除设备类型
   */
  async deleteDeviceModel(id: number, enterpriseId: number): Promise<void> {
    try {
      // 查找设备类型
      const deviceModel = await DeviceModel.findOne({
        where: {
          id,
          enterpriseId
        },
        include: [
          {
            association: 'devices',
            attributes: ['id'],
            required: false,
          }
        ]
      });

      if (!deviceModel) {
        throw createApiError('设备类型不存在', 404, 'DEVICE_MODEL_NOT_FOUND');
      }

      // 检查是否有关联的设备
      if (deviceModel.devices && deviceModel.devices.length > 0) {
        throw createApiError('该设备类型下还有关联的设备，无法删除', 400, 'DEVICE_MODEL_HAS_DEVICES');
      }

      // 删除设备类型
      await deviceModel.destroy();

      logger.info('删除设备类型成功', {
        deviceModelId: id,
        enterpriseId
      });
    } catch (error) {
      logger.error('删除设备类型失败', {
        deviceModelId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取设备类型选项列表（用于下拉选择）
   */
  async getDeviceModelOptions(enterpriseId: number): Promise<Array<{ id: number; code: string; name: string }>> {
    try {
      const deviceModels = await DeviceModel.findAll({
        where: { enterpriseId },
        attributes: ['id', 'code', 'name'],
        order: [['name', 'ASC']]
      });

      logger.info('获取设备类型选项列表成功', {
        enterpriseId,
        count: deviceModels.length
      });

      return deviceModels.map(dm => ({
        id: dm.id,
        code: dm.code,
        name: dm.name
      }));
    } catch (error) {
      logger.error('获取设备类型选项列表失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}
