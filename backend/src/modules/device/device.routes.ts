/**
 * 设备路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 14:30:51 +08:00; Reason: 设备管理模块开发, 创建设备HTTP路由配置; Principle_Applied: 路由分离;}}
 */

import { Router } from 'express';
import { DeviceController, deviceValidationRules } from './device.controller';
import { authMiddleware } from '../../shared/middleware/auth.middleware';
import { asyncHandler } from '../../shared/middleware/async.middleware';

const router = Router();
const deviceController = new DeviceController();

/**
 * @swagger
 * components:
 *   schemas:
 *     Device:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 设备ID
 *         enterpriseId:
 *           type: integer
 *           description: 企业ID
 *         code:
 *           type: string
 *           description: 机器编号
 *         sn:
 *           type: string
 *           description: 机器SN
 *         mac:
 *           type: string
 *           description: 机器Mac地址
 *         ip:
 *           type: string
 *           description: 机器IP
 *         name:
 *           type: string
 *           description: 机器名称
 *         remark:
 *           type: string
 *           description: 备注
 *         controlModel:
 *           type: string
 *           description: 电控
 *         deviceModel:
 *           type: string
 *           description: 机型
 *         vendor:
 *           type: string
 *           description: 电控厂商
 *         headSpace:
 *           type: number
 *           description: 机头头距
 *         headNum:
 *           type: number
 *           description: 机头头数
 *         headNeedleNum:
 *           type: integer
 *           description: 机头针数
 *         formularHeadSpace:
 *           type: number
 *           description: 计算头距
 *         formularHeadNum:
 *           type: number
 *           description: 计算头数
 *         formularLength:
 *           type: number
 *           description: 计算长度
 *         displaySoftware:
 *           type: string
 *           description: 显示软件
 *         controlSoftware:
 *           type: string
 *           description: 主控软件
 *         productionLineId:
 *           type: string
 *           description: 产线ID
 *         registerWay:
 *           type: string
 *           description: 添加方式
 *         wifiBitRate:
 *           type: number
 *           description: WiFi速率
 *         wifiFreq:
 *           type: number
 *           description: 无线频率
 *         wifiIp:
 *           type: string
 *           description: WiFi的IP
 *         wifiKeyMgmt:
 *           type: string
 *           description: WiFi加密方式
 *         wifiMac:
 *           type: string
 *           description: WiFi的mac
 *         wifiSsid:
 *           type: string
 *           description: WiFi名称
 *         wifiState:
 *           type: string
 *           description: WiFi连接状态
 *         wifiLinkQuality:
 *           type: string
 *           description: WiFi信号质量
 *         wifiSignalLevel:
 *           type: string
 *           description: WiFi信号强度
 *         gatewayMac:
 *           type: string
 *           description: 网关Mac
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *       required:
 *         - name
 *         - enterpriseId
 */

/**
 * @swagger
 * /api/v1/devices:
 *   get:
 *     summary: 获取设备列表
 *     tags: [设备管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词（设备名称、编号、SN）
 *       - in: query
 *         name: deviceModel
 *         schema:
 *           type: string
 *         description: 机型筛选
 *       - in: query
 *         name: vendor
 *         schema:
 *           type: string
 *         description: 厂商筛选
 *       - in: query
 *         name: wifiState
 *         schema:
 *           type: string
 *         description: WiFi状态筛选
 *     responses:
 *       200:
 *         description: 获取设备列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取设备列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     devices:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Device'
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 */
router.get('/', authMiddleware, deviceValidationRules.getDeviceList, asyncHandler(deviceController.getDeviceList));

/**
 * @swagger
 * /api/v1/devices/search-options:
 *   get:
 *     summary: 获取设备搜索选项
 *     tags: [设备管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取设备搜索选项成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取设备搜索选项成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     deviceModels:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             description: 设备类型ID
 *                           name:
 *                             type: string
 *                             description: 设备类型名称
 *                     productionLines:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             description: 车间产线ID
 *                           name:
 *                             type: string
 *                             description: 车间产线名称
 *                     groups:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             description: 机器分组ID
 *                           name:
 *                             type: string
 *                             description: 机器分组名称
 */
router.get('/search-options', authMiddleware, asyncHandler(deviceController.getSearchOptions));

/**
 * @swagger
 * /api/v1/devices/stats:
 *   get:
 *     summary: 获取设备统计信息
 *     tags: [设备管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取设备统计信息成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取设备统计信息成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       description: 总设备数
 *                     online:
 *                       type: integer
 *                       description: 在线设备数
 *                     offline:
 *                       type: integer
 *                       description: 离线设备数
 *                     byModel:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           model:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     byVendor:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           vendor:
 *                             type: string
 *                           count:
 *                             type: integer
 */
router.get('/stats', authMiddleware, asyncHandler(deviceController.getDeviceStats));

/**
 * @swagger
 * /api/v1/devices/{id}:
 *   get:
 *     summary: 获取设备详情
 *     tags: [设备管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 设备ID
 *     responses:
 *       200:
 *         description: 获取设备详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取设备详情成功
 *                 data:
 *                   $ref: '#/components/schemas/Device'
 *       404:
 *         description: 设备不存在
 */
router.get('/:id', authMiddleware, deviceValidationRules.getDeviceById, asyncHandler(deviceController.getDeviceById));

/**
 * @swagger
 * /api/v1/devices:
 *   post:
 *     summary: 创建设备
 *     tags: [设备管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: 设备名称
 *               code:
 *                 type: string
 *                 description: 机器编号
 *               sn:
 *                 type: string
 *                 description: 机器SN
 *               mac:
 *                 type: string
 *                 description: 机器Mac地址
 *               ip:
 *                 type: string
 *                 description: 机器IP
 *               remark:
 *                 type: string
 *                 description: 备注
 *               controlModel:
 *                 type: string
 *                 description: 电控
 *               deviceModel:
 *                 type: string
 *                 description: 机型
 *               vendor:
 *                 type: string
 *                 description: 电控厂商
 *     responses:
 *       201:
 *         description: 创建设备成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 创建设备成功
 *                 data:
 *                   $ref: '#/components/schemas/Device'
 */
router.post('/', authMiddleware, deviceValidationRules.createDevice, asyncHandler(deviceController.createDevice));

/**
 * @swagger
 * /api/v1/devices/{id}:
 *   put:
 *     summary: 更新设备
 *     tags: [设备管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 设备ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 设备名称
 *               code:
 *                 type: string
 *                 description: 机器编号
 *               sn:
 *                 type: string
 *                 description: 机器SN
 *               mac:
 *                 type: string
 *                 description: 机器Mac地址
 *               ip:
 *                 type: string
 *                 description: 机器IP
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 更新设备成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新设备成功
 *                 data:
 *                   $ref: '#/components/schemas/Device'
 */
router.put('/:id', authMiddleware, deviceValidationRules.updateDevice, asyncHandler(deviceController.updateDevice));

/**
 * @swagger
 * /api/v1/devices/{id}:
 *   delete:
 *     summary: 删除设备
 *     tags: [设备管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 设备ID
 *     responses:
 *       200:
 *         description: 删除设备成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除设备成功
 */
router.delete('/:id', authMiddleware, deviceValidationRules.deleteDevice, asyncHandler(deviceController.deleteDevice));

/**
 * @swagger
 * /api/v1/devices/{id}/wifi:
 *   put:
 *     summary: 更新设备WiFi配置
 *     tags: [设备管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 设备ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               wifiSsid:
 *                 type: string
 *                 description: WiFi名称
 *               wifiState:
 *                 type: string
 *                 description: WiFi连接状态
 *               wifiSignalLevel:
 *                 type: string
 *                 description: WiFi信号强度
 *     responses:
 *       200:
 *         description: 更新WiFi配置成功
 */
router.put('/:id/wifi', authMiddleware, deviceValidationRules.updateWifiConfig, asyncHandler(deviceController.updateWifiConfig));



export default router;
