/**
 * 部门控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 20:53:00 +08:00; Reason: Task-006 用户管理模块, 创建部门控制器; Principle_Applied: MVC架构设计;}}
 */

import { Request, Response, NextFunction } from 'express';
import { query, param, body, validationResult } from 'express-validator';
import { DepartmentService } from './department.service';
import { createApiError } from '@/shared/utils/error';

export class DepartmentController {
  private departmentService: DepartmentService;

  constructor() {
    this.departmentService = new DepartmentService();
  }

  /**
   * 获取部门树验证规则
   */
  public getDepartmentTreeValidation = [
    query('enterpriseId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('企业ID必须是大于0的整数'),
  ];

  /**
   * 获取部门树
   */
  public getDepartmentTree = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const enterpriseId = req.query.enterpriseId ? parseInt(req.query.enterpriseId as string) : undefined;
      const tree = await this.departmentService.getDepartmentTree(req.user.userId, enterpriseId);

      res.json({
        code: 200,
        message: '获取部门树成功',
        data: tree,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取部门详情验证规则
   */
  public getDepartmentByIdValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('部门ID必须是大于0的整数'),
  ];

  /**
   * 获取部门详情
   */
  public getDepartmentById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      const department = await this.departmentService.getDepartmentById(id, req.user.userId);

      res.json({
        code: 200,
        message: '获取部门详情成功',
        data: department,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取企业信息验证规则
   */
  public getEnterpriseInfoValidation = [
    query('enterpriseId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('企业ID必须是大于0的整数'),
  ];

  /**
   * 获取企业信息
   */
  public getEnterpriseInfo = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const enterpriseId = req.query.enterpriseId ? parseInt(req.query.enterpriseId as string) : undefined;
      const enterprise = await this.departmentService.getEnterpriseInfo(req.user.userId, enterpriseId);

      res.json({
        code: 200,
        message: '获取企业信息成功',
        data: enterprise,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 创建部门验证规则
   */
  public createDepartmentValidation = [
    body('name')
      .notEmpty()
      .withMessage('部门名称不能为空')
      .isLength({ min: 2, max: 100 })
      .withMessage('部门名称长度必须在2-100个字符之间'),
    body('parentId')
      .optional({ nullable: true })
      .custom((value) => {
        if (value === null || value === undefined) {
          return true;
        }
        if (!Number.isInteger(value) || value < 1) {
          throw new Error('父部门ID必须是大于0的整数');
        }
        return true;
      }),
    body('description')
      .optional()
      .isLength({ max: 500 })
      .withMessage('部门描述不能超过500个字符'),
    body('sortOrder')
      .optional()
      .isInt({ min: 0 })
      .withMessage('排序号必须是非负整数'),
  ];

  /**
   * 创建部门
   */
  public createDepartment = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const department = await this.departmentService.createDepartment(req.user.userId, req.body);

      res.status(201).json({
        code: 201,
        message: '创建部门成功',
        data: department,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 更新部门验证规则
   */
  public updateDepartmentValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('部门ID必须是大于0的整数'),
    body('name')
      .optional()
      .isLength({ min: 2, max: 100 })
      .withMessage('部门名称长度必须在2-100个字符之间'),
    body('parentId')
      .optional({ nullable: true })
      .custom((value) => {
        if (value === null || value === undefined) {
          return true;
        }
        if (!Number.isInteger(value) || value < 1) {
          throw new Error('父部门ID必须是大于0的整数');
        }
        return true;
      }),
    body('description')
      .optional()
      .isLength({ max: 500 })
      .withMessage('部门描述不能超过500个字符'),
    body('sortOrder')
      .optional()
      .isInt({ min: 0 })
      .withMessage('排序号必须是非负整数'),
  ];

  /**
   * 更新部门
   */
  public updateDepartment = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      const department = await this.departmentService.updateDepartment(id, req.user.userId, req.body);

      res.json({
        code: 200,
        message: '更新部门成功',
        data: department,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 删除部门验证规则
   */
  public deleteDepartmentValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('部门ID必须是大于0的整数'),
  ];

  /**
   * 删除部门
   */
  public deleteDepartment = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      await this.departmentService.deleteDepartment(id, req.user.userId);

      res.json({
        code: 200,
        message: '删除部门成功',
      });
    } catch (error) {
      next(error);
    }
  };
}
