/**
 * 部门路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 20:53:00 +08:00; Reason: Task-006 用户管理模块, 创建部门路由; Principle_Applied: RESTful API设计;}}
 */

import { Router } from 'express';
import { DepartmentController } from './department.controller';
import { authMiddleware, permissionMiddleware } from '@/shared/middleware/auth.middleware';

const router = Router();
const departmentController = new DepartmentController();

/**
 * @route   GET /departments/tree
 * @desc    获取部门树形结构
 * @access  Private (需要 department:view 权限)
 */
router.get(
  '/tree',
  authMiddleware,
  permissionMiddleware(['department:view']),
  departmentController.getDepartmentTreeValidation,
  departmentController.getDepartmentTree
);

/**
 * @route   GET /departments/:id
 * @desc    获取部门详情
 * @access  Private (需要 department:view 权限)
 */
router.get(
  '/:id',
  authMiddleware,
  permissionMiddleware(['department:view']),
  departmentController.getDepartmentByIdValidation,
  departmentController.getDepartmentById
);

/**
 * @route   POST /departments
 * @desc    创建部门
 * @access  Private (需要 department:create 权限)
 */
router.post(
  '/',
  authMiddleware,
  permissionMiddleware(['department:create']),
  departmentController.createDepartmentValidation,
  departmentController.createDepartment
);

/**
 * @route   PUT /departments/:id
 * @desc    更新部门
 * @access  Private (需要 department:update 权限)
 */
router.put(
  '/:id',
  authMiddleware,
  permissionMiddleware(['department:update']),
  departmentController.updateDepartmentValidation,
  departmentController.updateDepartment
);

/**
 * @route   DELETE /departments/:id
 * @desc    删除部门
 * @access  Private (需要 department:delete 权限)
 */
router.delete(
  '/:id',
  authMiddleware,
  permissionMiddleware(['department:delete']),
  departmentController.deleteDepartmentValidation,
  departmentController.deleteDepartment
);

/**
 * @route   GET /enterprise/current
 * @desc    获取当前企业信息
 * @access  Private (需要认证)
 */
router.get(
  '/enterprise/current',
  authMiddleware,
  departmentController.getEnterpriseInfoValidation,
  departmentController.getEnterpriseInfo
);

export { router as departmentRoutes };
