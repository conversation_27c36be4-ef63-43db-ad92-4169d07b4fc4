/**
 * 部门服务层
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 20:53:00 +08:00; Reason: Task-006 用户管理模块, 创建部门服务; Principle_Applied: 业务逻辑分层;}}
 */

import { Op } from 'sequelize';
import { logger } from '@/shared/utils/logger';
import { createApiError } from '@/shared/utils/error';
import {
  Department,
  Enterprise,
  User,
  Role
} from '@/shared/database/models';
import { formatDateTime } from '@/shared/utils/dateTime';

export interface DepartmentTreeNode {
  id: number;
  name: string;
  enterpriseId: number;
  parentId?: number;
  level: number;
  path: string;
  sortOrder: number;
  description?: string;
  userCount: number;
  children?: DepartmentTreeNode[];
}

export interface CreateDepartmentRequest {
  name: string;
  parentId?: number;
  description?: string;
  sortOrder?: number;
}

export interface UpdateDepartmentRequest {
  name?: string;
  parentId?: number;
  description?: string;
  sortOrder?: number;
}

export class DepartmentService {
  /**
   * 获取部门树形结构
   */
  async getDepartmentTree(currentUserId: number, enterpriseId?: number): Promise<DepartmentTreeNode[]> {
    try {
      // 获取当前用户信息以确定权限范围
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      
      // 构建查询条件
      const whereConditions: any = {};

      // 非超级管理员只能查看同企业部门
      if (!isSuperAdmin) {
        whereConditions.enterpriseId = currentUser.enterpriseId;
      } else if (enterpriseId) {
        whereConditions.enterpriseId = enterpriseId;
      }

      // 获取所有部门
      const departments = await Department.findAll({
        where: whereConditions,
        include: [
          {
            model: Enterprise,
            as: 'enterprise',
            attributes: ['id', 'name', 'code'],
          },
        ],
        order: [['level', 'ASC'], ['sortOrder', 'ASC']],
      });

      // 转换为树形结构（先不计算用户数量）
      const departmentNodes: DepartmentTreeNode[] = departments.map(dept => ({
        id: dept.id,
        name: dept.name,
        enterpriseId: dept.enterpriseId,
        parentId: dept.parentId,
        level: dept.level,
        path: dept.path,
        sortOrder: dept.sortOrder,
        description: dept.description,
        userCount: 0, // 先设为0，后面递归计算
        children: [],
      }));

      // 构建树形结构
      const tree = this.buildDepartmentTree(departmentNodes);

      // 递归计算每个部门及其子部门的用户总数
      await this.calculateDepartmentUserCounts(tree);

      logger.info('获取部门树成功', {
        userId: currentUserId,
        enterpriseId: enterpriseId || currentUser.enterpriseId,
        departmentCount: departments.length,
      });

      return tree;
    } catch (error) {
      logger.error('获取部门树失败', error);
      throw error;
    }
  }

  /**
   * 构建部门树形结构
   */
  private buildDepartmentTree(departments: DepartmentTreeNode[]): DepartmentTreeNode[] {
    const departmentMap = new Map<number, DepartmentTreeNode>();
    const rootDepartments: DepartmentTreeNode[] = [];

    // 创建部门映射
    departments.forEach(dept => {
      departmentMap.set(dept.id, { ...dept, children: [] });
    });

    // 构建树形结构
    departments.forEach(dept => {
      const departmentNode = departmentMap.get(dept.id)!;
      
      if (dept.parentId) {
        const parent = departmentMap.get(dept.parentId);
        if (parent) {
          parent.children!.push(departmentNode);
        } else {
          // 如果找不到父部门，作为根部门处理
          rootDepartments.push(departmentNode);
        }
      } else {
        rootDepartments.push(departmentNode);
      }
    });

    // 递归排序子部门
    const sortChildren = (nodes: DepartmentTreeNode[]) => {
      nodes.sort((a, b) => a.sortOrder - b.sortOrder);
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          sortChildren(node.children);
        }
      });
    };

    sortChildren(rootDepartments);

    return rootDepartments;
  }

  /**
   * 递归计算部门及其子部门的用户总数
   */
  private async calculateDepartmentUserCounts(departments: DepartmentTreeNode[]): Promise<void> {
    for (const dept of departments) {
      // 计算当前部门的直接用户数量
      const directUserCount = await User.count({
        where: { departmentId: dept.id },
      });

      // 递归计算子部门的用户数量
      if (dept.children && dept.children.length > 0) {
        await this.calculateDepartmentUserCounts(dept.children);

        // 计算子部门的用户总数
        const childrenUserCount = dept.children.reduce((total, child) => total + child.userCount, 0);

        // 当前部门的用户总数 = 直接用户数 + 子部门用户总数
        dept.userCount = directUserCount + childrenUserCount;
      } else {
        // 没有子部门，用户数量就是直接用户数量
        dept.userCount = directUserCount;
      }
    }
  }

  /**
   * 获取部门详情
   */
  async getDepartmentById(id: number, currentUserId: number): Promise<any> {
    try {
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      const department = await Department.findByPk(id, {
        include: [
          {
            model: Enterprise,
            as: 'enterprise',
            attributes: ['id', 'name', 'code'],
          },
          {
            model: Department,
            as: 'parent',
            attributes: ['id', 'name'],
          },
          {
            model: Department,
            as: 'children',
            attributes: ['id', 'name', 'level', 'sortOrder'],
          },
        ],
      });

      if (!department) {
        throw createApiError('部门不存在', 404, 'DEPARTMENT_NOT_FOUND');
      }

      // 非超级管理员只能查看同企业部门
      if (!isSuperAdmin && department.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('权限不足', 403, 'PERMISSION_DENIED');
      }

      // 获取部门用户数量
      const userCount = await User.count({
        where: { departmentId: id },
      });

      logger.info('获取部门详情成功', { userId: currentUserId, departmentId: id });

      return {
        id: department.id,
        name: department.name,
        enterpriseId: department.enterpriseId,
        parentId: department.parentId,
        level: department.level,
        path: department.path,
        sortOrder: department.sortOrder,
        description: department.description,
        createdAt: formatDateTime(department.createdAt),
        updatedAt: formatDateTime(department.updatedAt),
        enterprise: department.enterprise,
        parent: department.parent,
        children: department.children,
        userCount,
      };
    } catch (error) {
      logger.error('获取部门详情失败', error);
      throw error;
    }
  }

  /**
   * 获取企业信息（用于顶部企业信息展示）
   */
  async getEnterpriseInfo(currentUserId: number, enterpriseId?: number): Promise<any> {
    try {
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
          {
            model: Enterprise,
            as: 'enterprise',
            attributes: ['id', 'name', 'code', 'logoUrl', 'description'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      
      let targetEnterpriseId = enterpriseId;
      
      // 非超级管理员只能查看自己的企业
      if (!isSuperAdmin) {
        targetEnterpriseId = currentUser.enterpriseId;
      } else if (!enterpriseId) {
        targetEnterpriseId = currentUser.enterpriseId;
      }

      const enterprise = await Enterprise.findByPk(targetEnterpriseId);
      
      if (!enterprise) {
        throw createApiError('企业不存在', 404, 'ENTERPRISE_NOT_FOUND');
      }

      // 获取企业统计信息
      const [userCount, departmentCount] = await Promise.all([
        User.count({ where: { enterpriseId: targetEnterpriseId } }),
        Department.count({ where: { enterpriseId: targetEnterpriseId } }),
      ]);

      logger.info('获取企业信息成功', { 
        userId: currentUserId, 
        enterpriseId: targetEnterpriseId 
      });

      return {
        id: enterprise.id,
        name: enterprise.name,
        code: enterprise.code,
        logoUrl: enterprise.logoUrl,
        description: enterprise.description,
        status: enterprise.status,
        createdAt: formatDateTime(enterprise.createdAt),
        updatedAt: formatDateTime(enterprise.updatedAt),
        statistics: {
          userCount,
          departmentCount,
        },
      };
    } catch (error) {
      logger.error('获取企业信息失败', error);
      throw error;
    }
  }

  /**
   * 创建部门
   */
  async createDepartment(currentUserId: number, data: CreateDepartmentRequest): Promise<any> {
    try {
      // 获取当前用户信息以确定权限范围
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      // 非超级管理员只能在自己的企业下创建部门
      if (!isSuperAdmin && !currentUser.enterpriseId) {
        throw createApiError('当前用户没有关联企业，无法创建部门', 403, 'NO_ENTERPRISE');
      }

      const enterpriseId = currentUser.enterpriseId!;

      // 检查部门名称在同一企业下是否唯一
      const existingDepartment = await Department.findOne({
        where: {
          name: data.name,
          enterpriseId,
          ...(data.parentId ? { parentId: data.parentId } : { parentId: null })
        }
      });

      if (existingDepartment) {
        throw createApiError('同级部门名称不能重复', 400, 'DEPARTMENT_NAME_EXISTS');
      }

      // 处理父部门和层级
      let level = 1;
      let path = '';
      let parentDepartment = null;

      if (data.parentId) {
        parentDepartment = await Department.findOne({
          where: { id: data.parentId, enterpriseId }
        });

        if (!parentDepartment) {
          throw createApiError('父部门不存在', 404, 'PARENT_DEPARTMENT_NOT_FOUND');
        }

        level = parentDepartment.level + 1;
        path = `${parentDepartment.path}/${data.parentId}`;
      } else {
        path = '';
      }

      // 获取同级部门的最大排序号
      const maxSortOrder = await Department.max('sortOrder', {
        where: {
          enterpriseId,
          parentId: data.parentId || null
        }
      }) as number || 0;

      // 创建部门
      const department = await Department.create({
        name: data.name,
        enterpriseId,
        parentId: data.parentId,
        description: data.description,
        level,
        path,
        sortOrder: data.sortOrder || (maxSortOrder + 1)
      });

      logger.info('创建部门成功', {
        userId: currentUserId,
        departmentId: department.id,
        departmentName: data.name,
        enterpriseId
      });

      return {
        id: department.id,
        name: department.name,
        enterpriseId: department.enterpriseId,
        parentId: department.parentId,
        level: department.level,
        path: department.path,
        sortOrder: department.sortOrder,
        description: department.description,
        createdAt: formatDateTime(department.createdAt),
        updatedAt: formatDateTime(department.updatedAt),
        userCount: 0
      };
    } catch (error) {
      logger.error('创建部门失败', error);
      throw error;
    }
  }

  /**
   * 更新部门
   */
  async updateDepartment(id: number, currentUserId: number, data: UpdateDepartmentRequest): Promise<any> {
    try {
      // 获取当前用户信息以确定权限范围
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      // 获取要更新的部门
      const department = await Department.findByPk(id);
      if (!department) {
        throw createApiError('部门不存在', 404, 'DEPARTMENT_NOT_FOUND');
      }

      // 权限检查：非超级管理员只能更新自己企业的部门
      if (!isSuperAdmin && department.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('无权限更新此部门', 403, 'PERMISSION_DENIED');
      }

      // 如果要修改名称，检查同级部门名称唯一性
      if (data.name && data.name !== department.name) {
        const existingDepartment = await Department.findOne({
          where: {
            name: data.name,
            enterpriseId: department.enterpriseId,
            parentId: data.parentId !== undefined ? data.parentId : department.parentId,
            id: { [Op.ne]: id }
          }
        });

        if (existingDepartment) {
          throw createApiError('同级部门名称不能重复', 400, 'DEPARTMENT_NAME_EXISTS');
        }
      }

      // 如果要修改父部门，需要处理层级关系
      let updateData: any = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.sortOrder !== undefined) updateData.sortOrder = data.sortOrder;

      // 处理父部门变更
      if (data.parentId !== undefined && data.parentId !== department.parentId) {
        // 检查不能将部门设置为自己的子部门
        if (data.parentId) {
          const parentDepartment = await Department.findOne({
            where: { id: data.parentId, enterpriseId: department.enterpriseId }
          });

          if (!parentDepartment) {
            throw createApiError('父部门不存在', 404, 'PARENT_DEPARTMENT_NOT_FOUND');
          }

          // 检查是否会形成循环引用
          if (parentDepartment.path.includes(`/${id}/`) || parentDepartment.id === id) {
            throw createApiError('不能将部门设置为自己的子部门', 400, 'CIRCULAR_REFERENCE');
          }

          updateData.parentId = data.parentId;
          updateData.level = parentDepartment.level + 1;
          updateData.path = `${parentDepartment.path}/${data.parentId}`;
        } else {
          // 设置为根部门
          updateData.parentId = null;
          updateData.level = 1;
          updateData.path = '';
        }
      }

      // 更新部门
      await department.update(updateData);

      // 如果修改了层级关系，需要更新所有子部门的层级信息
      if (updateData.level !== undefined) {
        await this.updateChildrenPath(id, department.enterpriseId);
      }

      logger.info('更新部门成功', {
        userId: currentUserId,
        departmentId: id,
        updateData
      });

      // 重新获取更新后的部门信息
      const updatedDepartment = await Department.findByPk(id);
      const userCount = await User.count({ where: { departmentId: id } });

      return {
        id: updatedDepartment!.id,
        name: updatedDepartment!.name,
        enterpriseId: updatedDepartment!.enterpriseId,
        parentId: updatedDepartment!.parentId,
        level: updatedDepartment!.level,
        path: updatedDepartment!.path,
        sortOrder: updatedDepartment!.sortOrder,
        description: updatedDepartment!.description,
        createdAt: formatDateTime(updatedDepartment!.createdAt),
        updatedAt: formatDateTime(updatedDepartment!.updatedAt),
        userCount
      };
    } catch (error) {
      logger.error('更新部门失败', error);
      throw error;
    }
  }

  /**
   * 删除部门
   */
  async deleteDepartment(id: number, currentUserId: number): Promise<void> {
    try {
      // 获取当前用户信息以确定权限范围
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      // 获取要删除的部门
      const department = await Department.findByPk(id);
      if (!department) {
        throw createApiError('部门不存在', 404, 'DEPARTMENT_NOT_FOUND');
      }

      // 权限检查：非超级管理员只能删除自己企业的部门
      if (!isSuperAdmin && department.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('无权限删除此部门', 403, 'PERMISSION_DENIED');
      }

      // 检查是否有子部门
      const childrenCount = await Department.count({
        where: { parentId: id }
      });

      if (childrenCount > 0) {
        throw createApiError('该部门下还有子部门，无法删除', 400, 'HAS_CHILDREN');
      }

      // 检查是否有用户
      const userCount = await User.count({
        where: { departmentId: id }
      });

      if (userCount > 0) {
        throw createApiError('该部门下还有用户，无法删除', 400, 'HAS_USERS');
      }

      // 删除部门
      await department.destroy();

      logger.info('删除部门成功', {
        userId: currentUserId,
        departmentId: id,
        departmentName: department.name
      });
    } catch (error) {
      logger.error('删除部门失败', error);
      throw error;
    }
  }

  /**
   * 获取部门及其所有子部门的ID列表
   */
  async getDepartmentAndChildrenIds(departmentId: number): Promise<number[]> {
    try {
      const department = await Department.findByPk(departmentId);
      if (!department) {
        return [];
      }

      const departmentIds: number[] = [departmentId];

      // 递归获取所有子部门ID
      const getChildrenIds = async (parentId: number): Promise<void> => {
        const children = await Department.findAll({
          where: { parentId },
          attributes: ['id']
        });

        for (const child of children) {
          departmentIds.push(child.id);
          await getChildrenIds(child.id);
        }
      };

      await getChildrenIds(departmentId);
      return departmentIds;
    } catch (error) {
      logger.error('获取部门及子部门ID失败', error);
      return [departmentId]; // 出错时至少返回当前部门ID
    }
  }

  /**
   * 更新子部门的路径信息（当父部门层级发生变化时）
   */
  private async updateChildrenPath(parentId: number, enterpriseId: number): Promise<void> {
    const children = await Department.findAll({
      where: { parentId, enterpriseId }
    });

    for (const child of children) {
      const parent = await Department.findByPk(parentId);
      if (parent) {
        const newPath = parent.path ? `${parent.path}/${parentId}` : `/${parentId}`;
        const newLevel = parent.level + 1;

        await child.update({
          path: newPath,
          level: newLevel
        });

        // 递归更新子部门的子部门
        await this.updateChildrenPath(child.id, enterpriseId);
      }
    }
  }
}
