/**
 * 微信服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 11:51:06 +08:00; Reason: Task-003 微信服务层开发, 创建微信OAuth2.0服务; Principle_Applied: 业务逻辑分离;}}
 */

import { User } from '@/shared/database/models';
import { createApiError } from '@/shared/middleware/error.middleware';
import { logger } from '@/shared/utils/logger';
import { config } from '@/config/app.config';
import axios from 'axios';
import crypto from 'crypto';

// 微信用户信息接口
export interface WechatUserInfo {
  openid: string;
  unionid?: string;
  nickname: string;
  headimgurl: string;
  sex: number;
  province?: string;
  city?: string;
  country?: string;
}

// 微信授权URL响应接口
export interface WechatAuthUrlResponse {
  authUrl: string;
  state: string;
}

// 微信绑定请求接口
export interface WechatBindRequest {
  code: string;
  state: string;
  userId: number;
}

// 微信绑定响应接口
export interface WechatBindResponse {
  success: boolean;
  message: string;
  userInfo?: {
    wechatOpenid: string;
    wechatUnionid?: string;
    wechatNickname: string;
    wechatAvatar: string;
    wechatBoundAt: string;
  };
}

// 微信解绑响应接口
export interface WechatUnbindResponse {
  success: boolean;
  message: string;
}

// 微信访问令牌响应
interface WechatAccessTokenResponse {
  access_token: string;
  expires_in: number;
  refresh_token: string;
  openid: string;
  scope: string;
  unionid?: string;
}

// 微信API错误响应
interface WechatApiErrorResponse {
  errcode: number;
  errmsg: string;
}

export class WechatService {
  private readonly wechatApiBaseUrl = 'https://api.weixin.qq.com';
  private readonly wechatAuthBaseUrl = 'https://open.weixin.qq.com';

  /**
   * 生成微信授权URL
   */
  async generateAuthUrl(): Promise<WechatAuthUrlResponse> {
    try {
      logger.info('开始生成微信授权URL');

      // 验证微信配置
      if (!config.wechat.appId || !config.wechat.appSecret) {
        throw createApiError('微信配置不完整', 500, 'WECHAT_CONFIG_MISSING');
      }

      // 生成随机state参数，用于防CSRF攻击
      const state = crypto.randomBytes(16).toString('hex');

      // 构建微信授权URL
      const authUrl = `${this.wechatAuthBaseUrl}/connect/oauth2/authorize` +
        `?appid=${config.wechat.appId}` +
        `&redirect_uri=${encodeURIComponent(config.wechat.redirectUri)}` +
        `&response_type=code` +
        `&scope=snsapi_userinfo` +
        `&state=${state}` +
        `#wechat_redirect`;

      logger.info('微信授权URL生成成功', { state });

      return {
        authUrl,
        state
      };
    } catch (error) {
      logger.error('生成微信授权URL失败', { error: error instanceof Error ? error.message : '未知错误' });
      throw error;
    }
  }

  /**
   * 处理微信授权回调
   */
  async handleCallback(code: string, state: string): Promise<WechatUserInfo> {
    try {
      logger.info('开始处理微信授权回调', { code: code.substring(0, 10) + '...', state });

      // 验证参数
      if (!code || !state) {
        throw createApiError('授权参数不完整', 400, 'INVALID_CALLBACK_PARAMS');
      }

      // 获取访问令牌
      const tokenResponse = await this.getAccessToken(code);
      
      // 获取用户信息
      const userInfo = await this.getUserInfo(tokenResponse.access_token, tokenResponse.openid);

      logger.info('微信授权回调处理成功', { openid: userInfo.openid });

      return userInfo;
    } catch (error) {
      logger.error('处理微信授权回调失败', { error: error instanceof Error ? error.message : '未知错误' });
      throw error;
    }
  }

  /**
   * 绑定微信到用户账号
   */
  async bindWechat(request: WechatBindRequest): Promise<WechatBindResponse> {
    try {
      logger.info('开始绑定微信账号', { userId: request.userId });

      // 处理微信授权回调获取用户信息
      const wechatUserInfo = await this.handleCallback(request.code, request.state);

      // 检查该微信账号是否已被其他用户绑定
      const existingUser = await User.findOne({
        where: {
          wechatOpenid: wechatUserInfo.openid
        }
      });

      if (existingUser && existingUser.id !== request.userId) {
        throw createApiError('该微信账号已绑定其他用户', 400, 'WECHAT_ALREADY_BOUND');
      }

      // 查找当前用户
      const user = await User.findByPk(request.userId);
      if (!user) {
        throw createApiError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      // 检查用户是否已绑定微信
      if (user.wechatOpenid) {
        throw createApiError('用户已绑定微信账号', 400, 'USER_ALREADY_BOUND');
      }

      // 更新用户微信信息
      const updateData: any = {
        wechatOpenid: wechatUserInfo.openid,
        wechatNickname: wechatUserInfo.nickname,
        wechatAvatar: wechatUserInfo.headimgurl,
        wechatBoundAt: new Date()
      };

      if (wechatUserInfo.unionid) {
        updateData.wechatUnionid = wechatUserInfo.unionid;
      }

      await user.update(updateData);

      logger.info('微信账号绑定成功', { 
        userId: request.userId, 
        openid: wechatUserInfo.openid 
      });

      return {
        success: true,
        message: '微信绑定成功',
        userInfo: {
          wechatOpenid: wechatUserInfo.openid,
          wechatUnionid: wechatUserInfo.unionid || undefined,
          wechatNickname: wechatUserInfo.nickname,
          wechatAvatar: wechatUserInfo.headimgurl,
          wechatBoundAt: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('绑定微信账号失败', {
        userId: request.userId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 解绑微信账号
   */
  async unbindWechat(userId: number): Promise<WechatUnbindResponse> {
    try {
      logger.info('开始解绑微信账号', { userId });

      // 查找用户
      const user = await User.findByPk(userId);
      if (!user) {
        throw createApiError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      // 检查用户是否已绑定微信
      if (!user.wechatOpenid) {
        throw createApiError('用户未绑定微信账号', 400, 'USER_NOT_BOUND');
      }

      // 清除用户微信信息
      await user.update({
        wechatOpenid: null as any,
        wechatUnionid: null as any,
        wechatNickname: null as any,
        wechatAvatar: null as any,
        wechatBoundAt: null as any
      });

      logger.info('微信账号解绑成功', { userId });

      return {
        success: true,
        message: '微信解绑成功'
      };
    } catch (error) {
      logger.error('解绑微信账号失败', { userId, error: error instanceof Error ? error.message : '未知错误' });
      throw error;
    }
  }

  /**
   * 获取微信访问令牌
   */
  private async getAccessToken(code: string): Promise<WechatAccessTokenResponse> {
    try {
      const url = `${this.wechatApiBaseUrl}/sns/oauth2/access_token` +
        `?appid=${config.wechat.appId}` +
        `&secret=${config.wechat.appSecret}` +
        `&code=${code}` +
        `&grant_type=authorization_code`;

      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'EmbroideryManagement/1.0'
        }
      });

      const data = response.data;

      // 检查微信API错误
      if (data.errcode) {
        const errorResponse = data as WechatApiErrorResponse;
        throw createApiError(
          `微信API错误: ${errorResponse.errmsg}`,
          400,
          'WECHAT_API_ERROR',
          { errcode: errorResponse.errcode }
        );
      }

      return data as WechatAccessTokenResponse;
    } catch (error: any) {
      if (error.code === 'WECHAT_API_ERROR') {
        throw error;
      }
      logger.error('获取微信访问令牌失败', { error: error instanceof Error ? error.message : '未知错误' });
      throw createApiError('获取微信访问令牌失败', 500, 'GET_ACCESS_TOKEN_FAILED');
    }
  }

  /**
   * 获取微信用户信息
   */
  private async getUserInfo(accessToken: string, openid: string): Promise<WechatUserInfo> {
    try {
      const url = `${this.wechatApiBaseUrl}/sns/userinfo` +
        `?access_token=${accessToken}` +
        `&openid=${openid}` +
        `&lang=zh_CN`;

      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'EmbroideryManagement/1.0'
        }
      });

      const data = response.data;

      // 检查微信API错误
      if (data.errcode) {
        const errorResponse = data as WechatApiErrorResponse;
        throw createApiError(
          `微信API错误: ${errorResponse.errmsg}`,
          400,
          'WECHAT_API_ERROR',
          { errcode: errorResponse.errcode }
        );
      }

      return data as WechatUserInfo;
    } catch (error: any) {
      if (error.code === 'WECHAT_API_ERROR') {
        throw error;
      }
      logger.error('获取微信用户信息失败', { error: error instanceof Error ? error.message : '未知错误' });
      throw createApiError('获取微信用户信息失败', 500, 'GET_USER_INFO_FAILED');
    }
  }
}
