import { Op, WhereOptions } from 'sequelize';
import { Pattern } from '../../shared/database/models/Pattern';
import { Tag, TagType, TagStatus } from '../../shared/database/models/Tag';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

// 花样列表查询参数
export interface PatternListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  groupId?: number;
  fileType?: string;
  createUser?: string;
  enterpriseId: number;
}

// 花样列表响应
export interface PatternListResponse {
  patterns: Pattern[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 花样搜索选项
export interface PatternSearchOptions {
  groups: Array<{ id: number; name: string }>;
  fileTypes: Array<{ label: string; value: string }>;
  createUsers: Array<{ label: string; value: string }>;
}

// 创建花样请求
export interface CreatePatternRequest {
  code?: string;
  name: string;
  remark?: string;
  groupId?: number;
  fileType?: string;
  stitch?: number;
  jumps?: number;
  trim?: number;
  colors?: number;
  baseLine?: number;
  surfaceLine?: number;
  goldPieceNum?: number;
  minX?: number;
  maxX?: number;
  minY?: number;
  maxY?: number;
  width?: number;
  height?: number;
  goldPieceLine?: string;
  needle?: string;
  mergeGoldPieceLine?: string;
  image?: string;
  createUser?: string;
}

// 更新花样请求
export interface UpdatePatternRequest extends Partial<CreatePatternRequest> {}

// 花样统计信息
export interface PatternStats {
  total: number;
  byFileType: Array<{ fileType: string; count: number }>;
  byGroup: Array<{ groupName: string; count: number }>;
  byCreateUser: Array<{ createUser: string; count: number }>;
}

/**
 * 花样服务类
 */
export class PatternService {
  /**
   * 获取花样列表
   */
  async getPatternList(query: PatternListQuery): Promise<PatternListResponse> {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
        groupId,
        fileType,
        createUser,
        enterpriseId
      } = query;

      const offset = (page - 1) * pageSize;
      const limit = pageSize;

      // 构建查询条件
      const whereConditions: WhereOptions = {
        enterpriseId
      };

      // 搜索条件
      if (search) {
        whereConditions[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { code: { [Op.iLike]: `%${search}%` } },
          { remark: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // 筛选条件
      if (groupId) {
        whereConditions.groupId = groupId;
      }
      if (fileType) {
        whereConditions.fileType = fileType;
      }
      if (createUser) {
        whereConditions.createUser = createUser;
      }

      const { count, rows } = await Pattern.findAndCountAll({
        where: whereConditions,
        offset,
        limit,
        order: [['updatedAt', 'DESC']],
        include: [
          {
            association: 'group',
            attributes: ['id', 'name'],
            required: false,
          }
        ]
      });

      const totalPages = Math.ceil(count / pageSize);

      logger.info('获取花样列表成功', {
        enterpriseId,
        total: count,
        page,
        pageSize
      });

      return {
        patterns: rows,
        total: count,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      logger.error('获取花样列表失败', {
        query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据ID获取花样详情
   */
  async getPatternById(id: number, enterpriseId: number): Promise<Pattern> {
    try {
      const pattern = await Pattern.findOne({
        where: { id, enterpriseId },
        include: [
          {
            association: 'group',
            attributes: ['id', 'name'],
            required: false,
          }
        ]
      });

      if (!pattern) {
        throw createApiError(404, '花样不存在');
      }

      logger.info('获取花样详情成功', { id, enterpriseId });
      return pattern;
    } catch (error) {
      logger.error('获取花样详情失败', {
        id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建花样
   */
  async createPattern(data: CreatePatternRequest, enterpriseId: number): Promise<Pattern> {
    try {
      // 检查花样编号是否重复
      if (data.code) {
        const existingPattern = await Pattern.findOne({
          where: { code: data.code, enterpriseId }
        });
        if (existingPattern) {
          throw createApiError(400, '花样编号已存在');
        }
      }

      const pattern = await Pattern.create({
        ...data,
        enterpriseId
      });

      logger.info('创建花样成功', {
        id: pattern.id,
        name: pattern.name,
        enterpriseId
      });

      return pattern;
    } catch (error) {
      logger.error('创建花样失败', {
        data,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新花样
   */
  async updatePattern(id: number, data: UpdatePatternRequest, enterpriseId: number): Promise<Pattern> {
    try {
      const pattern = await Pattern.findOne({
        where: { id, enterpriseId }
      });

      if (!pattern) {
        throw createApiError(404, '花样不存在');
      }

      // 检查花样编号是否重复
      if (data.code && data.code !== pattern.code) {
        const existingPattern = await Pattern.findOne({
          where: { code: data.code, enterpriseId }
        });
        if (existingPattern) {
          throw createApiError(400, '花样编号已存在');
        }
      }

      await pattern.update(data);

      logger.info('更新花样成功', {
        id,
        name: pattern.name,
        enterpriseId
      });

      return pattern;
    } catch (error) {
      logger.error('更新花样失败', {
        id,
        data,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除花样
   */
  async deletePattern(id: number, enterpriseId: number): Promise<void> {
    try {
      const pattern = await Pattern.findOne({
        where: { id, enterpriseId }
      });

      if (!pattern) {
        throw createApiError(404, '花样不存在');
      }

      await pattern.destroy();

      logger.info('删除花样成功', {
        id,
        name: pattern.name,
        enterpriseId
      });
    } catch (error) {
      logger.error('删除花样失败', {
        id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取花样搜索选项
   */
  async getSearchOptions(enterpriseId: number): Promise<PatternSearchOptions> {
    try {
      // 查询花样分组选项（只返回启用状态的分组）
      const groups = await Tag.findAll({
        where: {
          enterpriseId,
          type: TagType.PATTERN_GROUP,
          status: TagStatus.ENABLED
        },
        attributes: ['id', 'name'],
        order: [['name', 'ASC']]
      });

      // 查询文件类型选项
      const fileTypeResults = await Pattern.findAll({
        where: {
          enterpriseId,
          fileType: { [Op.ne]: null }
        },
        attributes: ['fileType'],
        group: ['file_type'],
        order: [['file_type', 'ASC']]
      });

      const fileTypes = fileTypeResults.map(item => ({
        label: item.fileType!,
        value: item.fileType!
      }));

      // 查询创建人选项
      const createUserResults = await Pattern.findAll({
        where: {
          enterpriseId,
          createUser: { [Op.ne]: null }
        },
        attributes: ['createUser'],
        group: ['create_user'],
        order: [['create_user', 'ASC']]
      });

      const createUsers = createUserResults.map(item => ({
        label: item.createUser!,
        value: item.createUser!
      }));

      return {
        groups: groups.map(item => ({
          id: item.id,
          name: item.name
        })),
        fileTypes,
        createUsers
      };
    } catch (error) {
      logger.error('获取花样搜索选项失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取文件类型选项
   */
  async getFileTypes(enterpriseId: number): Promise<Array<{ label: string; value: string }>> {
    try {
      const fileTypeResults = await Pattern.findAll({
        where: {
          enterpriseId,
          fileType: { [Op.ne]: null }
        },
        attributes: ['fileType'],
        group: ['file_type'],
        order: [['file_type', 'ASC']]
      });

      return fileTypeResults.map(item => ({
        label: item.fileType!,
        value: item.fileType!
      }));
    } catch (error) {
      logger.error('获取文件类型选项失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取创建人选项
   */
  async getCreateUsers(enterpriseId: number): Promise<Array<{ label: string; value: string }>> {
    try {
      const createUserResults = await Pattern.findAll({
        where: {
          enterpriseId,
          createUser: { [Op.ne]: null }
        },
        attributes: ['createUser'],
        group: ['create_user'],
        order: [['create_user', 'ASC']]
      });

      return createUserResults.map(item => ({
        label: item.createUser!,
        value: item.createUser!
      }));
    } catch (error) {
      logger.error('获取创建人选项失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取花样统计信息
   */
  async getPatternStats(enterpriseId: number): Promise<PatternStats> {
    try {
      // 总数统计
      const total = await Pattern.count({
        where: { enterpriseId }
      });

      // 按文件类型统计
      const byFileTypeResults = await Pattern.findAll({
        where: { enterpriseId },
        attributes: [
          'fileType',
          [Pattern.sequelize!.fn('COUNT', '*'), 'count']
        ],
        group: ['fileType'],
        having: { fileType: { [Op.ne]: null } },
        order: [[Pattern.sequelize!.literal('count'), 'DESC']]
      });

      const byFileType = byFileTypeResults.map(item => ({
        fileType: item.fileType!,
        count: parseInt((item as any).dataValues.count)
      }));

      // 按分组统计
      const byGroupResults = await Pattern.findAll({
        where: { enterpriseId },
        attributes: [
          [Pattern.sequelize!.fn('COUNT', '*'), 'count']
        ],
        include: [
          {
            association: 'group',
            attributes: ['name'],
            required: true,
          }
        ],
        group: ['group.id', 'group.name'],
        order: [[Pattern.sequelize!.literal('count'), 'DESC']]
      });

      const byGroup = byGroupResults.map(item => ({
        groupName: (item as any).group?.name || '未分组',
        count: parseInt((item as any).dataValues.count)
      }));

      // 按创建人统计
      const byCreateUserResults = await Pattern.findAll({
        where: { enterpriseId },
        attributes: [
          'createUser',
          [Pattern.sequelize!.fn('COUNT', '*'), 'count']
        ],
        group: ['createUser'],
        having: { createUser: { [Op.ne]: null } },
        order: [[Pattern.sequelize!.literal('count'), 'DESC']]
      });

      const byCreateUser = byCreateUserResults.map(item => ({
        createUser: item.createUser!,
        count: parseInt((item as any).dataValues.count)
      }));

      return {
        total,
        byFileType,
        byGroup,
        byCreateUser
      };
    } catch (error) {
      logger.error('获取花样统计信息失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}

export const patternService = new PatternService();
