import { Request, Response } from 'express';
import { patternService, PatternListQuery } from './pattern.service';
import { logger } from '../../shared/utils/logger';

/**
 * 花样控制器类
 */
export class PatternController {
  /**
   * 获取花样列表
   */
  async getPatternList(req: Request, res: Response): Promise<void> {
    try {
      const query: PatternListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 10,
        search: req.query.search as string,
        groupId: req.query.groupId ? parseInt(req.query.groupId as string) : undefined,
        fileType: req.query.fileType as string,
        createUser: req.query.createUser as string,
        enterpriseId: req.user!.enterpriseId
      };

      const result = await patternService.getPatternList(query);

      res.json({
        code: 200,
        message: '获取花样列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取花样列表失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        query: req.query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取花样详情
   */
  async getPatternById(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      const pattern = await patternService.getPatternById(id, enterpriseId);

      res.json({
        code: 200,
        message: '获取花样详情成功',
        data: pattern
      });
    } catch (error) {
      logger.error('获取花样详情失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        patternId: req.params.id,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建花样
   */
  async createPattern(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;
      const data = req.body;

      const pattern = await patternService.createPattern(data, enterpriseId);

      res.status(201).json({
        code: 201,
        message: '创建花样成功',
        data: pattern
      });
    } catch (error) {
      logger.error('创建花样失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        data: req.body,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新花样
   */
  async updatePattern(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const data = req.body;

      const pattern = await patternService.updatePattern(id, data, enterpriseId);

      res.json({
        code: 200,
        message: '更新花样成功',
        data: pattern
      });
    } catch (error) {
      logger.error('更新花样失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        patternId: req.params.id,
        data: req.body,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除花样
   */
  async deletePattern(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      await patternService.deletePattern(id, enterpriseId);

      res.json({
        code: 200,
        message: '删除花样成功'
      });
    } catch (error) {
      logger.error('删除花样失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        patternId: req.params.id,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取文件类型选项
   */
  async getFileTypes(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;

      const fileTypes = await patternService.getFileTypes(enterpriseId);

      res.json({
        code: 200,
        message: '获取文件类型选项成功',
        data: fileTypes
      });
    } catch (error) {
      logger.error('获取文件类型选项失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取创建人选项
   */
  async getCreateUsers(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;

      const createUsers = await patternService.getCreateUsers(enterpriseId);

      res.json({
        code: 200,
        message: '获取创建人选项成功',
        data: createUsers
      });
    } catch (error) {
      logger.error('获取创建人选项失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取花样搜索选项
   */
  async getSearchOptions(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;

      const options = await patternService.getSearchOptions(enterpriseId);

      res.json({
        code: 200,
        message: '获取花样搜索选项成功',
        data: options
      });
    } catch (error) {
      logger.error('获取花样搜索选项失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取花样统计信息
   */
  async getPatternStats(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;

      const stats = await patternService.getPatternStats(enterpriseId);

      res.json({
        code: 200,
        message: '获取花样统计信息成功',
        data: stats
      });
    } catch (error) {
      logger.error('获取花样统计信息失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}

export const patternController = new PatternController();
