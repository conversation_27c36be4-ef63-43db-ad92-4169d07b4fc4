import { Router } from 'express';
import { patternController } from './pattern.controller';
import { patternValidationRules } from './pattern.validation';
import { authMiddleware } from '../../shared/middleware/auth.middleware';
import { asyncHandler } from '../../shared/middleware/async.middleware';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Pattern:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 花样ID
 *         enterpriseId:
 *           type: integer
 *           description: 企业ID
 *         code:
 *           type: string
 *           description: 花样编号
 *         name:
 *           type: string
 *           description: 花样名称
 *         remark:
 *           type: string
 *           description: 备注
 *         groupId:
 *           type: integer
 *           description: 花样分组ID
 *         fileType:
 *           type: string
 *           description: 花样类型
 *         stitch:
 *           type: integer
 *           description: 花样针数
 *         jumps:
 *           type: integer
 *           description: 跳针次数
 *         trim:
 *           type: integer
 *           description: 剪线次数
 *         colors:
 *           type: integer
 *           description: 换色数量
 *         baseLine:
 *           type: number
 *           description: 底线长度
 *         surfaceLine:
 *           type: number
 *           description: 面线长度
 *         goldPieceNum:
 *           type: integer
 *           description: 金片数量
 *         minX:
 *           type: number
 *           description: x最小
 *         maxX:
 *           type: number
 *           description: x最大
 *         minY:
 *           type: number
 *           description: y最小
 *         maxY:
 *           type: number
 *           description: y最大
 *         width:
 *           type: number
 *           description: 宽
 *         height:
 *           type: number
 *           description: 高
 *         goldPieceLine:
 *           type: string
 *           description: 金片线
 *         needle:
 *           type: string
 *           description: 针线
 *         mergeGoldPieceLine:
 *           type: string
 *           description: 金线片合并
 *         image:
 *           type: string
 *           description: 图片
 *         createUser:
 *           type: string
 *           description: 创建人
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 */

/**
 * @swagger
 * /api/v1/patterns:
 *   get:
 *     summary: 获取花样列表
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词（花样名称、编号、备注）
 *       - in: query
 *         name: groupId
 *         schema:
 *           type: integer
 *         description: 分组ID
 *       - in: query
 *         name: fileType
 *         schema:
 *           type: string
 *         description: 文件类型
 *       - in: query
 *         name: createUser
 *         schema:
 *           type: string
 *         description: 创建人
 *     responses:
 *       200:
 *         description: 获取花样列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取花样列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     patterns:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Pattern'
 *                     total:
 *                       type: integer
 *                       description: 总数
 *                     page:
 *                       type: integer
 *                       description: 当前页码
 *                     pageSize:
 *                       type: integer
 *                       description: 每页数量
 *                     totalPages:
 *                       type: integer
 *                       description: 总页数
 */
router.get('/', authMiddleware, patternValidationRules.getPatternList, asyncHandler(patternController.getPatternList));

/**
 * @swagger
 * /api/v1/patterns/file-types:
 *   get:
 *     summary: 获取文件类型选项
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取文件类型选项成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取文件类型选项成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       label:
 *                         type: string
 *                       value:
 *                         type: string
 */
router.get('/file-types', authMiddleware, asyncHandler(patternController.getFileTypes));

/**
 * @swagger
 * /api/v1/patterns/create-users:
 *   get:
 *     summary: 获取创建人选项
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取创建人选项成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取创建人选项成功
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       label:
 *                         type: string
 *                       value:
 *                         type: string
 */
router.get('/create-users', authMiddleware, asyncHandler(patternController.getCreateUsers));

/**
 * @swagger
 * /api/v1/patterns/search-options:
 *   get:
 *     summary: 获取花样搜索选项
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取花样搜索选项成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取花样搜索选项成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     groups:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             description: 分组ID
 *                           name:
 *                             type: string
 *                             description: 分组名称
 *                     fileTypes:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           label:
 *                             type: string
 *                             description: 文件类型标签
 *                           value:
 *                             type: string
 *                             description: 文件类型值
 *                     createUsers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           label:
 *                             type: string
 *                             description: 创建人标签
 *                           value:
 *                             type: string
 *                             description: 创建人值
 */
router.get('/search-options', authMiddleware, asyncHandler(patternController.getSearchOptions));

/**
 * @swagger
 * /api/v1/patterns/stats:
 *   get:
 *     summary: 获取花样统计信息
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取花样统计信息成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取花样统计信息成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       description: 总数
 *                     byFileType:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           fileType:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     byGroup:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           groupName:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     byCreateUser:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           createUser:
 *                             type: string
 *                           count:
 *                             type: integer
 */
router.get('/stats', authMiddleware, asyncHandler(patternController.getPatternStats));

/**
 * @swagger
 * /api/v1/patterns/{id}:
 *   get:
 *     summary: 获取花样详情
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 花样ID
 *     responses:
 *       200:
 *         description: 获取花样详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取花样详情成功
 *                 data:
 *                   $ref: '#/components/schemas/Pattern'
 *       404:
 *         description: 花样不存在
 */
router.get('/:id', authMiddleware, patternValidationRules.getPatternById, asyncHandler(patternController.getPatternById));

/**
 * @swagger
 * /api/v1/patterns:
 *   post:
 *     summary: 创建花样
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               code:
 *                 type: string
 *                 description: 花样编号
 *               name:
 *                 type: string
 *                 description: 花样名称
 *               remark:
 *                 type: string
 *                 description: 备注
 *               groupId:
 *                 type: integer
 *                 description: 花样分组ID
 *               fileType:
 *                 type: string
 *                 description: 花样类型
 *               stitch:
 *                 type: integer
 *                 description: 花样针数
 *               jumps:
 *                 type: integer
 *                 description: 跳针次数
 *               trim:
 *                 type: integer
 *                 description: 剪线次数
 *               colors:
 *                 type: integer
 *                 description: 换色数量
 *               baseLine:
 *                 type: number
 *                 description: 底线长度
 *               surfaceLine:
 *                 type: number
 *                 description: 面线长度
 *               goldPieceNum:
 *                 type: integer
 *                 description: 金片数量
 *               minX:
 *                 type: number
 *                 description: x最小
 *               maxX:
 *                 type: number
 *                 description: x最大
 *               minY:
 *                 type: number
 *                 description: y最小
 *               maxY:
 *                 type: number
 *                 description: y最大
 *               width:
 *                 type: number
 *                 description: 宽
 *               height:
 *                 type: number
 *                 description: 高
 *               goldPieceLine:
 *                 type: string
 *                 description: 金片线
 *               needle:
 *                 type: string
 *                 description: 针线
 *               mergeGoldPieceLine:
 *                 type: string
 *                 description: 金线片合并
 *               image:
 *                 type: string
 *                 description: 图片
 *               createUser:
 *                 type: string
 *                 description: 创建人
 *     responses:
 *       201:
 *         description: 创建花样成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 创建花样成功
 *                 data:
 *                   $ref: '#/components/schemas/Pattern'
 *       400:
 *         description: 请求参数错误
 */
router.post('/', authMiddleware, patternValidationRules.createPattern, asyncHandler(patternController.createPattern));

/**
 * @swagger
 * /api/v1/patterns/{id}:
 *   put:
 *     summary: 更新花样
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 花样ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *                 description: 花样编号
 *               name:
 *                 type: string
 *                 description: 花样名称
 *               remark:
 *                 type: string
 *                 description: 备注
 *               groupId:
 *                 type: integer
 *                 description: 花样分组ID
 *               fileType:
 *                 type: string
 *                 description: 花样类型
 *               stitch:
 *                 type: integer
 *                 description: 花样针数
 *               jumps:
 *                 type: integer
 *                 description: 跳针次数
 *               trim:
 *                 type: integer
 *                 description: 剪线次数
 *               colors:
 *                 type: integer
 *                 description: 换色数量
 *               baseLine:
 *                 type: number
 *                 description: 底线长度
 *               surfaceLine:
 *                 type: number
 *                 description: 面线长度
 *               goldPieceNum:
 *                 type: integer
 *                 description: 金片数量
 *               minX:
 *                 type: number
 *                 description: x最小
 *               maxX:
 *                 type: number
 *                 description: x最大
 *               minY:
 *                 type: number
 *                 description: y最小
 *               maxY:
 *                 type: number
 *                 description: y最大
 *               width:
 *                 type: number
 *                 description: 宽
 *               height:
 *                 type: number
 *                 description: 高
 *               goldPieceLine:
 *                 type: string
 *                 description: 金片线
 *               needle:
 *                 type: string
 *                 description: 针线
 *               mergeGoldPieceLine:
 *                 type: string
 *                 description: 金线片合并
 *               image:
 *                 type: string
 *                 description: 图片
 *               createUser:
 *                 type: string
 *                 description: 创建人
 *     responses:
 *       200:
 *         description: 更新花样成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新花样成功
 *                 data:
 *                   $ref: '#/components/schemas/Pattern'
 *       404:
 *         description: 花样不存在
 */
router.put('/:id', authMiddleware, patternValidationRules.updatePattern, asyncHandler(patternController.updatePattern));

/**
 * @swagger
 * /api/v1/patterns/{id}:
 *   delete:
 *     summary: 删除花样
 *     tags: [花样管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 花样ID
 *     responses:
 *       200:
 *         description: 删除花样成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除花样成功
 *       404:
 *         description: 花样不存在
 */
router.delete('/:id', authMiddleware, patternValidationRules.deletePattern, asyncHandler(patternController.deletePattern));

export default router;
