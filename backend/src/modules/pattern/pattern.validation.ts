import { body, param, query } from 'express-validator';

/**
 * 花样验证规则
 */
export const patternValidationRules = {
  /**
   * 获取花样列表验证规则
   */
  getPatternList: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须是1-100之间的整数'),
    query('search')
      .optional()
      .isLength({ max: 100 })
      .withMessage('搜索关键词长度不能超过100个字符'),
    query('groupId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('分组ID必须是大于0的整数'),
    query('fileType')
      .optional()
      .isLength({ max: 20 })
      .withMessage('文件类型长度不能超过20个字符'),
    query('createUser')
      .optional()
      .isLength({ max: 50 })
      .withMessage('创建人长度不能超过50个字符'),
  ],

  /**
   * 获取花样详情验证规则
   */
  getPatternById: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('花样ID必须是大于0的整数'),
  ],

  /**
   * 创建花样验证规则
   */
  createPattern: [
    body('name')
      .notEmpty()
      .withMessage('花样名称不能为空')
      .isLength({ max: 100 })
      .withMessage('花样名称长度不能超过100个字符'),
    body('code')
      .optional()
      .isLength({ max: 50 })
      .withMessage('花样编号长度不能超过50个字符'),
    body('remark')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('备注长度不能超过1000个字符'),
    body('groupId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('分组ID必须是大于0的整数'),
    body('fileType')
      .optional()
      .isLength({ max: 20 })
      .withMessage('文件类型长度不能超过20个字符'),
    body('stitch')
      .optional()
      .isInt({ min: 0 })
      .withMessage('花样针数必须是非负整数'),
    body('jumps')
      .optional()
      .isInt({ min: 0 })
      .withMessage('跳针次数必须是非负整数'),
    body('trim')
      .optional()
      .isInt({ min: 0 })
      .withMessage('剪线次数必须是非负整数'),
    body('colors')
      .optional()
      .isInt({ min: 0 })
      .withMessage('换色数量必须是非负整数'),
    body('baseLine')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('底线长度必须是非负数'),
    body('surfaceLine')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('面线长度必须是非负数'),
    body('goldPieceNum')
      .optional()
      .isInt({ min: 0 })
      .withMessage('金片数量必须是非负整数'),
    body('minX')
      .optional()
      .isFloat()
      .withMessage('x最小值必须是数字'),
    body('maxX')
      .optional()
      .isFloat()
      .withMessage('x最大值必须是数字'),
    body('minY')
      .optional()
      .isFloat()
      .withMessage('y最小值必须是数字'),
    body('maxY')
      .optional()
      .isFloat()
      .withMessage('y最大值必须是数字'),
    body('width')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('宽度必须是非负数'),
    body('height')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('高度必须是非负数'),
    body('goldPieceLine')
      .optional()
      .isLength({ max: 10000 })
      .withMessage('金片线数据长度不能超过10000个字符'),
    body('needle')
      .optional()
      .isLength({ max: 10000 })
      .withMessage('针线数据长度不能超过10000个字符'),
    body('mergeGoldPieceLine')
      .optional()
      .isLength({ max: 10000 })
      .withMessage('金线片合并数据长度不能超过10000个字符'),
    body('image')
      .optional()
      .isLength({ max: 255 })
      .withMessage('图片路径长度不能超过255个字符'),
    body('createUser')
      .optional()
      .isLength({ max: 50 })
      .withMessage('创建人长度不能超过50个字符'),
  ],

  /**
   * 更新花样验证规则
   */
  updatePattern: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('花样ID必须是大于0的整数'),
    body('name')
      .optional()
      .notEmpty()
      .withMessage('花样名称不能为空')
      .isLength({ max: 100 })
      .withMessage('花样名称长度不能超过100个字符'),
    body('code')
      .optional()
      .isLength({ max: 50 })
      .withMessage('花样编号长度不能超过50个字符'),
    body('remark')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('备注长度不能超过1000个字符'),
    body('groupId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('分组ID必须是大于0的整数'),
    body('fileType')
      .optional()
      .isLength({ max: 20 })
      .withMessage('文件类型长度不能超过20个字符'),
    body('stitch')
      .optional()
      .isInt({ min: 0 })
      .withMessage('花样针数必须是非负整数'),
    body('jumps')
      .optional()
      .isInt({ min: 0 })
      .withMessage('跳针次数必须是非负整数'),
    body('trim')
      .optional()
      .isInt({ min: 0 })
      .withMessage('剪线次数必须是非负整数'),
    body('colors')
      .optional()
      .isInt({ min: 0 })
      .withMessage('换色数量必须是非负整数'),
    body('baseLine')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('底线长度必须是非负数'),
    body('surfaceLine')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('面线长度必须是非负数'),
    body('goldPieceNum')
      .optional()
      .isInt({ min: 0 })
      .withMessage('金片数量必须是非负整数'),
    body('minX')
      .optional()
      .isFloat()
      .withMessage('x最小值必须是数字'),
    body('maxX')
      .optional()
      .isFloat()
      .withMessage('x最大值必须是数字'),
    body('minY')
      .optional()
      .isFloat()
      .withMessage('y最小值必须是数字'),
    body('maxY')
      .optional()
      .isFloat()
      .withMessage('y最大值必须是数字'),
    body('width')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('宽度必须是非负数'),
    body('height')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('高度必须是非负数'),
    body('goldPieceLine')
      .optional()
      .isLength({ max: 10000 })
      .withMessage('金片线数据长度不能超过10000个字符'),
    body('needle')
      .optional()
      .isLength({ max: 10000 })
      .withMessage('针线数据长度不能超过10000个字符'),
    body('mergeGoldPieceLine')
      .optional()
      .isLength({ max: 10000 })
      .withMessage('金线片合并数据长度不能超过10000个字符'),
    body('image')
      .optional()
      .isLength({ max: 255 })
      .withMessage('图片路径长度不能超过255个字符'),
    body('createUser')
      .optional()
      .isLength({ max: 50 })
      .withMessage('创建人长度不能超过50个字符'),
  ],

  /**
   * 删除花样验证规则
   */
  deletePattern: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('花样ID必须是大于0的整数'),
  ],
};
