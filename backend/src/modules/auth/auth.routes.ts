/**
 * 认证路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-003 认证系统, 创建认证路由; Principle_Applied: RESTful API设计;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 12:09:12 +08:00; Reason: Task-005 微信路由配置, 添加微信相关API路由; Principle_Applied: RESTful API设计;}}
 */

import { Router } from 'express';
import { AuthController } from './auth.controller';
import { authMiddleware } from '@/shared/middleware/auth.middleware';
import { loginRateLimitMiddleware } from '@/shared/middleware/rate-limit.middleware';

const router = Router();
const authController = new AuthController();

/**
 * @route   POST /auth/login
 * @desc    用户登录
 * @access  Public
 */
router.post(
  '/login',
  loginRateLimitMiddleware, // 登录限流
  authController.loginValidation, // 输入验证
  authController.login
);

/**
 * @route   POST /auth/refresh
 * @desc    刷新访问令牌
 * @access  Public
 */
router.post(
  '/refresh',
  authController.refreshTokenValidation, // 输入验证
  authController.refreshToken
);

/**
 * @route   GET /auth/profile
 * @desc    获取当前用户信息
 * @access  Private
 */
router.get(
  '/profile',
  authMiddleware, // 需要认证
  authController.getProfile
);

/**
 * @route   POST /auth/logout
 * @desc    用户登出
 * @access  Private
 */
router.post(
  '/logout',
  authMiddleware, // 需要认证
  authController.logout
);

/**
 * @route   GET /auth/verify
 * @desc    验证令牌有效性
 * @access  Private
 */
router.get(
  '/verify',
  authMiddleware, // 需要认证
  authController.verifyToken
);

/**
 * @route   PUT /auth/profile
 * @desc    更新个人信息
 * @access  Private
 */
router.put(
  '/profile',
  authMiddleware, // 需要认证
  authController.updateProfileValidation, // 输入验证
  authController.updateProfile
);

/**
 * @route   POST /auth/change-password
 * @desc    修改密码
 * @access  Private
 */
router.post(
  '/change-password',
  authMiddleware, // 需要认证
  authController.changePasswordValidation, // 输入验证
  authController.changePassword
);

// ==================== 微信相关路由 ====================

/**
 * @route   GET /auth/wechat/auth-url
 * @desc    获取微信授权URL
 * @access  Private
 */
router.get(
  '/wechat/auth-url',
  authMiddleware, // 需要认证
  authController.getWechatAuthUrl
);

/**
 * @route   POST /auth/wechat/callback
 * @desc    处理微信授权回调
 * @access  Public
 * @note    此接口为微信服务器回调，不需要认证
 */
router.post(
  '/wechat/callback',
  authController.handleWechatCallback
);

/**
 * @route   POST /auth/wechat/bind
 * @desc    绑定微信账号
 * @access  Private
 */
router.post(
  '/wechat/bind',
  authMiddleware, // 需要认证
  authController.wechatBindValidation, // 输入验证
  authController.bindWechat
);

/**
 * @route   DELETE /auth/wechat/unbind
 * @desc    解绑微信账号
 * @access  Private
 */
router.delete(
  '/wechat/unbind',
  authMiddleware, // 需要认证
  authController.unbindWechat
);

export { router as authRoutes };
