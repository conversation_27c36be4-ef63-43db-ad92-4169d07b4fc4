/**
 * 认证控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-003 认证系统, 创建认证控制器; Principle_Applied: MVC架构模式;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 12:01:25 +08:00; Reason: Task-004 微信控制器开发, 添加微信相关API端点; Principle_Applied: MVC架构模式;}}
 */

import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';
import { AuthService } from './auth.service';
import { WechatService } from '@/modules/wechat/wechat.service';
import { asyncHandler, createApiError } from '@/shared/middleware/error.middleware';
import { logger } from '@/shared/utils/logger';

export class AuthController {
  private authService: AuthService;
  private wechatService: WechatService;

  constructor() {
    this.authService = new AuthService();
    this.wechatService = new WechatService();
  }

  /**
   * 用户登录验证规则
   */
  public loginValidation = [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6, max: 50 })
      .withMessage('密码长度必须在6-50个字符之间'),
  ];

  /**
   * 刷新令牌验证规则
   */
  public refreshTokenValidation = [
    body('refreshToken')
      .notEmpty()
      .withMessage('刷新令牌不能为空'),
  ];

  /**
   * 用户登录
   */
  public login = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createApiError('输入数据验证失败', 400, 'VALIDATION_ERROR', errors.array());
    }

    const { username, password } = req.body;

    try {
      const result = await this.authService.login({ username, password });

      logger.info('用户登录API调用成功', {
        userId: result.user.id,
        username: result.user.username,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '登录成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('用户登录API调用失败', {
        username,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 刷新访问令牌
   */
  public refreshToken = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createApiError('输入数据验证失败', 400, 'VALIDATION_ERROR', errors.array());
    }

    const { refreshToken } = req.body;

    try {
      const result = await this.authService.refreshToken({ refreshToken });

      logger.info('令牌刷新API调用成功', {
        ip: req.ip,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '令牌刷新成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('令牌刷新API调用失败', {
        ip: req.ip,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 获取当前用户信息
   */
  public getProfile = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    try {
      const result = await this.authService.getUserProfile(req.user.userId);

      logger.info('获取用户信息API调用成功', {
        userId: req.user.userId,
        username: req.user.username,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '获取用户信息成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取用户信息API调用失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 用户登出
   */
  public logout = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    try {
      await this.authService.logout(req.user.userId);

      logger.info('用户登出API调用成功', {
        userId: req.user.userId,
        username: req.user.username,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '登出成功',
        data: null,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('用户登出API调用失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 验证令牌有效性
   */
  public verifyToken = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw createApiError('令牌无效', 401, 'INVALID_TOKEN');
    }

    res.status(200).json({
      code: 200,
      message: '令牌有效',
      data: {
        valid: true,
        user: {
          id: req.user.userId,
          username: req.user.username,
          enterpriseId: req.user.enterpriseId,
          roles: req.user.roles,
          permissions: req.user.permissions,
        },
      },
      timestamp: new Date().toISOString(),
    });
  });

  /**
   * 更新个人信息验证规则
   */
  public updateProfileValidation = [
    body('realName')
      .notEmpty()
      .withMessage('真实姓名不能为空')
      .isLength({ min: 2, max: 20 })
      .withMessage('真实姓名长度必须在2-20个字符之间'),

    body('email')
      .optional({ values: 'falsy' })
      .isEmail()
      .withMessage('邮箱格式不正确'),

    body('phone')
      .optional({ values: 'falsy' })
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('手机号格式不正确'),
  ];

  /**
   * 更新个人信息
   */
  public updateProfile = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createApiError('输入数据验证失败', 400, 'VALIDATION_ERROR', errors.array());
    }

    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    const { realName, email, phone } = req.body;

    try {
      const result = await this.authService.updateProfile(req.user.userId, {
        realName,
        email,
        phone,
      });

      logger.info('更新个人信息API调用成功', {
        userId: req.user.userId,
        username: req.user.username,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '个人信息更新成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('更新个人信息API调用失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 修改密码验证规则
   */
  public changePasswordValidation = [
    body('currentPassword')
      .notEmpty()
      .withMessage('当前密码不能为空'),

    body('newPassword')
      .notEmpty()
      .withMessage('新密码不能为空')
      .isLength({ min: 6, max: 50 })
      .withMessage('新密码长度必须在6-50个字符之间'),
  ];

  /**
   * 修改密码
   */
  public changePassword = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createApiError('输入数据验证失败', 400, 'VALIDATION_ERROR', errors.array());
    }

    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    const { currentPassword, newPassword } = req.body;

    try {
      await this.authService.changePassword(req.user.userId, {
        currentPassword,
        newPassword,
      });

      logger.info('修改密码API调用成功', {
        userId: req.user.userId,
        username: req.user.username,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '密码修改成功',
        data: null,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('修改密码API调用失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  // ==================== 微信相关API ====================

  /**
   * 微信绑定验证规则
   */
  public wechatBindValidation = [
    body('code')
      .notEmpty()
      .withMessage('微信授权码不能为空')
      .isLength({ min: 1, max: 200 })
      .withMessage('微信授权码格式不正确'),

    body('state')
      .notEmpty()
      .withMessage('状态参数不能为空')
      .isLength({ min: 1, max: 100 })
      .withMessage('状态参数格式不正确'),
  ];

  /**
   * 获取微信授权URL
   * GET /auth/wechat/auth-url
   */
  public getWechatAuthUrl = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    try {
      const result = await this.wechatService.generateAuthUrl();

      logger.info('获取微信授权URL成功', {
        userId: req.user.userId,
        username: req.user.username,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '获取微信授权URL成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取微信授权URL失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 处理微信授权回调
   * POST /auth/wechat/callback
   * 注意：此接口为公开接口，不需要认证
   */
  public handleWechatCallback = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const { code, state, error: wechatError, error_description } = req.body;

    try {
      // 检查微信是否返回错误
      if (wechatError) {
        throw createApiError(
          `微信授权失败: ${error_description || wechatError}`,
          400,
          'WECHAT_AUTH_ERROR'
        );
      }

      // 验证必需参数
      if (!code || !state) {
        throw createApiError('微信授权参数不完整', 400, 'INVALID_CALLBACK_PARAMS');
      }

      // 处理微信回调获取用户信息
      const userInfo = await this.wechatService.handleCallback(code, state);

      logger.info('微信授权回调处理成功', {
        openid: userInfo.openid,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '微信授权成功',
        data: {
          userInfo,
          // 返回临时标识，前端可用于后续绑定操作
          tempToken: Buffer.from(`${code}:${state}`).toString('base64'),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('微信授权回调处理失败', {
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 绑定微信账号
   * POST /auth/wechat/bind
   */
  public bindWechat = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createApiError('输入数据验证失败', 400, 'VALIDATION_ERROR', errors.array());
    }

    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    const { code, state } = req.body;

    try {
      const result = await this.wechatService.bindWechat({
        code,
        state,
        userId: req.user.userId,
      });

      logger.info('微信账号绑定成功', {
        userId: req.user.userId,
        username: req.user.username,
        openid: result.userInfo?.wechatOpenid,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: result.message,
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('微信账号绑定失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 解绑微信账号
   * DELETE /auth/wechat/unbind
   */
  public unbindWechat = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    try {
      const result = await this.wechatService.unbindWechat(req.user.userId);

      logger.info('微信账号解绑成功', {
        userId: req.user.userId,
        username: req.user.username,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: result.message,
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('微信账号解绑失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });
}
