/**
 * 用户服务层
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 20:53:00 +08:00; Reason: Task-006 用户管理模块, 创建用户服务; Principle_Applied: 业务逻辑分层;}}
 */

import { Op } from 'sequelize';
import { logger } from '@/shared/utils/logger';
import { createApiError } from '@/shared/utils/error';
import {
  User,
  Enterprise,
  Department,
  Role,
  UserRole
} from '@/shared/database/models';
import { DepartmentService } from '@/modules/department/department.service';
import { formatDateTime } from '@/shared/utils/dateTime';

export interface UserListQuery {
  page?: number;
  pageSize?: number;
  keyword?: string;
  departmentId?: number;
  status?: number;
  enterpriseId?: number;
}

export interface CreateUserRequest {
  username: string;
  email?: string;
  phone?: string;
  password: string;
  realName: string;
  departmentId?: number;
  roleIds?: number[];
  status?: number;
}

export interface UpdateUserRequest {
  email?: string;
  phone?: string;
  realName?: string;
  departmentId?: number;
  roleIds?: number[];
  status?: number;
}

export interface UserListResponse {
  users: any[];
  total: number;
  page: number;
  pageSize: number;
}

export class UserService {
  private departmentService = new DepartmentService();

  /**
   * 获取用户列表
   */
  async getUserList(query: UserListQuery, currentUserId: number): Promise<UserListResponse> {
    try {
      const {
        page = 1,
        pageSize = 20,
        keyword,
        departmentId,
        status,
        enterpriseId
      } = query;

      // 获取当前用户信息以确定权限范围
      const currentUser = await User.findByPk(currentUserId, {
        include: [
          {
            model: Role,
            as: 'roles',
            attributes: ['code'],
          },
        ],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');
      
      // 构建查询条件
      const whereConditions: any = {};

      // 权限控制：
      // 1. 超级管理员可以查看指定企业的用户（不包括其他超级管理员）
      // 2. 企业管理员只能查看自己企业的用户（不包括超级管理员）
      if (!isSuperAdmin) {
        // 企业管理员：只能查看自己企业的用户，排除超级管理员
        whereConditions.enterpriseId = currentUser.enterpriseId;
      } else if (enterpriseId) {
        // 超级管理员查看指定企业：只显示该企业的用户，排除超级管理员
        whereConditions.enterpriseId = enterpriseId;
      } else {
        // 超级管理员查看所有企业：显示所有有企业归属的用户，排除超级管理员
        whereConditions.enterpriseId = { [Op.ne]: null };
      }

      // 关键词搜索
      if (keyword) {
        whereConditions[Op.or] = [
          { username: { [Op.iLike]: `%${keyword}%` } },
          { realName: { [Op.iLike]: `%${keyword}%` } },
          { email: { [Op.iLike]: `%${keyword}%` } },
          { phone: { [Op.iLike]: `%${keyword}%` } },
        ];
      }

      // 部门筛选（包含子部门）
      if (departmentId) {
        const departmentIds = await this.departmentService.getDepartmentAndChildrenIds(departmentId);
        whereConditions.departmentId = {
          [Op.in]: departmentIds
        };
      }

      // 状态筛选
      if (status !== undefined) {
        whereConditions.status = status;
      }

      const offset = (page - 1) * pageSize;

      const { rows: users, count: total } = await User.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: Enterprise,
            as: 'enterprise',
            attributes: ['id', 'name', 'code'],
          },
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name'],
          },
          {
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'code'],
            through: { attributes: [] },
          },
        ],
        attributes: { exclude: ['passwordHash'] },
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset,
      });

      logger.info('获取用户列表成功', {
        userId: currentUserId,
        total,
        page,
        pageSize,
        filters: { keyword, departmentId, status, enterpriseId },
      });

      return {
        users: users.map(user => ({
          id: user.id,
          username: user.username,
          email: user.email,
          phone: user.phone,
          realName: user.realName,
          avatarUrl: user.avatarUrl,
          status: user.status,
          lastLoginAt: formatDateTime(user.lastLoginAt),
          createdAt: formatDateTime(user.createdAt),
          updatedAt: formatDateTime(user.updatedAt),
          enterprise: user.enterprise,
          department: user.department,
          roles: user.roles,
        })),
        total,
        page,
        pageSize,
      };
    } catch (error) {
      logger.error('获取用户列表失败', error);
      throw error;
    }
  }

  /**
   * 获取用户详情
   */
  async getUserById(id: number, currentUserId: number): Promise<any> {
    try {
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      const user = await User.findByPk(id, {
        include: [
          {
            model: Enterprise,
            as: 'enterprise',
            attributes: ['id', 'name', 'code'],
          },
          {
            model: Department,
            as: 'department',
            attributes: ['id', 'name'],
          },
          {
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'code'],
            through: { attributes: [] },
          },
        ],
        attributes: { exclude: ['passwordHash'] },
      });

      if (!user) {
        throw createApiError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      // 非超级管理员只能查看同企业用户
      if (!isSuperAdmin && user.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('权限不足', 403, 'PERMISSION_DENIED');
      }

      logger.info('获取用户详情成功', { userId: currentUserId, targetUserId: id });

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        realName: user.realName,
        avatarUrl: user.avatarUrl,
        status: user.status,
        lastLoginAt: formatDateTime(user.lastLoginAt),
        createdAt: formatDateTime(user.createdAt),
        updatedAt: formatDateTime(user.updatedAt),
        enterprise: user.enterprise,
        department: user.department,
        roles: user.roles,
      };
    } catch (error) {
      logger.error('获取用户详情失败', error);
      throw error;
    }
  }

  /**
   * 创建用户
   */
  async createUser(data: CreateUserRequest, currentUserId: number): Promise<any> {
    try {
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      // 检查用户名是否已存在
      const existingUser = await User.findOne({
        where: { username: data.username },
      });

      if (existingUser) {
        throw createApiError('用户名已存在', 400, 'USERNAME_EXISTS');
      }

      // 检查邮箱是否已存在
      if (data.email) {
        const existingEmail = await User.findOne({
          where: { email: data.email },
        });

        if (existingEmail) {
          throw createApiError('邮箱已存在', 400, 'EMAIL_EXISTS');
        }
      }

      // 验证部门是否存在且属于当前企业
      let departmentId = data.departmentId;
      if (departmentId) {
        const department = await Department.findByPk(departmentId);
        if (!department) {
          throw createApiError('部门不存在', 400, 'DEPARTMENT_NOT_FOUND');
        }

        if (!isSuperAdmin && department.enterpriseId !== currentUser.enterpriseId) {
          throw createApiError('不能为其他企业的部门分配用户', 403, 'PERMISSION_DENIED');
        }
      }

      // 确定企业ID
      let targetEnterpriseId = currentUser.enterpriseId;
      if (isSuperAdmin && departmentId) {
        const dept = await Department.findByPk(departmentId);
        if (dept) {
          targetEnterpriseId = dept.enterpriseId;
        }
      }

      // 创建用户
      const userData: any = {
        username: data.username,
        passwordHash: data.password, // 将被自动加密
        realName: data.realName,
        enterpriseId: targetEnterpriseId,
        status: data.status ?? 1,
      };

      if (data.email) userData.email = data.email;
      if (data.phone) userData.phone = data.phone;
      if (departmentId) userData.departmentId = departmentId;

      const user = await User.create(userData);

      // 分配角色
      if (data.roleIds && data.roleIds.length > 0) {
        const roles = await Role.findAll({
          where: {
            id: data.roleIds,
            ...(isSuperAdmin ? {} : { enterpriseId: currentUser.enterpriseId }),
          },
        });

        if (roles.length !== data.roleIds.length) {
          throw createApiError('部分角色不存在或无权限分配', 400, 'INVALID_ROLES');
        }

        await user.setRoles(roles);
      }

      logger.info('创建用户成功', {
        userId: currentUserId,
        newUserId: user.id,
        username: user.username,
      });

      return await this.getUserById(user.id, currentUserId);
    } catch (error) {
      logger.error('创建用户失败', error);
      throw error;
    }
  }

  /**
   * 更新用户
   */
  async updateUser(id: number, data: UpdateUserRequest, currentUserId: number): Promise<any> {
    try {
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      const user = await User.findByPk(id);
      if (!user) {
        throw createApiError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      // 非超级管理员只能修改同企业用户
      if (!isSuperAdmin && user.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('权限不足', 403, 'PERMISSION_DENIED');
      }

      // 检查邮箱是否已被其他用户使用
      if (data.email && data.email !== user.email) {
        const existingEmail = await User.findOne({
          where: { email: data.email, id: { [Op.ne]: id } },
        });

        if (existingEmail) {
          throw createApiError('邮箱已存在', 400, 'EMAIL_EXISTS');
        }
      }

      // 验证部门是否存在且属于当前企业
      if (data.departmentId) {
        const department = await Department.findByPk(data.departmentId);
        if (!department) {
          throw createApiError('部门不存在', 400, 'DEPARTMENT_NOT_FOUND');
        }

        if (!isSuperAdmin && department.enterpriseId !== currentUser.enterpriseId) {
          throw createApiError('不能将用户分配到其他企业的部门', 403, 'PERMISSION_DENIED');
        }
      }

      // 更新用户信息
      const updateData: any = {};
      if (data.email !== undefined) updateData.email = data.email;
      if (data.phone !== undefined) updateData.phone = data.phone;
      if (data.realName !== undefined) updateData.realName = data.realName;
      if (data.departmentId !== undefined) updateData.departmentId = data.departmentId;
      if (data.status !== undefined) updateData.status = data.status;

      if (Object.keys(updateData).length > 0) {
        await user.update(updateData);
      }

      // 更新角色
      if (data.roleIds !== undefined) {
        if (data.roleIds.length > 0) {
          const roles = await Role.findAll({
            where: {
              id: data.roleIds,
              ...(isSuperAdmin ? {} : { enterpriseId: currentUser.enterpriseId }),
            },
          });

          if (roles.length !== data.roleIds.length) {
            throw createApiError('部分角色不存在或无权限分配', 400, 'INVALID_ROLES');
          }

          await user.setRoles(roles);
        } else {
          await user.setRoles([]);
        }
      }

      logger.info('更新用户成功', {
        userId: currentUserId,
        targetUserId: id,
        changes: data,
      });

      return await this.getUserById(id, currentUserId);
    } catch (error) {
      logger.error('更新用户失败', error);
      throw error;
    }
  }

  /**
   * 删除用户
   */
  async deleteUser(id: number, currentUserId: number): Promise<void> {
    try {
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      const user = await User.findByPk(id, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });
      if (!user) {
        throw createApiError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      // 不能删除自己
      if (id === currentUserId) {
        throw createApiError('不能删除自己', 400, 'CANNOT_DELETE_SELF');
      }

      // 检查是否为企业管理员，企业管理员不能被删除
      const isEnterpriseAdmin = user.roles?.some(role => role.code === 'ENTERPRISE_ADMIN');
      if (isEnterpriseAdmin) {
        throw createApiError('不能删除企业管理员', 400, 'CANNOT_DELETE_ENTERPRISE_ADMIN');
      }

      // 检查是否为超级管理员，超级管理员不能被删除
      const isTargetSuperAdmin = user.roles?.some(role => role.code === 'SUPER_ADMIN');
      if (isTargetSuperAdmin) {
        throw createApiError('不能删除超级管理员', 400, 'CANNOT_DELETE_SUPER_ADMIN');
      }

      // 非超级管理员只能删除同企业用户
      if (!isSuperAdmin && user.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('权限不足', 403, 'PERMISSION_DENIED');
      }

      // 删除用户角色关联
      await UserRole.destroy({
        where: { userId: id },
      });

      // 删除用户
      await user.destroy();

      logger.info('删除用户成功', {
        userId: currentUserId,
        deletedUserId: id,
        deletedUsername: user.username,
      });
    } catch (error) {
      logger.error('删除用户失败', error);
      throw error;
    }
  }

  /**
   * 重置用户密码
   */
  async resetPassword(id: number, newPassword: string, currentUserId: number): Promise<void> {
    try {
      const currentUser = await User.findByPk(currentUserId, {
        include: [{ model: Role, as: 'roles', attributes: ['code'] }],
      });

      if (!currentUser) {
        throw createApiError('当前用户不存在', 404, 'USER_NOT_FOUND');
      }

      const isSuperAdmin = currentUser.roles?.some(role => role.code === 'SUPER_ADMIN');

      const user = await User.findByPk(id);
      if (!user) {
        throw createApiError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      // 非超级管理员只能重置同企业用户密码
      if (!isSuperAdmin && user.enterpriseId !== currentUser.enterpriseId) {
        throw createApiError('权限不足', 403, 'PERMISSION_DENIED');
      }

      // 更新密码
      await user.update({
        passwordHash: newPassword, // 将被自动加密
      });

      logger.info('重置用户密码成功', {
        userId: currentUserId,
        targetUserId: id,
        targetUsername: user.username,
      });
    } catch (error) {
      logger.error('重置用户密码失败', error);
      throw error;
    }
  }
}
