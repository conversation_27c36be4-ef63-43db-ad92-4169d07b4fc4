/**
 * 用户控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 20:53:00 +08:00; Reason: Task-006 用户管理模块, 创建用户控制器; Principle_Applied: MVC架构设计;}}
 */

import { Request, Response, NextFunction } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { UserService } from './user.service';
import { logger } from '@/shared/utils/logger';
import { createApiError } from '@/shared/utils/error';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * 获取用户列表验证规则
   */
  public getUserListValidation = [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须是1-100之间的整数'),
    query('keyword')
      .optional()
      .isLength({ max: 50 })
      .withMessage('关键词长度不能超过50个字符'),
    query('departmentId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('部门ID必须是大于0的整数'),
    query('status')
      .optional()
      .isIn(['0', '1'])
      .withMessage('状态值必须是0或1'),
    query('enterpriseId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('企业ID必须是大于0的整数'),
  ];

  /**
   * 获取用户列表
   */
  public getUserList = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const query = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 20,
        keyword: req.query.keyword as string,
        departmentId: req.query.departmentId ? parseInt(req.query.departmentId as string) : undefined,
        status: req.query.status ? parseInt(req.query.status as string) : undefined,
        enterpriseId: req.query.enterpriseId ? parseInt(req.query.enterpriseId as string) : undefined,
      };

      const result = await this.userService.getUserList(query, req.user.userId);

      res.json({
        code: 200,
        message: '获取用户列表成功',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取用户详情验证规则
   */
  public getUserByIdValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('用户ID必须是大于0的整数'),
  ];

  /**
   * 获取用户详情
   */
  public getUserById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      const user = await this.userService.getUserById(id, req.user.userId);

      res.json({
        code: 200,
        message: '获取用户详情成功',
        data: user,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 创建用户验证规则
   */
  public createUserValidation = [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    body('email')
      .optional({ checkFalsy: true })
      .custom((value) => {
        if (!value || value.trim() === '') {
          return true; // 空值或空字符串直接通过
        }
        // 使用内置的邮箱验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          throw new Error('邮箱格式不正确');
        }
        return true;
      }),
    body('phone')
      .optional({ checkFalsy: true })
      .custom((value) => {
        if (!value || value.trim() === '') {
          return true; // 空值或空字符串直接通过
        }
        // 使用中国手机号验证正则
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(value)) {
          throw new Error('手机号格式不正确');
        }
        return true;
      }),
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6, max: 20 })
      .withMessage('密码长度必须在6-20个字符之间'),
    body('realName')
      .notEmpty()
      .withMessage('真实姓名不能为空')
      .isLength({ min: 2, max: 20 })
      .withMessage('真实姓名长度必须在2-20个字符之间'),
    body('departmentId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('部门ID必须是大于0的整数'),
    body('roleIds')
      .optional()
      .isArray()
      .withMessage('角色ID列表必须是数组'),
    body('roleIds.*')
      .optional()
      .isInt({ min: 1 })
      .withMessage('角色ID必须是大于0的整数'),
    body('status')
      .optional()
      .isIn([0, 1])
      .withMessage('状态值必须是0或1'),
  ];

  /**
   * 创建用户
   */
  public createUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const user = await this.userService.createUser(req.body, req.user.userId);

      res.status(201).json({
        code: 201,
        message: '创建用户成功',
        data: user,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 更新用户验证规则
   */
  public updateUserValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('用户ID必须是大于0的整数'),
    body('email')
      .optional({ checkFalsy: true })
      .custom((value) => {
        if (!value || value.trim() === '') {
          return true; // 空值或空字符串直接通过
        }
        // 使用内置的邮箱验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          throw new Error('邮箱格式不正确');
        }
        return true;
      }),
    body('phone')
      .optional({ checkFalsy: true })
      .custom((value) => {
        if (!value || value.trim() === '') {
          return true; // 空值或空字符串直接通过
        }
        // 使用中国手机号验证正则
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(value)) {
          throw new Error('手机号格式不正确');
        }
        return true;
      }),
    body('realName')
      .optional()
      .isLength({ min: 2, max: 20 })
      .withMessage('真实姓名长度必须在2-20个字符之间'),
    body('departmentId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('部门ID必须是大于0的整数'),
    body('roleIds')
      .optional()
      .isArray()
      .withMessage('角色ID列表必须是数组'),
    body('roleIds.*')
      .optional()
      .isInt({ min: 1 })
      .withMessage('角色ID必须是大于0的整数'),
    body('status')
      .optional()
      .isIn([0, 1])
      .withMessage('状态值必须是0或1'),
  ];

  /**
   * 更新用户
   */
  public updateUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      const user = await this.userService.updateUser(id, req.body, req.user.userId);

      res.json({
        code: 200,
        message: '更新用户成功',
        data: user,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 删除用户验证规则
   */
  public deleteUserValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('用户ID必须是大于0的整数'),
  ];

  /**
   * 删除用户
   */
  public deleteUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      await this.userService.deleteUser(id, req.user.userId);

      res.json({
        code: 200,
        message: '删除用户成功',
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 重置密码验证规则
   */
  public resetPasswordValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('用户ID必须是大于0的整数'),
    body('newPassword')
      .notEmpty()
      .withMessage('新密码不能为空')
      .isLength({ min: 6, max: 20 })
      .withMessage('新密码长度必须在6-20个字符之间'),
  ];

  /**
   * 重置用户密码
   */
  public resetPassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const id = parseInt(req.params.id);
      const { newPassword } = req.body;

      await this.userService.resetPassword(id, newPassword, req.user.userId);

      res.json({
        code: 200,
        message: '重置密码成功',
      });
    } catch (error) {
      next(error);
    }
  };
}
