/**
 * 用户路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-28 20:53:00 +08:00; Reason: Task-006 用户管理模块, 创建用户路由; Principle_Applied: RESTful API设计;}}
 */

import { Router } from 'express';
import { UserController } from './user.controller';
import { authMiddleware, permissionMiddleware } from '@/shared/middleware/auth.middleware';

const router = Router();
const userController = new UserController();

/**
 * @route   GET /users
 * @desc    获取用户列表
 * @access  Private (需要 user:view 权限)
 */
router.get(
  '/',
  authMiddleware,
  permissionMiddleware(['user:view']),
  userController.getUserListValidation,
  userController.getUserList
);

/**
 * @route   GET /users/:id
 * @desc    获取用户详情
 * @access  Private (需要 user:view 权限)
 */
router.get(
  '/:id',
  authMiddleware,
  permissionMiddleware(['user:view']),
  userController.getUserByIdValidation,
  userController.getUserById
);

/**
 * @route   POST /users
 * @desc    创建用户
 * @access  Private (需要 user:create 权限)
 */
router.post(
  '/',
  authMiddleware,
  permissionMiddleware(['user:create']),
  userController.createUserValidation,
  userController.createUser
);

/**
 * @route   PUT /users/:id
 * @desc    更新用户
 * @access  Private (需要 user:update 权限)
 */
router.put(
  '/:id',
  authMiddleware,
  permissionMiddleware(['user:update']),
  userController.updateUserValidation,
  userController.updateUser
);

/**
 * @route   DELETE /users/:id
 * @desc    删除用户
 * @access  Private (需要 user:delete 权限)
 */
router.delete(
  '/:id',
  authMiddleware,
  permissionMiddleware(['user:delete']),
  userController.deleteUserValidation,
  userController.deleteUser
);

/**
 * @route   POST /users/:id/reset-password
 * @desc    重置用户密码
 * @access  Private (需要 user:update 权限)
 */
router.post(
  '/:id/reset-password',
  authMiddleware,
  permissionMiddleware(['user:update']),
  userController.resetPasswordValidation,
  userController.resetPassword
);

export { router as userRoutes };
