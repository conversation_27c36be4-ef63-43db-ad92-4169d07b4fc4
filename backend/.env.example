# 应用配置
NODE_ENV=development
PORT=3002
API_PREFIX=/api/v1

# 数据库配置
DB_NAME=embroidery_management
DB_USER=admin
DB_PASSWORD="123456"
DB_HOST=localhost
DB_PORT=5432
DB_TABLE_PREFIX=em_
# 数据库连接池配置
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRES_IN=7d

# 加密配置
BCRYPT_ROUNDS=12

# 文件上传配置
UPLOAD_PATH=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=logs

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS配置
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true

# 安全配置
HELMET_ENABLED=true
COMPRESSION_ENABLED=true

# 微信配置
WECHAT_APP_ID=your_wechat_app_id_here
WECHAT_APP_SECRET=your_wechat_app_secret_here
WECHAT_REDIRECT_URI=http://localhost:3002/api/v1/auth/wechat/callback


